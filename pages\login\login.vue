<template>
  <view class="container">
    <!-- 顶部Logo -->
    <view class="logo-container">
      <image src="../../static/ddy.jpeg" class="logo"></image>
      <text class="app-name">登录助手</text>
      <text class="app-desc">安全便捷的登录体验</text>
    </view>

    <!-- 登录表单 -->
    <view class="form-container">
      <view class="input-group">
        <view class="input-icon">
          <text class="fa fa-user"></text>
        </view>
        <input 
          type="text" 
          class="input-field" 
          placeholder="请输入账号" 
          v-model="username"
          placeholder-class="placeholder-style"
        />
      </view>
      
      <view class="input-group">
        <view class="input-icon">
          <text class="fa fa-lock"></text>
        </view>
        <input 
          type="password" 
          class="input-field" 
          placeholder="请输入密码" 
          v-model="password"
          placeholder-class="placeholder-style"
        />
      </view>
      
      <view class="form-actions">
        <checkbox-group class="remember-me">
          <label>
            <checkbox value="remember" checked color="#6B7280" />
            <text>记住密码</text>
          </label>
        </checkbox-group>
        <text class="forgot-password">忘记密码?</text>
      </view>
      
      <button class="login-btn" @click="handleLogin" :disabled="isLoading">
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
      
      <view class="register-container">
        <text>还没有账号?</text>
        <navigator url="/pages/register/register" class="register-link">立即注册</navigator>
      </view>
      
      <view class="tips" v-if="showTips">
        <text class="tips-text">{{ tipsMessage }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      username: '123',
      password: '123',
      isLoading: false,
      showTips: false,
      tipsMessage: ''
    };
  },
  methods: {
    handleLogin() {
      this.showTips = false;
      
      // 表单验证
      if (!this.username) {
        this.showTips = true;
        this.tipsMessage = '请输入账号';
        return;
      }
      
      if (!this.password) {
        this.showTips = true;
        this.tipsMessage = '请输入密码';
        return;
      }
      
      // 模拟登录验证
      if (this.username === '123' && this.password === '123') {
        this.isLoading = true;
        
        uni.showLoading({
          title: '登录中...',
          mask: true
        });
        
        // 模拟登录请求
        setTimeout(() => {
          this.isLoading = false;
          uni.hideLoading();
          
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          // 存储登录状态
          uni.setStorageSync('isLoggedIn', true);
          
          // 跳转到首页
          uni.redirectTo({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        this.showTips = true;
        this.tipsMessage = '账号或密码错误';
      }
    }
  }
}
</script>

<style lang="scss">
.container {
  padding: 120rpx 60rpx 0;
  background: linear-gradient(135deg, #E6F7FF 0%, #F3F3FF 100%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(5px); /* 背景模糊效果 */
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
  
  .logo {
    width: 220rpx;
    height: 220rpx;
    border-radius: 24rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease; /* 过渡效果 */
    
    &:hover {
      transform: scale(1.1); /* 悬停放大效果 */
    }
  }
  
  .app-name {
    font-size: 52rpx;
    font-weight: bold;
    color: #4B5563;
    text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1); /* 文字阴影 */
  }
  
  .app-desc {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #6B7280;
  }
}

.form-container {
  background-color: rgba(255, 255, 255, 0.8); /* 半透明背景 */
  border-radius: 30rpx;
  padding: 60rpx 50rpx;
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 80rpx;
  transition: transform 0.3s ease; /* 过渡效果 */
  
  &:hover {
    transform: translateY(-10rpx); /* 悬停上移效果 */
  }
  
  .input-group {
    display: flex;
    align-items: center;
    padding: 25rpx 0;
    border-bottom: 1rpx solid #F3F4F6;
    
    .input-icon {
      width: 60rpx;
      text-align: center;
      color: #9CA3AF;
      font-size: 36rpx;
      margin-right: 20rpx;
    }
    
    .input-field {
      flex: 1;
      font-size: 32rpx;
      height: 60rpx;
      color: #374151;
      border: none;
      background-color: transparent;
      outline: none;
      
      &::placeholder {
        color: #D1D5DB;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;
    
    .remember-me {
      display: flex;
      align-items: center;
      
      checkbox {
        transform: scale(0.8);
      }
      
      text {
        font-size: 28rpx;
        color: #6B7280;
        margin-left: 10rpx;
      }
    }
    
    .forgot-password {
      font-size: 28rpx;
      color: #4F46E5;
      text-decoration: underline; /* 下划线效果 */
      
      &:hover {
        color: #3730a3; /* 悬停颜色变化 */
      }
    }
  }
  
  .login-btn {
    margin-top: 60rpx;
    height: 96rpx;
    line-height: 96rpx;
    background: linear-gradient(90deg, #4F46E5 0%, #6366F1 100%);
    color: #FFFFFF;
    font-size: 36rpx;
    border-radius: 48rpx;
    font-weight: 500;
    box-shadow: 0 10rpx 20rpx rgba(79, 70, 229, 0.2);
    transition: all 0.3s;
    
    &:hover {
      background: linear-gradient(90deg, #3730a3 0%, #4338ca 100%); /* 悬停背景渐变 */
      transform: scale(1.05); /* 悬停放大效果 */
    }
    
    &[disabled] {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  
  .register-container {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
    font-size: 28rpx;
    
    text {
      color: #6B7280;
    }
    
    .register-link {
      color: #4F46E5;
      margin-left: 10rpx;
      text-decoration: underline; /* 下划线效果 */
      
      &:hover {
        color: #3730a3; /* 悬停颜色变化 */
      }
    }
  }
  
  .tips {
    margin-top: 30rpx;
    text-align: center;
  }
  
  .tips-text {
    color: #EF4444;
    font-size: 28rpx;
  }
}

.placeholder-style {
  color: #D1D5DB;
  font-size: 32rpx;
}
</style>    