<view class="follow-page"><view class="page-header"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn" bindtap="__e"><text class="fa fa-arrow-left"></text></view><view class="title">我的关注</view><view class="right-placeholder"></view></view><view class="search-bar"><view class="search-input"><text class="fa fa-search"></text><input placeholder="搜索关注" disabled="{{true}}"/></view></view><view class="follow-list"><block wx:for="{{followList}}" wx:for-item="person" wx:for-index="index"><view class="follow-item"><view data-event-opts="{{[['tap',[['goToStarDetail',[person.id||index]]]]]}}" class="avatar-wrap" bindtap="__e"><image class="avatar-img" src="{{person.avatar}}" mode="widthFix"></image></view><view class="follow-info"><view class="name">{{person.name}}</view><view class="desc">{{person.desc}}</view></view><view data-event-opts="{{[['tap',[['toggleFollow',[index]]]]]}}" class="{{['follow-btn',(person.isFollowing)?'active':'']}}" bindtap="__e"><text class="btn-text">{{person.isFollowing?'已关注':'关注'}}</text><text class="{{['btn-icon','fa',person.isFollowing?'fa-check':'fa-plus']}}"></text></view></view></block></view></view>