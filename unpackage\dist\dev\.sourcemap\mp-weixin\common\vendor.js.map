{"version": 3, "sources": ["webpack:///./node_modules/@dcloudio/uni-mp-weixin/dist/index.js?543d", "webpack:///./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js?f0c5", null, "uni-app:///common/config.js", "webpack:///./node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js?37dc", "webpack:///./node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js?66fd", "webpack:///./node_modules/@babel/runtime/regenerator/index.js?a34a", "webpack:///./node_modules/regenerator-runtime/runtime-module.js?bbdd", "webpack:///./node_modules/regenerator-runtime/runtime.js?96cf"], "names": ["realAtob", "b64", "b64re", "atob", "str", "String", "replace", "test", "Error", "slice", "length", "bitmap", "result", "r1", "r2", "i", "indexOf", "char<PERSON>t", "fromCharCode", "b64DecodeUnicode", "decodeURIComponent", "split", "map", "c", "charCodeAt", "toString", "join", "getCurrentUserInfo", "token", "wx", "getStorageSync", "tokenArr", "uid", "role", "permission", "tokenExpired", "userInfo", "JSON", "parse", "error", "message", "exp", "iat", "uniIdMixin", "<PERSON><PERSON>", "prototype", "uniIDHasRole", "roleId", "uniIDHasPermission", "permissionId", "uniIDTokenValid", "Date", "now", "_toString", "Object", "hasOwnProperty", "isFn", "fn", "isStr", "isPlainObject", "obj", "call", "hasOwn", "key", "noop", "cached", "cache", "create", "cachedFn", "hit", "camelizeRE", "camelize", "_", "toUpperCase", "sortObject", "sortObj", "keys", "sort", "for<PERSON>ach", "HOOKS", "globalInterceptors", "scopedInterceptors", "mergeHook", "parentVal", "childVal", "res", "concat", "Array", "isArray", "dedupe<PERSON><PERSON>s", "hooks", "push", "removeH<PERSON>", "hook", "index", "splice", "mergeInterceptorHook", "interceptor", "option", "removeInterceptorHook", "addInterceptor", "method", "removeInterceptor", "wrapperHook", "data", "isPromise", "then", "queue", "promise", "Promise", "resolve", "callback", "wrapperOptions", "options", "name", "oldCallback", "callbackInterceptor", "wrapperReturnValue", "returnValue", "returnValueHooks", "getApiInterceptorHooks", "scopedInterceptor", "invokeApi", "api", "params", "invoke", "promiseInterceptor", "reject", "SYNC_API_RE", "CONTEXT_API_RE", "CONTEXT_API_RE_EXC", "ASYNC_API", "CALLBACK_API_RE", "isContextApi", "isSyncApi", "isCallbackApi", "handlePromise", "catch", "err", "shouldPromise", "finally", "constructor", "value", "reason", "promisify", "promiseApi", "success", "fail", "complete", "assign", "EPS", "BASE_DEVICE_WIDTH", "isIOS", "deviceWidth", "deviceDPR", "checkDeviceWidth", "getSystemInfoSync", "platform", "pixelRatio", "windowWidth", "upx2px", "number", "newDeviceWidth", "Number", "Math", "floor", "LOCALE_ZH_HANS", "LOCALE_ZH_HANT", "LOCALE_EN", "LOCALE_FR", "LOCALE_ES", "messages", "locale", "normalizeLocale", "language", "initI18nMessages", "isEnableLocale", "localeKeys", "__uniConfig", "locales", "curMessages", "userMessages", "i18n", "t", "i18nMixin", "mixin", "beforeCreate", "unwatch", "watchLocale", "$forceUpdate", "$once", "methods", "$$t", "values", "setLocale", "getLocale", "initAppLocale", "appVm", "state", "observable", "localeWatchers", "$watchLocale", "defineProperty", "get", "set", "v", "watch", "include", "parts", "find", "part", "startsWith", "trim", "toLowerCase", "lang", "getLocale$1", "app", "getApp", "allowDefault", "$vm", "$locale", "setLocale$1", "oldLocale", "onLocaleChangeCallbacks", "onLocaleChange", "global", "interceptors", "baseApi", "freeze", "__proto__", "findExistsPageIndex", "url", "pages", "getCurrentPages", "len", "page", "$page", "fullPath", "redirectTo", "fromArgs", "exists", "delta", "args", "existsPageIndex", "previewImage", "currentIndex", "parseInt", "current", "isNaN", "urls", "filter", "item", "indicator", "loop", "UUID_KEY", "deviceId", "useDeviceId", "random", "setStorage", "addSafeAreaInsets", "safeArea", "safeAreaInsets", "top", "left", "right", "bottom", "screenHeight", "populateParameters", "brand", "model", "system", "theme", "version", "fontSizeSetting", "SDKVersion", "deviceOrientation", "osName", "osVersion", "hostVersion", "deviceType", "getGetDeviceType", "deviceBrand", "getDevice<PERSON>rand", "_hostName", "getHostName", "_deviceOrientation", "_devicePixelRatio", "_SDKVersion", "hostLanguage", "parameters", "appId", "process", "appName", "appVersion", "appVersionCode", "appLanguage", "getAppLanguage", "uniCompileVersion", "uniRuntimeVersion", "uniPlatform", "deviceModel", "devicePixelRatio", "toLocaleLowerCase", "hostTheme", "hostName", "hostSDKVersion", "hostFontSizeSetting", "windowTop", "windowBottom", "osLanguage", "undefined", "osTheme", "ua", "hostPackageName", "browserName", "browserVersion", "deviceTypeMaps", "ipad", "windows", "mac", "deviceTypeMapsKeys", "_model", "_m", "defaultLanguage", "_platform", "environment", "host", "env", "getSystemInfo", "showActionSheet", "alertText", "title", "getAppBaseInfo", "getDeviceInfo", "getWindowInfo", "getAppAuthorizeSetting", "locationReducedAccuracy", "locationAccuracy", "protocols", "todos", "canIUses", "CALLBACKS", "processCallback", "methodName", "processReturnValue", "processArgs", "argsOption", "keepFromArgs", "<PERSON><PERSON><PERSON><PERSON>", "keyOption", "console", "warn", "keepReturnValue", "wrapper", "protocol", "arg1", "arg2", "apply", "todo<PERSON><PERSON>", "TODOS", "createTodoApi", "todoApi", "errMsg", "providers", "o<PERSON>h", "share", "payment", "get<PERSON><PERSON><PERSON>", "service", "provider", "extraApi", "getEmitter", "Emitter", "getUniEmitter", "ctx", "$on", "arguments", "$off", "$emit", "eventApi", "tryCatch", "e", "getApiCallbacks", "apiCallbacks", "param", "cid", "cidErrMsg", "enabled", "normalizePushMessage", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "invokeGetPushCidCallbacks", "onPushMessageCallbacks", "stopped", "getPushCidCallbacks", "getPushClientId", "hasSuccess", "hasFail", "hasComplete", "onPushMessage", "offPushMessage", "MPPage", "Page", "MPComponent", "Component", "customizeRE", "customize", "initTriggerEvent", "mpInstance", "oldTriggerEvent", "triggerEvent", "newTriggerEvent", "event", "_triggerEvent", "initHook", "isComponent", "oldHook", "__$wrappered", "after", "PAGE_EVENT_HOOKS", "initMocks", "vm", "mocks", "$mp", "mpType", "mock", "hasH<PERSON>", "vueOptions", "default", "extendOptions", "super", "mixins", "initHooks", "mpOptions", "__call_hook", "initVueComponent", "VueComponent", "extend", "initSlots", "vueSlots", "$slots", "slotName", "$scopedSlots", "initVueIds", "vueIds", "_$vueId", "_$vuePid", "initData", "context", "VUE_APP_DEBUG", "stringify", "__lifecycle_hooks__", "PROP_TYPES", "Boolean", "createObserver", "observer", "newVal", "oldVal", "initBehaviors", "init<PERSON>eh<PERSON>or", "vueBehaviors", "behaviors", "vueExtends", "extends", "vueMixins", "vueProps", "props", "behavior", "properties", "initProperties", "vueMixin", "parsePropType", "defaultValue", "file", "is<PERSON>eh<PERSON>or", "vueId", "virtualHost", "virtualHostStyle", "virtualHostClass", "scopedSlotsCompiler", "setData", "opts", "wrapper$1", "mp", "stopPropagation", "preventDefault", "target", "detail", "markerId", "getExtraValue", "dataPathsArray", "dataPathArray", "dataPath", "prop<PERSON>ath", "valuePath", "vFor", "isInteger", "substr", "__get_value", "vForItem", "vForKey", "processEventExtra", "extra", "extraObj", "__args__", "getObjByArray", "arr", "element", "processEventArgs", "isCustom", "isCustomMPEvent", "currentTarget", "dataset", "comType", "ret", "arg", "ONCE", "CUSTOM", "isMatchEventType", "eventType", "optType", "getContextVm", "$parent", "$options", "generic", "$scope", "handleEvent", "eventOpts", "eventOpt", "eventsArray", "isOnce", "eventArray", "handlerCtx", "handler", "path", "route", "is", "once", "eventChannels", "eventChannelStack", "getEventChannel", "id", "eventChannel", "shift", "initEventChannel", "getOpenerEventChannel", "callHook", "__id__", "__eventChannel__", "initScopedSlotsParams", "center", "parents", "$hasScopedSlotsParams", "has", "$getScopedSlotsParams", "object", "$setScopedSlotsParams", "propsData", "destroyed", "parseBaseApp", "initRefs", "store", "$store", "mpHost", "$i18n", "_i18n", "appOptions", "onLaunch", "canIUse", "globalData", "_isMounted", "findVmByVueId", "vuePid", "$children", "childVm", "parentVm", "Behavior", "isPage", "initRelation", "selectAllComponents", "selector", "$refs", "components", "component", "ref", "vueGeneric", "scopedComponent", "forComponents", "handleLink", "parent", "parseApp", "createApp", "App", "encodeReserveRE", "encodeReserveReplacer", "commaRE", "encode", "encodeURIComponent", "stringifyQuery", "encodeStr", "val", "val2", "x", "parseBaseComponent", "vueComponentOptions", "multipleSlots", "addGlobalClass", "componentOptions", "__file", "lifetimes", "attached", "$mount", "ready", "detached", "$destroy", "pageLifetimes", "show", "hide", "resize", "size", "__l", "__e", "externalClasses", "wxsCallMethods", "callMethod", "parseComponent", "hooks$1", "parseBasePage", "vuePageOptions", "pageOptions", "onLoad", "query", "copyQuery", "parsePage", "createPage", "createComponent", "createSubpackageApp", "onShow", "onAppShow", "onHide", "onAppHide", "getLaunchOptionsSync", "createPlugin", "canIUseApi", "apiName", "uni", "Proxy", "uni$1", "module", "exports", "isObject", "defaultDelimiters", "BaseFormatter", "_caches", "delimiters", "tokens", "compile", "RE_TOKEN_LIST_VALUE", "RE_TOKEN_NAMED_VALUE", "format", "startDelimiter", "endDelimiter", "position", "text", "char", "sub", "isClosed", "compiled", "mode", "defaultFormatter", "I18n", "fallback<PERSON><PERSON><PERSON>", "watcher", "formater", "watchers", "override", "interpolate", "watchAppLocale", "newLocale", "$watch", "getDefaultLocale", "initVueI18n", "isWatchedAppLocale", "f", "add", "isString", "hasI18nJson", "jsonObj", "walkJsonObj", "isI18nStr", "parseI18nJson", "compileStr", "compileI18nJsonStr", "jsonStr", "localeValues", "unshift", "compileJsonObj", "compileValue", "valueLocales", "localValue", "walk", "resolveLocale", "resolveLocaleChain", "chain", "pop", "require"], "mappings": ";;;;;;;;;;mUAAA;AACA,qE;;AAEA,IAAIA,QAAJ;;AAEA,IAAMC,GAAG,GAAG,mEAAZ;AACA,IAAMC,KAAK,GAAG,sEAAd;;AAEA,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;AAC9BH,UAAQ,GAAG,kBAAUI,GAAV,EAAe;AACxBA,OAAG,GAAGC,MAAM,CAACD,GAAD,CAAN,CAAYE,OAAZ,CAAoB,eAApB,EAAqC,EAArC,CAAN;AACA,QAAI,CAACJ,KAAK,CAACK,IAAN,CAAWH,GAAX,CAAL,EAAsB,CAAE,MAAM,IAAII,KAAJ,CAAU,0FAAV,CAAN,CAA6G;;AAErI;AACAJ,OAAG,IAAI,KAAKK,KAAL,CAAW,KAAKL,GAAG,CAACM,MAAJ,GAAa,CAAlB,CAAX,CAAP;AACA,QAAIC,MAAJ,CAAY,IAAIC,MAAM,GAAG,EAAb,CAAiB,IAAIC,EAAJ,CAAQ,IAAIC,EAAJ,CAAQ,IAAIC,CAAC,GAAG,CAAR;AAC7C,WAAOA,CAAC,GAAGX,GAAG,CAACM,MAAf,GAAwB;AACtBC,YAAM,GAAGV,GAAG,CAACe,OAAJ,CAAYZ,GAAG,CAACa,MAAJ,CAAWF,CAAC,EAAZ,CAAZ,KAAgC,EAAhC,GAAqCd,GAAG,CAACe,OAAJ,CAAYZ,GAAG,CAACa,MAAJ,CAAWF,CAAC,EAAZ,CAAZ,KAAgC,EAArE;AACK,OAACF,EAAE,GAAGZ,GAAG,CAACe,OAAJ,CAAYZ,GAAG,CAACa,MAAJ,CAAWF,CAAC,EAAZ,CAAZ,CAAN,KAAuC,CAD5C,IACiDD,EAAE,GAAGb,GAAG,CAACe,OAAJ,CAAYZ,GAAG,CAACa,MAAJ,CAAWF,CAAC,EAAZ,CAAZ,CADtD,CAAT;;AAGAH,YAAM,IAAIC,EAAE,KAAK,EAAP,GAAYR,MAAM,CAACa,YAAP,CAAoBP,MAAM,IAAI,EAAV,GAAe,GAAnC,CAAZ;AACNG,QAAE,KAAK,EAAP,GAAYT,MAAM,CAACa,YAAP,CAAoBP,MAAM,IAAI,EAAV,GAAe,GAAnC,EAAwCA,MAAM,IAAI,CAAV,GAAc,GAAtD,CAAZ;AACEN,YAAM,CAACa,YAAP,CAAoBP,MAAM,IAAI,EAAV,GAAe,GAAnC,EAAwCA,MAAM,IAAI,CAAV,GAAc,GAAtD,EAA2DA,MAAM,GAAG,GAApE,CAFN;AAGD;AACD,WAAOC,MAAP;AACD,GAhBD;AAiBD,CAlBD,MAkBO;AACL;AACAZ,UAAQ,GAAGG,IAAX;AACD;;AAED,SAASgB,gBAAT,CAA2Bf,GAA3B,EAAgC;AAC9B,SAAOgB,kBAAkB,CAACpB,QAAQ,CAACI,GAAD,CAAR,CAAciB,KAAd,CAAoB,EAApB,EAAwBC,GAAxB,CAA4B,UAAUC,CAAV,EAAa;AACjE,WAAO,MAAM,CAAC,OAAOA,CAAC,CAACC,UAAF,CAAa,CAAb,EAAgBC,QAAhB,CAAyB,EAAzB,CAAR,EAAsChB,KAAtC,CAA4C,CAAC,CAA7C,CAAb;AACD,GAFyB,EAEvBiB,IAFuB,CAElB,EAFkB,CAAD,CAAzB;AAGD;;AAED,SAASC,kBAAT,GAA+B;AAC7B,MAAMC,KAAK,GAAKC,EAAF,CAAMC,cAAN,CAAqB,cAArB,KAAwC,EAAtD;AACA,MAAMC,QAAQ,GAAGH,KAAK,CAACP,KAAN,CAAY,GAAZ,CAAjB;AACA,MAAI,CAACO,KAAD,IAAUG,QAAQ,CAACrB,MAAT,KAAoB,CAAlC,EAAqC;AACnC,WAAO;AACLsB,SAAG,EAAE,IADA;AAELC,UAAI,EAAE,EAFD;AAGLC,gBAAU,EAAE,EAHP;AAILC,kBAAY,EAAE,CAJT,EAAP;;AAMD;AACD,MAAIC,QAAJ;AACA,MAAI;AACFA,YAAQ,GAAGC,IAAI,CAACC,KAAL,CAAWnB,gBAAgB,CAACY,QAAQ,CAAC,CAAD,CAAT,CAA3B,CAAX;AACD,GAFD,CAEE,OAAOQ,KAAP,EAAc;AACd,UAAM,IAAI/B,KAAJ,CAAU,wBAAwB+B,KAAK,CAACC,OAAxC,CAAN;AACD;AACDJ,UAAQ,CAACD,YAAT,GAAwBC,QAAQ,CAACK,GAAT,GAAe,IAAvC;AACA,SAAOL,QAAQ,CAACK,GAAhB;AACA,SAAOL,QAAQ,CAACM,GAAhB;AACA,SAAON,QAAP;AACD;;AAED,SAASO,UAAT,CAAqBC,GAArB,EAA0B;AACxBA,KAAG,CAACC,SAAJ,CAAcC,YAAd,GAA6B,UAAUC,MAAV,EAAkB;;;AAGzCpB,sBAAkB,EAHuB,CAE3CM,IAF2C,uBAE3CA,IAF2C;AAI7C,WAAOA,IAAI,CAACjB,OAAL,CAAa+B,MAAb,IAAuB,CAAC,CAA/B;AACD,GALD;AAMAH,KAAG,CAACC,SAAJ,CAAcG,kBAAd,GAAmC,UAAUC,YAAV,EAAwB;;;AAGrDtB,sBAAkB,EAHmC,CAEvDO,UAFuD,wBAEvDA,UAFuD;AAIzD,WAAO,KAAKY,YAAL,CAAkB,OAAlB,KAA8BZ,UAAU,CAAClB,OAAX,CAAmBiC,YAAnB,IAAmC,CAAC,CAAzE;AACD,GALD;AAMAL,KAAG,CAACC,SAAJ,CAAcK,eAAd,GAAgC,YAAY;;;AAGtCvB,sBAAkB,EAHoB,CAExCQ,YAFwC,wBAExCA,YAFwC;AAI1C,WAAOA,YAAY,GAAGgB,IAAI,CAACC,GAAL,EAAtB;AACD,GALD;AAMD;;AAED,IAAMC,SAAS,GAAGC,MAAM,CAACT,SAAP,CAAiBpB,QAAnC;AACA,IAAM8B,cAAc,GAAGD,MAAM,CAACT,SAAP,CAAiBU,cAAxC;;AAEA,SAASC,IAAT,CAAeC,EAAf,EAAmB;AACjB,SAAO,OAAOA,EAAP,KAAc,UAArB;AACD;;AAED,SAASC,KAAT,CAAgBtD,GAAhB,EAAqB;AACnB,SAAO,OAAOA,GAAP,KAAe,QAAtB;AACD;;AAED,SAASuD,aAAT,CAAwBC,GAAxB,EAA6B;AAC3B,SAAOP,SAAS,CAACQ,IAAV,CAAeD,GAAf,MAAwB,iBAA/B;AACD;;AAED,SAASE,MAAT,CAAiBF,GAAjB,EAAsBG,GAAtB,EAA2B;AACzB,SAAOR,cAAc,CAACM,IAAf,CAAoBD,GAApB,EAAyBG,GAAzB,CAAP;AACD;;AAED,SAASC,IAAT,GAAiB,CAAG;;AAEpB;;;AAGA,SAASC,MAAT,CAAiBR,EAAjB,EAAqB;AACnB,MAAMS,KAAK,GAAGZ,MAAM,CAACa,MAAP,CAAc,IAAd,CAAd;AACA,SAAO,SAASC,QAAT,CAAmBhE,GAAnB,EAAwB;AAC7B,QAAMiE,GAAG,GAAGH,KAAK,CAAC9D,GAAD,CAAjB;AACA,WAAOiE,GAAG,KAAKH,KAAK,CAAC9D,GAAD,CAAL,GAAaqD,EAAE,CAACrD,GAAD,CAApB,CAAV;AACD,GAHD;AAID;;AAED;;;AAGA,IAAMkE,UAAU,GAAG,QAAnB;AACA,IAAMC,QAAQ,GAAGN,MAAM,CAAC,UAAC7D,GAAD,EAAS;AAC/B,SAAOA,GAAG,CAACE,OAAJ,CAAYgE,UAAZ,EAAwB,UAACE,CAAD,EAAIjD,CAAJ,UAAUA,CAAC,GAAGA,CAAC,CAACkD,WAAF,EAAH,GAAqB,EAAhC,EAAxB,CAAP;AACD,CAFsB,CAAvB;;AAIA,SAASC,UAAT,CAAqBd,GAArB,EAA0B;AACxB,MAAMe,OAAO,GAAG,EAAhB;AACA,MAAIhB,aAAa,CAACC,GAAD,CAAjB,EAAwB;AACtBN,UAAM,CAACsB,IAAP,CAAYhB,GAAZ,EAAiBiB,IAAjB,GAAwBC,OAAxB,CAAgC,UAAAf,GAAG,EAAI;AACrCY,aAAO,CAACZ,GAAD,CAAP,GAAeH,GAAG,CAACG,GAAD,CAAlB;AACD,KAFD;AAGD;AACD,SAAO,CAACT,MAAM,CAACsB,IAAP,CAAYD,OAAZ,CAAD,GAAwBf,GAAxB,GAA8Be,OAArC;AACD;;AAED,IAAMI,KAAK,GAAG;AACZ,QADY;AAEZ,SAFY;AAGZ,MAHY;AAIZ,UAJY;AAKZ,aALY,CAAd;;;AAQA,IAAMC,kBAAkB,GAAG,EAA3B;AACA,IAAMC,kBAAkB,GAAG,EAA3B;;AAEA,SAASC,SAAT,CAAoBC,SAApB,EAA+BC,QAA/B,EAAyC;AACvC,MAAMC,GAAG,GAAGD,QAAQ;AAChBD,WAAS;AACPA,WAAS,CAACG,MAAV,CAAiBF,QAAjB,CADO;AAEPG,OAAK,CAACC,OAAN,CAAcJ,QAAd;AACEA,UADF,GACa,CAACA,QAAD,CAJC;AAKhBD,WALJ;AAMA,SAAOE,GAAG;AACNI,aAAW,CAACJ,GAAD,CADL;AAENA,KAFJ;AAGD;;AAED,SAASI,WAAT,CAAsBC,KAAtB,EAA6B;AAC3B,MAAML,GAAG,GAAG,EAAZ;AACA,OAAK,IAAItE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2E,KAAK,CAAChF,MAA1B,EAAkCK,CAAC,EAAnC,EAAuC;AACrC,QAAIsE,GAAG,CAACrE,OAAJ,CAAY0E,KAAK,CAAC3E,CAAD,CAAjB,MAA0B,CAAC,CAA/B,EAAkC;AAChCsE,SAAG,CAACM,IAAJ,CAASD,KAAK,CAAC3E,CAAD,CAAd;AACD;AACF;AACD,SAAOsE,GAAP;AACD;;AAED,SAASO,UAAT,CAAqBF,KAArB,EAA4BG,IAA5B,EAAkC;AAChC,MAAMC,KAAK,GAAGJ,KAAK,CAAC1E,OAAN,CAAc6E,IAAd,CAAd;AACA,MAAIC,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChBJ,SAAK,CAACK,MAAN,CAAaD,KAAb,EAAoB,CAApB;AACD;AACF;;AAED,SAASE,oBAAT,CAA+BC,WAA/B,EAA4CC,MAA5C,EAAoD;AAClD5C,QAAM,CAACsB,IAAP,CAAYsB,MAAZ,EAAoBpB,OAApB,CAA4B,UAAAe,IAAI,EAAI;AAClC,QAAId,KAAK,CAAC/D,OAAN,CAAc6E,IAAd,MAAwB,CAAC,CAAzB,IAA8BrC,IAAI,CAAC0C,MAAM,CAACL,IAAD,CAAP,CAAtC,EAAsD;AACpDI,iBAAW,CAACJ,IAAD,CAAX,GAAoBX,SAAS,CAACe,WAAW,CAACJ,IAAD,CAAZ,EAAoBK,MAAM,CAACL,IAAD,CAA1B,CAA7B;AACD;AACF,GAJD;AAKD;;AAED,SAASM,qBAAT,CAAgCF,WAAhC,EAA6CC,MAA7C,EAAqD;AACnD,MAAI,CAACD,WAAD,IAAgB,CAACC,MAArB,EAA6B;AAC3B;AACD;AACD5C,QAAM,CAACsB,IAAP,CAAYsB,MAAZ,EAAoBpB,OAApB,CAA4B,UAAAe,IAAI,EAAI;AAClC,QAAId,KAAK,CAAC/D,OAAN,CAAc6E,IAAd,MAAwB,CAAC,CAAzB,IAA8BrC,IAAI,CAAC0C,MAAM,CAACL,IAAD,CAAP,CAAtC,EAAsD;AACpDD,gBAAU,CAACK,WAAW,CAACJ,IAAD,CAAZ,EAAoBK,MAAM,CAACL,IAAD,CAA1B,CAAV;AACD;AACF,GAJD;AAKD;;AAED,SAASO,cAAT,CAAyBC,MAAzB,EAAiCH,MAAjC,EAAyC;AACvC,MAAI,OAAOG,MAAP,KAAkB,QAAlB,IAA8B1C,aAAa,CAACuC,MAAD,CAA/C,EAAyD;AACvDF,wBAAoB,CAACf,kBAAkB,CAACoB,MAAD,CAAlB,KAA+BpB,kBAAkB,CAACoB,MAAD,CAAlB,GAA6B,EAA5D,CAAD,EAAkEH,MAAlE,CAApB;AACD,GAFD,MAEO,IAAIvC,aAAa,CAAC0C,MAAD,CAAjB,EAA2B;AAChCL,wBAAoB,CAAChB,kBAAD,EAAqBqB,MAArB,CAApB;AACD;AACF;;AAED,SAASC,iBAAT,CAA4BD,MAA5B,EAAoCH,MAApC,EAA4C;AAC1C,MAAI,OAAOG,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAI1C,aAAa,CAACuC,MAAD,CAAjB,EAA2B;AACzBC,2BAAqB,CAAClB,kBAAkB,CAACoB,MAAD,CAAnB,EAA6BH,MAA7B,CAArB;AACD,KAFD,MAEO;AACL,aAAOjB,kBAAkB,CAACoB,MAAD,CAAzB;AACD;AACF,GAND,MAMO,IAAI1C,aAAa,CAAC0C,MAAD,CAAjB,EAA2B;AAChCF,yBAAqB,CAACnB,kBAAD,EAAqBqB,MAArB,CAArB;AACD;AACF;;AAED,SAASE,WAAT,CAAsBV,IAAtB,EAA4B;AAC1B,SAAO,UAAUW,IAAV,EAAgB;AACrB,WAAOX,IAAI,CAACW,IAAD,CAAJ,IAAcA,IAArB;AACD,GAFD;AAGD;;AAED,SAASC,SAAT,CAAoB7C,GAApB,EAAyB;AACvB,SAAO,CAAC,CAACA,GAAF,KAAU,OAAOA,GAAP,KAAe,QAAf,IAA2B,OAAOA,GAAP,KAAe,UAApD,KAAmE,OAAOA,GAAG,CAAC8C,IAAX,KAAoB,UAA9F;AACD;;AAED,SAASC,KAAT,CAAgBjB,KAAhB,EAAuBc,IAAvB,EAA6B;AAC3B,MAAII,OAAO,GAAG,KAAd;AACA,OAAK,IAAI7F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2E,KAAK,CAAChF,MAA1B,EAAkCK,CAAC,EAAnC,EAAuC;AACrC,QAAM8E,IAAI,GAAGH,KAAK,CAAC3E,CAAD,CAAlB;AACA,QAAI6F,OAAJ,EAAa;AACXA,aAAO,GAAGC,OAAO,CAACC,OAAR,CAAgBP,WAAW,CAACV,IAAD,CAA3B,CAAV;AACD,KAFD,MAEO;AACL,UAAMR,GAAG,GAAGQ,IAAI,CAACW,IAAD,CAAhB;AACA,UAAIC,SAAS,CAACpB,GAAD,CAAb,EAAoB;AAClBuB,eAAO,GAAGC,OAAO,CAACC,OAAR,CAAgBzB,GAAhB,CAAV;AACD;AACD,UAAIA,GAAG,KAAK,KAAZ,EAAmB;AACjB,eAAO;AACLqB,cADK,kBACG,CAAG,CADN,EAAP;;AAGD;AACF;AACF;AACD,SAAOE,OAAO,IAAI;AAChBF,QADgB,gBACVK,QADU,EACA;AACd,aAAOA,QAAQ,CAACP,IAAD,CAAf;AACD,KAHe,EAAlB;;AAKD;;AAED,SAASQ,cAAT,CAAyBf,WAAzB,EAAoD,KAAdgB,OAAc,uEAAJ,EAAI;AAClD,GAAC,SAAD,EAAY,MAAZ,EAAoB,UAApB,EAAgCnC,OAAhC,CAAwC,UAAAoC,IAAI,EAAI;AAC9C,QAAI3B,KAAK,CAACC,OAAN,CAAcS,WAAW,CAACiB,IAAD,CAAzB,CAAJ,EAAsC;AACpC,UAAMC,WAAW,GAAGF,OAAO,CAACC,IAAD,CAA3B;AACAD,aAAO,CAACC,IAAD,CAAP,GAAgB,SAASE,mBAAT,CAA8B/B,GAA9B,EAAmC;AACjDsB,aAAK,CAACV,WAAW,CAACiB,IAAD,CAAZ,EAAoB7B,GAApB,CAAL,CAA8BqB,IAA9B,CAAmC,UAACrB,GAAD,EAAS;AAC1C;AACA,iBAAO7B,IAAI,CAAC2D,WAAD,CAAJ,IAAqBA,WAAW,CAAC9B,GAAD,CAAhC,IAAyCA,GAAhD;AACD,SAHD;AAID,OALD;AAMD;AACF,GAVD;AAWA,SAAO4B,OAAP;AACD;;AAED,SAASI,kBAAT,CAA6BhB,MAA7B,EAAqCiB,WAArC,EAAkD;AAChD,MAAMC,gBAAgB,GAAG,EAAzB;AACA,MAAIhC,KAAK,CAACC,OAAN,CAAcR,kBAAkB,CAACsC,WAAjC,CAAJ,EAAmD;AACjDC,oBAAgB,CAAC5B,IAAjB,OAAA4B,gBAAgB,qBAASvC,kBAAkB,CAACsC,WAA5B,EAAhB;AACD;AACD,MAAMrB,WAAW,GAAGhB,kBAAkB,CAACoB,MAAD,CAAtC;AACA,MAAIJ,WAAW,IAAIV,KAAK,CAACC,OAAN,CAAcS,WAAW,CAACqB,WAA1B,CAAnB,EAA2D;AACzDC,oBAAgB,CAAC5B,IAAjB,OAAA4B,gBAAgB,qBAAStB,WAAW,CAACqB,WAArB,EAAhB;AACD;AACDC,kBAAgB,CAACzC,OAAjB,CAAyB,UAAAe,IAAI,EAAI;AAC/ByB,eAAW,GAAGzB,IAAI,CAACyB,WAAD,CAAJ,IAAqBA,WAAnC;AACD,GAFD;AAGA,SAAOA,WAAP;AACD;;AAED,SAASE,sBAAT,CAAiCnB,MAAjC,EAAyC;AACvC,MAAMJ,WAAW,GAAG3C,MAAM,CAACa,MAAP,CAAc,IAAd,CAApB;AACAb,QAAM,CAACsB,IAAP,CAAYI,kBAAZ,EAAgCF,OAAhC,CAAwC,UAAAe,IAAI,EAAI;AAC9C,QAAIA,IAAI,KAAK,aAAb,EAA4B;AAC1BI,iBAAW,CAACJ,IAAD,CAAX,GAAoBb,kBAAkB,CAACa,IAAD,CAAlB,CAAyBpF,KAAzB,EAApB;AACD;AACF,GAJD;AAKA,MAAMgH,iBAAiB,GAAGxC,kBAAkB,CAACoB,MAAD,CAA5C;AACA,MAAIoB,iBAAJ,EAAuB;AACrBnE,UAAM,CAACsB,IAAP,CAAY6C,iBAAZ,EAA+B3C,OAA/B,CAAuC,UAAAe,IAAI,EAAI;AAC7C,UAAIA,IAAI,KAAK,aAAb,EAA4B;AAC1BI,mBAAW,CAACJ,IAAD,CAAX,GAAoB,CAACI,WAAW,CAACJ,IAAD,CAAX,IAAqB,EAAtB,EAA0BP,MAA1B,CAAiCmC,iBAAiB,CAAC5B,IAAD,CAAlD,CAApB;AACD;AACF,KAJD;AAKD;AACD,SAAOI,WAAP;AACD;;AAED,SAASyB,SAAT,CAAoBrB,MAApB,EAA4BsB,GAA5B,EAAiCV,OAAjC,EAAqD,mCAARW,MAAQ,uEAARA,MAAQ;AACnD,MAAM3B,WAAW,GAAGuB,sBAAsB,CAACnB,MAAD,CAA1C;AACA,MAAIJ,WAAW,IAAI3C,MAAM,CAACsB,IAAP,CAAYqB,WAAZ,EAAyBvF,MAA5C,EAAoD;AAClD,QAAI6E,KAAK,CAACC,OAAN,CAAcS,WAAW,CAAC4B,MAA1B,CAAJ,EAAuC;AACrC,UAAMxC,GAAG,GAAGsB,KAAK,CAACV,WAAW,CAAC4B,MAAb,EAAqBZ,OAArB,CAAjB;AACA,aAAO5B,GAAG,CAACqB,IAAJ,CAAS,UAACO,OAAD,EAAa;AAC3B,eAAOU,GAAG,MAAH,UAAIX,cAAc,CAACf,WAAD,EAAcgB,OAAd,CAAlB,SAA6CW,MAA7C,EAAP;AACD,OAFM,CAAP;AAGD,KALD,MAKO;AACL,aAAOD,GAAG,MAAH,UAAIX,cAAc,CAACf,WAAD,EAAcgB,OAAd,CAAlB,SAA6CW,MAA7C,EAAP;AACD;AACF;AACD,SAAOD,GAAG,MAAH,UAAIV,OAAJ,SAAgBW,MAAhB,EAAP;AACD;;AAED,IAAME,kBAAkB,GAAG;AACzBR,aADyB,uBACZjC,GADY,EACP;AAChB,QAAI,CAACoB,SAAS,CAACpB,GAAD,CAAd,EAAqB;AACnB,aAAOA,GAAP;AACD;AACD,WAAO,IAAIwB,OAAJ,CAAY,UAACC,OAAD,EAAUiB,MAAV,EAAqB;AACtC1C,SAAG,CAACqB,IAAJ,CAAS,UAAArB,GAAG,EAAI;AACd,YAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;AACV0C,gBAAM,CAAC1C,GAAG,CAAC,CAAD,CAAJ,CAAN;AACD,SAFD,MAEO;AACLyB,iBAAO,CAACzB,GAAG,CAAC,CAAD,CAAJ,CAAP;AACD;AACF,OAND;AAOD,KARM,CAAP;AASD,GAdwB,EAA3B;;;AAiBA,IAAM2C,WAAW;AACf,gaADF;;AAGA,IAAMC,cAAc,GAAG,kBAAvB;;AAEA;AACA,IAAMC,kBAAkB,GAAG,CAAC,qBAAD,CAA3B;;AAEA;AACA,IAAMC,SAAS,GAAG,CAAC,qBAAD,EAAwB,mBAAxB,CAAlB;;AAEA,IAAMC,eAAe,GAAG,UAAxB;;AAEA,SAASC,YAAT,CAAuBnB,IAAvB,EAA6B;AAC3B,SAAOe,cAAc,CAAC1H,IAAf,CAAoB2G,IAApB,KAA6BgB,kBAAkB,CAAClH,OAAnB,CAA2BkG,IAA3B,MAAqC,CAAC,CAA1E;AACD;AACD,SAASoB,SAAT,CAAoBpB,IAApB,EAA0B;AACxB,SAAOc,WAAW,CAACzH,IAAZ,CAAiB2G,IAAjB,KAA0BiB,SAAS,CAACnH,OAAV,CAAkBkG,IAAlB,MAA4B,CAAC,CAA9D;AACD;;AAED,SAASqB,aAAT,CAAwBrB,IAAxB,EAA8B;AAC5B,SAAOkB,eAAe,CAAC7H,IAAhB,CAAqB2G,IAArB,KAA8BA,IAAI,KAAK,QAA9C;AACD;;AAED,SAASsB,aAAT,CAAwB5B,OAAxB,EAAiC;AAC/B,SAAOA,OAAO,CAACF,IAAR,CAAa,UAAAF,IAAI,EAAI;AAC1B,WAAO,CAAC,IAAD,EAAOA,IAAP,CAAP;AACD,GAFM;AAGJiC,OAHI,CAGE,UAAAC,GAAG,UAAI,CAACA,GAAD,CAAJ,EAHL,CAAP;AAID;;AAED,SAASC,aAAT,CAAwBzB,IAAxB,EAA8B;AAC5B;AACEmB,cAAY,CAACnB,IAAD,CAAZ;AACAoB,WAAS,CAACpB,IAAD,CADT;AAEAqB,eAAa,CAACrB,IAAD,CAHf;AAIE;AACA,WAAO,KAAP;AACD;AACD,SAAO,IAAP;AACD;;AAED;AACA,IAAI,CAACL,OAAO,CAAChE,SAAR,CAAkB+F,OAAvB,EAAgC;AAC9B/B,SAAO,CAAChE,SAAR,CAAkB+F,OAAlB,GAA4B,UAAU7B,QAAV,EAAoB;AAC9C,QAAMH,OAAO,GAAG,KAAKiC,WAArB;AACA,WAAO,KAAKnC,IAAL;AACL,cAAAoC,KAAK,UAAIlC,OAAO,CAACE,OAAR,CAAgBC,QAAQ,EAAxB,EAA4BL,IAA5B,CAAiC,oBAAMoC,KAAN,EAAjC,CAAJ,EADA;AAEL,cAAAC,MAAM,UAAInC,OAAO,CAACE,OAAR,CAAgBC,QAAQ,EAAxB,EAA4BL,IAA5B,CAAiC,YAAM;AAC/C,cAAMqC,MAAN;AACD,OAFS,CAAJ,EAFD,CAAP;;AAMD,GARD;AASD;;AAED,SAASC,SAAT,CAAoB9B,IAApB,EAA0BS,GAA1B,EAA+B;AAC7B,MAAI,CAACgB,aAAa,CAACzB,IAAD,CAAlB,EAA0B;AACxB,WAAOS,GAAP;AACD;AACD,SAAO,SAASsB,UAAT,GAA8C,KAAzBhC,OAAyB,uEAAf,EAAe,oCAARW,MAAQ,6EAARA,MAAQ;AACnD,QAAIpE,IAAI,CAACyD,OAAO,CAACiC,OAAT,CAAJ,IAAyB1F,IAAI,CAACyD,OAAO,CAACkC,IAAT,CAA7B,IAA+C3F,IAAI,CAACyD,OAAO,CAACmC,QAAT,CAAvD,EAA2E;AACzE,aAAO/B,kBAAkB,CAACH,IAAD,EAAOQ,SAAS,MAAT,UAAUR,IAAV,EAAgBS,GAAhB,EAAqBV,OAArB,SAAiCW,MAAjC,EAAP,CAAzB;AACD;AACD,WAAOP,kBAAkB,CAACH,IAAD,EAAOsB,aAAa,CAAC,IAAI3B,OAAJ,CAAY,UAACC,OAAD,EAAUiB,MAAV,EAAqB;AAC7EL,eAAS,MAAT,UAAUR,IAAV,EAAgBS,GAAhB,EAAqBrE,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBpC,OAAlB,EAA2B;AAC9CiC,eAAO,EAAEpC,OADqC;AAE9CqC,YAAI,EAAEpB,MAFwC,EAA3B,CAArB;AAGOH,YAHP;AAID,KAL6C,CAAD,CAApB,CAAzB;AAMD,GAVD;AAWD;;AAED,IAAM0B,GAAG,GAAG,IAAZ;AACA,IAAMC,iBAAiB,GAAG,GAA1B;AACA,IAAIC,KAAK,GAAG,KAAZ;AACA,IAAIC,WAAW,GAAG,CAAlB;AACA,IAAIC,SAAS,GAAG,CAAhB;;AAEA,SAASC,gBAAT,GAA6B;;;;;AAKvB9H,IAAE,CAAC+H,iBAAH,EALuB,CAEzBC,QAFyB,yBAEzBA,QAFyB,CAGzBC,UAHyB,yBAGzBA,UAHyB,CAIzBC,WAJyB,yBAIzBA,WAJyB,EAKC;;AAE5BN,aAAW,GAAGM,WAAd;AACAL,WAAS,GAAGI,UAAZ;AACAN,OAAK,GAAGK,QAAQ,KAAK,KAArB;AACD;;AAED,SAASG,MAAT,CAAiBC,MAAjB,EAAyBC,cAAzB,EAAyC;AACvC,MAAIT,WAAW,KAAK,CAApB,EAAuB;AACrBE,oBAAgB;AACjB;;AAEDM,QAAM,GAAGE,MAAM,CAACF,MAAD,CAAf;AACA,MAAIA,MAAM,KAAK,CAAf,EAAkB;AAChB,WAAO,CAAP;AACD;AACD,MAAIrJ,MAAM,GAAIqJ,MAAM,GAAGV,iBAAV,IAAgCW,cAAc,IAAIT,WAAlD,CAAb;AACA,MAAI7I,MAAM,GAAG,CAAb,EAAgB;AACdA,UAAM,GAAG,CAACA,MAAV;AACD;AACDA,QAAM,GAAGwJ,IAAI,CAACC,KAAL,CAAWzJ,MAAM,GAAG0I,GAApB,CAAT;AACA,MAAI1I,MAAM,KAAK,CAAf,EAAkB;AAChB,QAAI8I,SAAS,KAAK,CAAd,IAAmB,CAACF,KAAxB,EAA+B;AAC7B5I,YAAM,GAAG,CAAT;AACD,KAFD,MAEO;AACLA,YAAM,GAAG,GAAT;AACD;AACF;AACD,SAAOqJ,MAAM,GAAG,CAAT,GAAa,CAACrJ,MAAd,GAAuBA,MAA9B;AACD;;AAED,IAAM0J,cAAc,GAAG,SAAvB;AACA,IAAMC,cAAc,GAAG,SAAvB;AACA,IAAMC,SAAS,GAAG,IAAlB;AACA,IAAMC,SAAS,GAAG,IAAlB;AACA,IAAMC,SAAS,GAAG,IAAlB;;AAEA,IAAMC,QAAQ,GAAG,EAAjB;;AAEA,IAAIC,MAAJ;;AAEA;AACEA,QAAM,GAAGC,eAAe,CAAChJ,EAAE,CAAC+H,iBAAH,GAAuBkB,QAAxB,CAAf,IAAoDN,SAA7D;AACD;;AAED,SAASO,gBAAT,GAA6B;AAC3B,MAAI,CAACC,cAAc,EAAnB,EAAuB;AACrB;AACD;AACD,MAAMC,UAAU,GAAG3H,MAAM,CAACsB,IAAP,CAAYsG,WAAW,CAACC,OAAxB,CAAnB;AACA,MAAIF,UAAU,CAACvK,MAAf,EAAuB;AACrBuK,cAAU,CAACnG,OAAX,CAAmB,UAAC8F,MAAD,EAAY;AAC7B,UAAMQ,WAAW,GAAGT,QAAQ,CAACC,MAAD,CAA5B;AACA,UAAMS,YAAY,GAAGH,WAAW,CAACC,OAAZ,CAAoBP,MAApB,CAArB;AACA,UAAIQ,WAAJ,EAAiB;AACf9H,cAAM,CAAC+F,MAAP,CAAc+B,WAAd,EAA2BC,YAA3B;AACD,OAFD,MAEO;AACLV,gBAAQ,CAACC,MAAD,CAAR,GAAmBS,YAAnB;AACD;AACF,KARD;AASD;AACF;;AAEDN,gBAAgB;;AAEhB,IAAMO,IAAI,GAAG;AACXV,MADW;AAEV,EAFU,CAAb;;AAIA,IAAMW,CAAC,GAAGD,IAAI,CAACC,CAAf;AACA,IAAMC,SAAS,GAAIF,IAAI,CAACG,KAAL,GAAa;AAC9BC,cAD8B,0BACd;AACd,QAAMC,OAAO,GAAGL,IAAI,CAACA,IAAL,CAAUM,WAAV,CAAsB,YAAM;AAC1C,WAAI,CAACC,YAAL;AACD,KAFe,CAAhB;AAGA,SAAKC,KAAL,CAAW,oBAAX,EAAiC,YAAY;AAC3CH,aAAO;AACR,KAFD;AAGD,GAR6B;AAS9BI,SAAO,EAAE;AACPC,OADO,eACFjI,GADE,EACGkI,MADH,EACW;AAChB,aAAOV,CAAC,CAACxH,GAAD,EAAMkI,MAAN,CAAR;AACD,KAHM,EATqB,EAAhC;;;AAeA,IAAMC,SAAS,GAAGZ,IAAI,CAACY,SAAvB;AACA,IAAMC,SAAS,GAAGb,IAAI,CAACa,SAAvB;;AAEA,SAASC,aAAT,CAAwBxJ,GAAxB,EAA6ByJ,KAA7B,EAAoCzB,MAApC,EAA4C;AAC1C,MAAM0B,KAAK,GAAG1J,GAAG,CAAC2J,UAAJ,CAAe;AAC3B3B,UAAM,EAAEA,MAAM,IAAIU,IAAI,CAACa,SAAL,EADS,EAAf,CAAd;;AAGA,MAAMK,cAAc,GAAG,EAAvB;AACAH,OAAK,CAACI,YAAN,GAAqB,UAAAhJ,EAAE,EAAI;AACzB+I,kBAAc,CAAC7G,IAAf,CAAoBlC,EAApB;AACD,GAFD;AAGAH,QAAM,CAACoJ,cAAP,CAAsBL,KAAtB,EAA6B,SAA7B,EAAwC;AACtCM,OADsC,iBAC/B;AACL,aAAOL,KAAK,CAAC1B,MAAb;AACD,KAHqC;AAItCgC,OAJsC,eAIjCC,CAJiC,EAI9B;AACNP,WAAK,CAAC1B,MAAN,GAAeiC,CAAf;AACAL,oBAAc,CAAC1H,OAAf,CAAuB,UAAAgI,KAAK,UAAIA,KAAK,CAACD,CAAD,CAAT,EAA5B;AACD,KAPqC,EAAxC;;AASD;;AAED,SAAS7B,cAAT,GAA2B;AACzB,SAAO,OAAOE,WAAP,KAAuB,WAAvB,IAAsCA,WAAW,CAACC,OAAlD,IAA6D,CAAC,CAAC7H,MAAM,CAACsB,IAAP,CAAYsG,WAAW,CAACC,OAAxB,EAAiCzK,MAAvG;AACD;;AAED,SAASqM,OAAT,CAAkB3M,GAAlB,EAAuB4M,KAAvB,EAA8B;AAC5B,SAAO,CAAC,CAACA,KAAK,CAACC,IAAN,CAAW,UAACC,IAAD,UAAU9M,GAAG,CAACY,OAAJ,CAAYkM,IAAZ,MAAsB,CAAC,CAAjC,EAAX,CAAT;AACD;;AAED,SAASC,UAAT,CAAqB/M,GAArB,EAA0B4M,KAA1B,EAAiC;AAC/B,SAAOA,KAAK,CAACC,IAAN,CAAW,UAACC,IAAD,UAAU9M,GAAG,CAACY,OAAJ,CAAYkM,IAAZ,MAAsB,CAAhC,EAAX,CAAP;AACD;;AAED,SAASrC,eAAT,CAA0BD,MAA1B,EAAkCD,QAAlC,EAA4C;AAC1C,MAAI,CAACC,MAAL,EAAa;AACX;AACD;AACDA,QAAM,GAAGA,MAAM,CAACwC,IAAP,GAAc9M,OAAd,CAAsB,IAAtB,EAA4B,GAA5B,CAAT;AACA,MAAIqK,QAAQ,IAAIA,QAAQ,CAACC,MAAD,CAAxB,EAAkC;AAChC,WAAOA,MAAP;AACD;AACDA,QAAM,GAAGA,MAAM,CAACyC,WAAP,EAAT;AACA,MAAIzC,MAAM,KAAK,SAAf,EAA0B;AACxB;AACA,WAAON,cAAP;AACD;AACD,MAAIM,MAAM,CAAC5J,OAAP,CAAe,IAAf,MAAyB,CAA7B,EAAgC;AAC9B,QAAI4J,MAAM,CAAC5J,OAAP,CAAe,OAAf,IAA0B,CAAC,CAA/B,EAAkC;AAChC,aAAOsJ,cAAP;AACD;AACD,QAAIM,MAAM,CAAC5J,OAAP,CAAe,OAAf,IAA0B,CAAC,CAA/B,EAAkC;AAChC,aAAOuJ,cAAP;AACD;AACD,QAAIwC,OAAO,CAACnC,MAAD,EAAS,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,MAAtB,CAAT,CAAX,EAAoD;AAClD,aAAOL,cAAP;AACD;AACD,WAAOD,cAAP;AACD;AACD,MAAMgD,IAAI,GAAGH,UAAU,CAACvC,MAAD,EAAS,CAACJ,SAAD,EAAYC,SAAZ,EAAuBC,SAAvB,CAAT,CAAvB;AACA,MAAI4C,IAAJ,EAAU;AACR,WAAOA,IAAP;AACD;AACF;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAT,GAAwB;AACtB;AACA,MAAMC,GAAG,GAAGC,MAAM,CAAC;AACjBC,gBAAY,EAAE,IADG,EAAD,CAAlB;;AAGA,MAAIF,GAAG,IAAIA,GAAG,CAACG,GAAf,EAAoB;AAClB,WAAOH,GAAG,CAACG,GAAJ,CAAQC,OAAf;AACD;AACD,SAAO/C,eAAe,CAAChJ,EAAE,CAAC+H,iBAAH,GAAuBkB,QAAxB,CAAf,IAAoDN,SAA3D;AACD;;AAED,SAASqD,WAAT,CAAsBjD,MAAtB,EAA8B;AAC5B,MAAM4C,GAAG,GAAGC,MAAM,EAAlB;AACA,MAAI,CAACD,GAAL,EAAU;AACR,WAAO,KAAP;AACD;AACD,MAAMM,SAAS,GAAGN,GAAG,CAACG,GAAJ,CAAQC,OAA1B;AACA,MAAIE,SAAS,KAAKlD,MAAlB,EAA0B;AACxB4C,OAAG,CAACG,GAAJ,CAAQC,OAAR,GAAkBhD,MAAlB;AACAmD,2BAAuB,CAACjJ,OAAxB,CAAgC,UAACrB,EAAD,UAAQA,EAAE,CAAC;AACzCmH,cAAM,EAANA,MADyC,EAAD,CAAV,EAAhC;;AAGA,WAAO,IAAP;AACD;AACD,SAAO,KAAP;AACD;;AAED,IAAMmD,uBAAuB,GAAG,EAAhC;AACA,SAASC,cAAT,CAAyBvK,EAAzB,EAA6B;AAC3B,MAAIsK,uBAAuB,CAAC/M,OAAxB,CAAgCyC,EAAhC,MAAwC,CAAC,CAA7C,EAAgD;AAC9CsK,2BAAuB,CAACpI,IAAxB,CAA6BlC,EAA7B;AACD;AACF;;AAED,IAAI,OAAOwK,MAAP,KAAkB,WAAtB,EAAmC;AACjCA,QAAM,CAAC9B,SAAP,GAAmBoB,WAAnB;AACD;;AAED,IAAMW,YAAY,GAAG;AACnBpG,oBAAkB,EAAlBA,kBADmB,EAArB;;;AAIA,IAAIqG,OAAO,GAAG,aAAa7K,MAAM,CAAC8K,MAAP,CAAc;AACvCC,WAAS,EAAE,IAD4B;AAEvCrE,QAAM,EAAEA,MAF+B;AAGvCmC,WAAS,EAAEoB,WAH4B;AAIvCrB,WAAS,EAAE2B,WAJ4B;AAKvCG,gBAAc,EAAEA,cALuB;AAMvC5H,gBAAc,EAAEA,cANuB;AAOvCE,mBAAiB,EAAEA,iBAPoB;AAQvC4H,cAAY,EAAEA,YARyB,EAAd,CAA3B;;;AAWA,SAASI,mBAAT,CAA8BC,GAA9B,EAAmC;AACjC,MAAMC,KAAK,GAAGC,eAAe,EAA7B;AACA,MAAIC,GAAG,GAAGF,KAAK,CAAC9N,MAAhB;AACA,SAAOgO,GAAG,EAAV,EAAc;AACZ,QAAMC,IAAI,GAAGH,KAAK,CAACE,GAAD,CAAlB;AACA,QAAIC,IAAI,CAACC,KAAL,IAAcD,IAAI,CAACC,KAAL,CAAWC,QAAX,KAAwBN,GAA1C,EAA+C;AAC7C,aAAOG,GAAP;AACD;AACF;AACD,SAAO,CAAC,CAAR;AACD;;AAED,IAAII,UAAU,GAAG;AACf5H,MADe,gBACT6H,QADS,EACC;AACd,QAAIA,QAAQ,CAACC,MAAT,KAAoB,MAApB,IAA8BD,QAAQ,CAACE,KAA3C,EAAkD;AAChD,aAAO,cAAP;AACD;AACD,WAAO,YAAP;AACD,GANc;AAOfC,MAPe,gBAOTH,QAPS,EAOC;AACd,QAAIA,QAAQ,CAACC,MAAT,KAAoB,MAApB,IAA8BD,QAAQ,CAACR,GAA3C,EAAgD;AAC9C,UAAMY,eAAe,GAAGb,mBAAmB,CAACS,QAAQ,CAACR,GAAV,CAA3C;AACA,UAAIY,eAAe,KAAK,CAAC,CAAzB,EAA4B;AAC1B,YAAMF,KAAK,GAAGR,eAAe,GAAG/N,MAAlB,GAA2B,CAA3B,GAA+ByO,eAA7C;AACA,YAAIF,KAAK,GAAG,CAAZ,EAAe;AACbF,kBAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACD;AACF;AACF;AACF,GAjBc,EAAjB;;;AAoBA,IAAIG,YAAY,GAAG;AACjBF,MADiB,gBACXH,QADW,EACD;AACd,QAAIM,YAAY,GAAGC,QAAQ,CAACP,QAAQ,CAACQ,OAAV,CAA3B;AACA,QAAIC,KAAK,CAACH,YAAD,CAAT,EAAyB;AACvB;AACD;AACD,QAAMI,IAAI,GAAGV,QAAQ,CAACU,IAAtB;AACA,QAAI,CAAClK,KAAK,CAACC,OAAN,CAAciK,IAAd,CAAL,EAA0B;AACxB;AACD;AACD,QAAMf,GAAG,GAAGe,IAAI,CAAC/O,MAAjB;AACA,QAAI,CAACgO,GAAL,EAAU;AACR;AACD;AACD,QAAIW,YAAY,GAAG,CAAnB,EAAsB;AACpBA,kBAAY,GAAG,CAAf;AACD,KAFD,MAEO,IAAIA,YAAY,IAAIX,GAApB,EAAyB;AAC9BW,kBAAY,GAAGX,GAAG,GAAG,CAArB;AACD;AACD,QAAIW,YAAY,GAAG,CAAnB,EAAsB;AACpBN,cAAQ,CAACQ,OAAT,GAAmBE,IAAI,CAACJ,YAAD,CAAvB;AACAN,cAAQ,CAACU,IAAT,GAAgBA,IAAI,CAACC,MAAL;AACd,gBAACC,IAAD,EAAO7J,KAAP,UAAiBA,KAAK,GAAGuJ,YAAR,GAAuBM,IAAI,KAAKF,IAAI,CAACJ,YAAD,CAApC,GAAqD,IAAtE,EADc,CAAhB;;AAGD,KALD,MAKO;AACLN,cAAQ,CAACQ,OAAT,GAAmBE,IAAI,CAAC,CAAD,CAAvB;AACD;AACD,WAAO;AACLG,eAAS,EAAE,KADN;AAELC,UAAI,EAAE,KAFD,EAAP;;AAID,GA/BgB,EAAnB;;;AAkCA,IAAMC,QAAQ,GAAG,gBAAjB;AACA,IAAIC,QAAJ;AACA,SAASC,WAAT,CAAsBpP,MAAtB,EAA8B;AAC5BmP,UAAQ,GAAGA,QAAQ,IAAIlO,EAAE,CAACC,cAAH,CAAkBgO,QAAlB,CAAvB;AACA,MAAI,CAACC,QAAL,EAAe;AACbA,YAAQ,GAAG5M,IAAI,CAACC,GAAL,KAAa,EAAb,GAAkBgH,IAAI,CAACC,KAAL,CAAWD,IAAI,CAAC6F,MAAL,KAAgB,GAA3B,CAA7B;AACApO,MAAE,CAACqO,UAAH,CAAc;AACZnM,SAAG,EAAE+L,QADO;AAEZtJ,UAAI,EAAEuJ,QAFM,EAAd;;AAID;AACDnP,QAAM,CAACmP,QAAP,GAAkBA,QAAlB;AACD;;AAED,SAASI,iBAAT,CAA4BvP,MAA5B,EAAoC;AAClC,MAAIA,MAAM,CAACwP,QAAX,EAAqB;AACnB,QAAMA,QAAQ,GAAGxP,MAAM,CAACwP,QAAxB;AACAxP,UAAM,CAACyP,cAAP,GAAwB;AACtBC,SAAG,EAAEF,QAAQ,CAACE,GADQ;AAEtBC,UAAI,EAAEH,QAAQ,CAACG,IAFO;AAGtBC,WAAK,EAAE5P,MAAM,CAACmJ,WAAP,GAAqBqG,QAAQ,CAACI,KAHf;AAItBC,YAAM,EAAE7P,MAAM,CAAC8P,YAAP,GAAsBN,QAAQ,CAACK,MAJjB,EAAxB;;AAMD;AACF;;AAED,SAASE,kBAAT,CAA6B/P,MAA7B,EAAqC;;;;;;AAM/BA,QAN+B,CAEjCgQ,KAFiC,CAEjCA,KAFiC,8BAEzB,EAFyB,iCAM/BhQ,MAN+B,CAErBiQ,KAFqB,CAErBA,KAFqB,8BAEb,EAFa,kCAM/BjQ,MAN+B,CAETkQ,MAFS,CAETA,MAFS,+BAEA,EAFA,qCAM/BlQ,MAN+B,CAGjCkK,QAHiC,CAGjCA,QAHiC,iCAGtB,EAHsB,oBAGlBiG,KAHkB,GAM/BnQ,MAN+B,CAGlBmQ,KAHkB,CAGXC,OAHW,GAM/BpQ,MAN+B,CAGXoQ,OAHW,CAIjCnH,QAJiC,GAM/BjJ,MAN+B,CAIjCiJ,QAJiC,CAIvBoH,eAJuB,GAM/BrQ,MAN+B,CAIvBqQ,eAJuB,CAKjCC,UALiC,GAM/BtQ,MAN+B,CAKjCsQ,UALiC,CAKrBpH,UALqB,GAM/BlJ,MAN+B,CAKrBkJ,UALqB,CAKTqH,iBALS,GAM/BvQ,MAN+B,CAKTuQ,iBALS;AAOnC;;AAEA;AACA,MAAIC,MAAM,GAAG,EAAb;AACA,MAAIC,SAAS,GAAG,EAAhB;AACA;AACED,UAAM,GAAGN,MAAM,CAACzP,KAAP,CAAa,GAAb,EAAkB,CAAlB,KAAwB,EAAjC;AACAgQ,aAAS,GAAGP,MAAM,CAACzP,KAAP,CAAa,GAAb,EAAkB,CAAlB,KAAwB,EAApC;AACD;AACD,MAAIiQ,WAAW,GAAGN,OAAlB;;AAEA;AACA,MAAMO,UAAU,GAAGC,gBAAgB,CAAC5Q,MAAD,EAASiQ,KAAT,CAAnC;;AAEA;AACA,MAAMY,WAAW,GAAGC,cAAc,CAACd,KAAD,CAAlC;;AAEA;AACA,MAAMe,SAAS,GAAGC,WAAW,CAAChR,MAAD,CAA7B;;AAEA;AACA,MAAIiR,kBAAkB,GAAGV,iBAAzB,CA5BmC,CA4BS;;AAE5C;AACA,MAAIW,iBAAiB,GAAGhI,UAAxB;;AAEA;AACA,MAAIiI,WAAW,GAAGb,UAAlB;;AAEA;AACA,MAAMc,YAAY,GAAGlH,QAAQ,CAACxK,OAAT,CAAiB,IAAjB,EAAuB,GAAvB,CAArB;;AAEA;;AAEA,MAAM2R,UAAU,GAAG;AACjBC,SAAK,EAAEC,gBADU;AAEjBC,WAAO,EAAED,OAFQ;AAGjBE,cAAU,EAAEF,OAHK;AAIjBG,kBAAc,EAAEH,KAJC;AAKjBI,eAAW,EAAEC,cAAc,CAACR,YAAD,CALV;AAMjBS,qBAAiB,EAAEN,OANF;AAOjBO,qBAAiB,EAAEP,OAPF;AAQjBQ,eAAW,EAAER,SAAA,IAAgCA,WAR5B;AASjBV,eAAW,EAAXA,WATiB;AAUjBmB,eAAW,EAAE/B,KAVI;AAWjBU,cAAU,EAAVA,UAXiB;AAYjBsB,oBAAgB,EAAEf,iBAZD;AAajBX,qBAAiB,EAAEU,kBAbF;AAcjBT,UAAM,EAAEA,MAAM,CAAC0B,iBAAP,EAdS;AAejBzB,aAAS,EAATA,SAfiB;AAgBjB0B,aAAS,EAAEhC,KAhBM;AAiBjBO,eAAW,EAAXA,WAjBiB;AAkBjBU,gBAAY,EAAZA,YAlBiB;AAmBjBgB,YAAQ,EAAErB,SAnBO;AAoBjBsB,kBAAc,EAAElB,WApBC;AAqBjBmB,uBAAmB,EAAEjC,eArBJ;AAsBjBkC,aAAS,EAAE,CAtBM;AAuBjBC,gBAAY,EAAE,CAvBG;AAwBjB;AACAC,cAAU,EAAEC,SAzBK;AA0BjBC,WAAO,EAAED,SA1BQ;AA2BjBE,MAAE,EAAEF,SA3Ba;AA4BjBG,mBAAe,EAAEH,SA5BA;AA6BjBI,eAAW,EAAEJ,SA7BI;AA8BjBK,kBAAc,EAAEL,SA9BC,EAAnB;;;AAiCAhQ,QAAM,CAAC+F,MAAP,CAAczI,MAAd,EAAsBqR,UAAtB;AACD;;AAED,SAAST,gBAAT,CAA2B5Q,MAA3B,EAAmCiQ,KAAnC,EAA0C;AACxC,MAAIU,UAAU,GAAG3Q,MAAM,CAAC2Q,UAAP,IAAqB,OAAtC;AACA;AACE,QAAMqC,cAAc,GAAG;AACrBC,UAAI,EAAE,KADe;AAErBC,aAAO,EAAE,IAFY;AAGrBC,SAAG,EAAE,IAHgB,EAAvB;;AAKA,QAAMC,kBAAkB,GAAG1Q,MAAM,CAACsB,IAAP,CAAYgP,cAAZ,CAA3B;AACA,QAAMK,MAAM,GAAGpD,KAAK,CAACiC,iBAAN,EAAf;AACA,SAAK,IAAIhN,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGkO,kBAAkB,CAACtT,MAA/C,EAAuDoF,KAAK,EAA5D,EAAgE;AAC9D,UAAMoO,EAAE,GAAGF,kBAAkB,CAAClO,KAAD,CAA7B;AACA,UAAImO,MAAM,CAACjT,OAAP,CAAekT,EAAf,MAAuB,CAAC,CAA5B,EAA+B;AAC7B3C,kBAAU,GAAGqC,cAAc,CAACM,EAAD,CAA3B;AACA;AACD;AACF;AACF;AACD,SAAO3C,UAAP;AACD;;AAED,SAASG,cAAT,CAAyBd,KAAzB,EAAgC;AAC9B,MAAIa,WAAW,GAAGb,KAAlB;AACA,MAAIa,WAAJ,EAAiB;AACfA,eAAW,GAAGb,KAAK,CAACkC,iBAAN,EAAd;AACD;AACD,SAAOrB,WAAP;AACD;;AAED,SAASe,cAAT,CAAyB2B,eAAzB,EAA0C;AACxC,SAAO5G,WAAW;AACdA,aAAW,EADG;AAEd4G,iBAFJ;AAGD;;AAED,SAASvC,WAAT,CAAsBhR,MAAtB,EAA8B;AAC5B,MAAMwT,SAAS,GAAI,QAAnB;AACA,MAAIzC,SAAS,GAAG/Q,MAAM,CAACoS,QAAP,IAAmBoB,SAAnC,CAF4B,CAEkB;AAC9C;AACE,QAAIxT,MAAM,CAACyT,WAAX,EAAwB;AACtB1C,eAAS,GAAG/Q,MAAM,CAACyT,WAAnB;AACD,KAFD,MAEO,IAAIzT,MAAM,CAAC0T,IAAP,IAAe1T,MAAM,CAAC0T,IAAP,CAAYC,GAA/B,EAAoC;AACzC5C,eAAS,GAAG/Q,MAAM,CAAC0T,IAAP,CAAYC,GAAxB;AACD;AACF;;AAED,SAAO5C,SAAP;AACD;;AAED,IAAI6C,aAAa,GAAG;AAClBlN,aAAW,EAAE,qBAAU1G,MAAV,EAAkB;AAC7BoP,eAAW,CAACpP,MAAD,CAAX;AACAuP,qBAAiB,CAACvP,MAAD,CAAjB;AACA+P,sBAAkB,CAAC/P,MAAD,CAAlB;AACD,GALiB,EAApB;;;AAQA,IAAI6T,eAAe,GAAG;AACpBvF,MADoB,gBACdH,QADc,EACJ;AACd,QAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC;AAChCA,cAAQ,CAAC2F,SAAT,GAAqB3F,QAAQ,CAAC4F,KAA9B;AACD;AACF,GALmB,EAAtB;;;AAQA,IAAIC,cAAc,GAAG;AACnBtN,aAAW,EAAE,qBAAU1G,MAAV,EAAkB;AACoBA,UADpB,CACrBoQ,OADqB,WACrBA,OADqB,CACZlG,QADY,WACZA,QADY,CACFoG,UADE,WACFA,UADE,CACUH,KADV,WACUA,KADV;;AAG7B,QAAMY,SAAS,GAAGC,WAAW,CAAChR,MAAD,CAA7B;;AAEA,QAAMoR,YAAY,GAAGlH,QAAQ,CAACxK,OAAT,CAAiB,GAAjB,EAAsB,GAAtB,CAArB;;AAEAM,UAAM,GAAG8D,UAAU,CAACpB,MAAM,CAAC+F,MAAP,CAAczI,MAAd,EAAsB;AACxCsR,WAAK,EAAEC,gBADiC;AAExCC,aAAO,EAAED,OAF+B;AAGxCE,gBAAU,EAAEF,OAH4B;AAIxCG,oBAAc,EAAEH,KAJwB;AAKxCI,iBAAW,EAAEC,cAAc,CAACR,YAAD,CALa;AAMxCV,iBAAW,EAAEN,OAN2B;AAOxCgB,kBAAY,EAAZA,YAPwC;AAQxCgB,cAAQ,EAAErB,SAR8B;AASxCsB,oBAAc,EAAE/B,UATwB;AAUxC6B,eAAS,EAAEhC,KAV6B,EAAtB,CAAD,CAAnB;;AAYD,GApBkB,EAArB;;;AAuBA,IAAI8D,aAAa,GAAG;AAClBvN,aAAW,EAAE,qBAAU1G,MAAV,EAAkB;AACJA,UADI,CACrBgQ,KADqB,YACrBA,KADqB,CACdC,KADc,YACdA,KADc;AAE7B,QAAMU,UAAU,GAAGC,gBAAgB,CAAC5Q,MAAD,EAASiQ,KAAT,CAAnC;AACA,QAAMY,WAAW,GAAGC,cAAc,CAACd,KAAD,CAAlC;AACAZ,eAAW,CAACpP,MAAD,CAAX;;AAEAA,UAAM,GAAG8D,UAAU,CAACpB,MAAM,CAAC+F,MAAP,CAAczI,MAAd,EAAsB;AACxC2Q,gBAAU,EAAVA,UADwC;AAExCE,iBAAW,EAAXA,WAFwC;AAGxCmB,iBAAW,EAAE/B,KAH2B,EAAtB,CAAD,CAAnB;;AAKD,GAZiB,EAApB;;;AAeA,IAAIiE,aAAa,GAAG;AAClBxN,aAAW,EAAE,qBAAU1G,MAAV,EAAkB;AAC7BuP,qBAAiB,CAACvP,MAAD,CAAjB;;AAEAA,UAAM,GAAG8D,UAAU,CAACpB,MAAM,CAAC+F,MAAP,CAAczI,MAAd,EAAsB;AACxCuS,eAAS,EAAE,CAD6B;AAExCC,kBAAY,EAAE,CAF0B,EAAtB,CAAD,CAAnB;;AAID,GARiB,EAApB;;;AAWA,IAAI2B,sBAAsB,GAAG;AAC3BzN,aAAW,EAAE,qBAAU1G,MAAV,EAAkB;AACrBoU,2BADqB,GACOpU,MADP,CACrBoU,uBADqB;;AAG7BpU,UAAM,CAACqU,gBAAP,GAA0B,aAA1B;AACA,QAAID,uBAAuB,KAAK,IAAhC,EAAsC;AACpCpU,YAAM,CAACqU,gBAAP,GAA0B,SAA1B;AACD,KAFD,MAEO,IAAID,uBAAuB,KAAK,KAAhC,EAAuC;AAC5CpU,YAAM,CAACqU,gBAAP,GAA0B,MAA1B;AACD;AACF,GAV0B,EAA7B;;;AAaA;;AAEA,IAAMC,SAAS,GAAG;AAChBpG,YAAU,EAAVA,UADgB;AAEhB;AACAM,cAAY,EAAZA,YAHgB;AAIhBoF,eAAa,EAAbA,aAJgB;AAKhB5K,mBAAiB,EAAE4K,aALH;AAMhBC,iBAAe,EAAfA,eANgB;AAOhBG,gBAAc,EAAdA,cAPgB;AAQhBC,eAAa,EAAbA,aARgB;AAShBC,eAAa,EAAbA,aATgB;AAUhBC,wBAAsB,EAAtBA,sBAVgB,EAAlB;;AAYA,IAAMI,KAAK,GAAG;AACZ,SADY;AAEZ,aAFY;AAGZ,eAHY;AAIZ,gBAJY,CAAd;;AAMA,IAAMC,QAAQ,GAAG,EAAjB;;AAEA,IAAMC,SAAS,GAAG,CAAC,SAAD,EAAY,MAAZ,EAAoB,QAApB,EAA8B,UAA9B,CAAlB;;AAEA,SAASC,eAAT,CAA0BC,UAA1B,EAAsClP,MAAtC,EAA8CiB,WAA9C,EAA2D;AACzD,SAAO,UAAUjC,GAAV,EAAe;AACpB,WAAOgB,MAAM,CAACmP,kBAAkB,CAACD,UAAD,EAAalQ,GAAb,EAAkBiC,WAAlB,CAAnB,CAAb;AACD,GAFD;AAGD;;AAED,SAASmO,WAAT,CAAsBF,UAAtB,EAAkCxG,QAAlC,EAAqG,KAAzD2G,UAAyD,uEAA5C,EAA4C,KAAxCpO,WAAwC,uEAA1B,EAA0B,KAAtBqO,YAAsB,uEAAP,KAAO;AACnG,MAAIhS,aAAa,CAACoL,QAAD,CAAjB,EAA6B,CAAE;AAC7B,QAAM6G,MAAM,GAAGD,YAAY,KAAK,IAAjB,GAAwB5G,QAAxB,GAAmC,EAAlD,CAD2B,CAC2B;AACtD,QAAIvL,IAAI,CAACkS,UAAD,CAAR,EAAsB;AACpBA,gBAAU,GAAGA,UAAU,CAAC3G,QAAD,EAAW6G,MAAX,CAAV,IAAgC,EAA7C;AACD;AACD,SAAK,IAAM7R,GAAX,IAAkBgL,QAAlB,EAA4B;AAC1B,UAAIjL,MAAM,CAAC4R,UAAD,EAAa3R,GAAb,CAAV,EAA6B;AAC3B,YAAI8R,SAAS,GAAGH,UAAU,CAAC3R,GAAD,CAA1B;AACA,YAAIP,IAAI,CAACqS,SAAD,CAAR,EAAqB;AACnBA,mBAAS,GAAGA,SAAS,CAAC9G,QAAQ,CAAChL,GAAD,CAAT,EAAgBgL,QAAhB,EAA0B6G,MAA1B,CAArB;AACD;AACD,YAAI,CAACC,SAAL,EAAgB,CAAE;AAChBC,iBAAO,CAACC,IAAR,gBAAqBR,UAArB,4FAAwFxR,GAAxF;AACD,SAFD,MAEO,IAAIL,KAAK,CAACmS,SAAD,CAAT,EAAsB,CAAE;AAC7BD,gBAAM,CAACC,SAAD,CAAN,GAAoB9G,QAAQ,CAAChL,GAAD,CAA5B;AACD,SAFM,MAEA,IAAIJ,aAAa,CAACkS,SAAD,CAAjB,EAA8B,CAAE;AACrCD,gBAAM,CAACC,SAAS,CAAC3O,IAAV,GAAiB2O,SAAS,CAAC3O,IAA3B,GAAkCnD,GAAnC,CAAN,GAAgD8R,SAAS,CAAC/M,KAA1D;AACD;AACF,OAZD,MAYO,IAAIuM,SAAS,CAACrU,OAAV,CAAkB+C,GAAlB,MAA2B,CAAC,CAAhC,EAAmC;AACxC,YAAIP,IAAI,CAACuL,QAAQ,CAAChL,GAAD,CAAT,CAAR,EAAyB;AACvB6R,gBAAM,CAAC7R,GAAD,CAAN,GAAcuR,eAAe,CAACC,UAAD,EAAaxG,QAAQ,CAAChL,GAAD,CAArB,EAA4BuD,WAA5B,CAA7B;AACD;AACF,OAJM,MAIA;AACL,YAAI,CAACqO,YAAL,EAAmB;AACjBC,gBAAM,CAAC7R,GAAD,CAAN,GAAcgL,QAAQ,CAAChL,GAAD,CAAtB;AACD;AACF;AACF;AACD,WAAO6R,MAAP;AACD,GA7BD,MA6BO,IAAIpS,IAAI,CAACuL,QAAD,CAAR,EAAoB;AACzBA,YAAQ,GAAGuG,eAAe,CAACC,UAAD,EAAaxG,QAAb,EAAuBzH,WAAvB,CAA1B;AACD;AACD,SAAOyH,QAAP;AACD;;AAED,SAASyG,kBAAT,CAA6BD,UAA7B,EAAyClQ,GAAzC,EAA8CiC,WAA9C,EAAoF,KAAzB0O,eAAyB,uEAAP,KAAO;AAClF,MAAIxS,IAAI,CAAC0R,SAAS,CAAC5N,WAAX,CAAR,EAAiC,CAAE;AACjCjC,OAAG,GAAG6P,SAAS,CAAC5N,WAAV,CAAsBiO,UAAtB,EAAkClQ,GAAlC,CAAN;AACD;AACD,SAAOoQ,WAAW,CAACF,UAAD,EAAalQ,GAAb,EAAkBiC,WAAlB,EAA+B,EAA/B,EAAmC0O,eAAnC,CAAlB;AACD;;AAED,SAASC,OAAT,CAAkBV,UAAlB,EAA8BlP,MAA9B,EAAsC;AACpC,MAAIvC,MAAM,CAACoR,SAAD,EAAYK,UAAZ,CAAV,EAAmC;AACjC,QAAMW,QAAQ,GAAGhB,SAAS,CAACK,UAAD,CAA1B;AACA,QAAI,CAACW,QAAL,EAAe,CAAE;AACf,aAAO,YAAY;AACjBJ,eAAO,CAACvT,KAAR,uEAAoDgT,UAApD;AACD,OAFD;AAGD;AACD,WAAO,UAAUY,IAAV,EAAgBC,IAAhB,EAAsB,CAAE;AAC7B,UAAInP,OAAO,GAAGiP,QAAd;AACA,UAAI1S,IAAI,CAAC0S,QAAD,CAAR,EAAoB;AAClBjP,eAAO,GAAGiP,QAAQ,CAACC,IAAD,CAAlB;AACD;;AAEDA,UAAI,GAAGV,WAAW,CAACF,UAAD,EAAaY,IAAb,EAAmBlP,OAAO,CAACiI,IAA3B,EAAiCjI,OAAO,CAACK,WAAzC,CAAlB;;AAEA,UAAM4H,IAAI,GAAG,CAACiH,IAAD,CAAb;AACA,UAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;AAC/BlH,YAAI,CAACvJ,IAAL,CAAUyQ,IAAV;AACD;AACD,UAAI5S,IAAI,CAACyD,OAAO,CAACC,IAAT,CAAR,EAAwB;AACtBqO,kBAAU,GAAGtO,OAAO,CAACC,IAAR,CAAaiP,IAAb,CAAb;AACD,OAFD,MAEO,IAAIzS,KAAK,CAACuD,OAAO,CAACC,IAAT,CAAT,EAAyB;AAC9BqO,kBAAU,GAAGtO,OAAO,CAACC,IAArB;AACD;AACD,UAAMI,WAAW,GAAGzF,EAAE,CAAC0T,UAAD,CAAF,CAAec,KAAf,CAAqBxU,EAArB,EAAyBqN,IAAzB,CAApB;AACA,UAAI5G,SAAS,CAACiN,UAAD,CAAb,EAA2B,CAAE;AAC3B,eAAOC,kBAAkB,CAACD,UAAD,EAAajO,WAAb,EAA0BL,OAAO,CAACK,WAAlC,EAA+Ce,YAAY,CAACkN,UAAD,CAA3D,CAAzB;AACD;AACD,aAAOjO,WAAP;AACD,KAtBD;AAuBD;AACD,SAAOjB,MAAP;AACD;;AAED,IAAMiQ,QAAQ,GAAGhT,MAAM,CAACa,MAAP,CAAc,IAAd,CAAjB;;AAEA,IAAMoS,KAAK,GAAG;AACZ,sBADY;AAEZ,eAFY;AAGZ,iBAHY;AAIZ,QAJY;AAKZ,SALY;AAMZ,OANY,CAAd;;;AASA,SAASC,aAAT,CAAwBtP,IAAxB,EAA8B;AAC5B,SAAO,SAASuP,OAAT;;;AAGJ,OAFDtN,IAEC,QAFDA,IAEC,CADDC,QACC,QADDA,QACC;AACD,QAAM/D,GAAG,GAAG;AACVqR,YAAM,YAAKxP,IAAL,2BAA0BA,IAA1B,oBADI,EAAZ;;AAGA1D,QAAI,CAAC2F,IAAD,CAAJ,IAAcA,IAAI,CAAC9D,GAAD,CAAlB;AACA7B,QAAI,CAAC4F,QAAD,CAAJ,IAAkBA,QAAQ,CAAC/D,GAAD,CAA1B;AACD,GATD;AAUD;;AAEDkR,KAAK,CAACzR,OAAN,CAAc,UAAUoC,IAAV,EAAgB;AAC5BoP,UAAQ,CAACpP,IAAD,CAAR,GAAiBsP,aAAa,CAACtP,IAAD,CAA9B;AACD,CAFD;;AAIA,IAAIyP,SAAS,GAAG;AACdC,OAAK,EAAE,CAAC,QAAD,CADO;AAEdC,OAAK,EAAE,CAAC,QAAD,CAFO;AAGdC,SAAO,EAAE,CAAC,OAAD,CAHK;AAIdnR,MAAI,EAAE,CAAC,QAAD,CAJQ,EAAhB;;;AAOA,SAASoR,WAAT;;;;;AAKG,KAJDC,OAIC,SAJDA,OAIC,CAHD9N,OAGC,SAHDA,OAGC,CAFDC,IAEC,SAFDA,IAEC,CADDC,QACC,SADDA,QACC;AACD,MAAI/D,GAAG,GAAG,KAAV;AACA,MAAIsR,SAAS,CAACK,OAAD,CAAb,EAAwB;AACtB3R,OAAG,GAAG;AACJqR,YAAM,EAAE,gBADJ;AAEJM,aAAO,EAAPA,OAFI;AAGJC,cAAQ,EAAEN,SAAS,CAACK,OAAD,CAHf,EAAN;;AAKAxT,QAAI,CAAC0F,OAAD,CAAJ,IAAiBA,OAAO,CAAC7D,GAAD,CAAxB;AACD,GAPD,MAOO;AACLA,OAAG,GAAG;AACJqR,YAAM,EAAE,oCADJ,EAAN;;AAGAlT,QAAI,CAAC2F,IAAD,CAAJ,IAAcA,IAAI,CAAC9D,GAAD,CAAlB;AACD;AACD7B,MAAI,CAAC4F,QAAD,CAAJ,IAAkBA,QAAQ,CAAC/D,GAAD,CAA1B;AACD;;AAED,IAAI6R,QAAQ,GAAG,aAAa5T,MAAM,CAAC8K,MAAP,CAAc;AACxCC,WAAS,EAAE,IAD6B;AAExC0I,aAAW,EAAEA,WAF2B,EAAd,CAA5B;;;AAKA,IAAMI,UAAU,GAAI,YAAY;AAC9B,MAAIC,OAAJ;AACA,SAAO,SAASC,aAAT,GAA0B;AAC/B,QAAI,CAACD,OAAL,EAAc;AACZA,aAAO,GAAG,IAAIxU,YAAJ,EAAV;AACD;AACD,WAAOwU,OAAP;AACD,GALD;AAMD,CARkB,EAAnB;;AAUA,SAASf,KAAT,CAAgBiB,GAAhB,EAAqBjR,MAArB,EAA6B6I,IAA7B,EAAmC;AACjC,SAAOoI,GAAG,CAACjR,MAAD,CAAH,CAAYgQ,KAAZ,CAAkBiB,GAAlB,EAAuBpI,IAAvB,CAAP;AACD;;AAED,SAASqI,GAAT,GAAgB;AACd,SAAOlB,KAAK,CAACc,UAAU,EAAX,EAAe,KAAf,6BAA0BK,SAA1B,EAAZ;AACD;AACD,SAASC,IAAT,GAAiB;AACf,SAAOpB,KAAK,CAACc,UAAU,EAAX,EAAe,MAAf,6BAA2BK,SAA3B,EAAZ;AACD;AACD,SAAS1L,KAAT,GAAkB;AAChB,SAAOuK,KAAK,CAACc,UAAU,EAAX,EAAe,OAAf,6BAA4BK,SAA5B,EAAZ;AACD;AACD,SAASE,KAAT,GAAkB;AAChB,SAAOrB,KAAK,CAACc,UAAU,EAAX,EAAe,OAAf,6BAA4BK,SAA5B,EAAZ;AACD;;AAED,IAAIG,QAAQ,GAAG,aAAarU,MAAM,CAAC8K,MAAP,CAAc;AACxCC,WAAS,EAAE,IAD6B;AAExCkJ,KAAG,EAAEA,GAFmC;AAGxCE,MAAI,EAAEA,IAHkC;AAIxC3L,OAAK,EAAEA,KAJiC;AAKxC4L,OAAK,EAAEA,KALiC,EAAd,CAA5B;;;AAQA;;;AAGA;;;AAGA,SAASE,QAAT,CAAmBnU,EAAnB,EAAuB;AACrB,SAAO,YAAY;AACjB,QAAI;AACF,aAAOA,EAAE,CAAC4S,KAAH,CAAS5S,EAAT,EAAa+T,SAAb,CAAP;AACD,KAFD,CAEE,OAAOK,CAAP,EAAU;AACV;AACA/B,aAAO,CAACvT,KAAR,CAAcsV,CAAd;AACD;AACF,GAPD;AAQD;;AAED,SAASC,eAAT,CAA0BlQ,MAA1B,EAAkC;AAChC,MAAMmQ,YAAY,GAAG,EAArB;AACA,OAAK,IAAM7Q,IAAX,IAAmBU,MAAnB,EAA2B;AACzB,QAAMoQ,KAAK,GAAGpQ,MAAM,CAACV,IAAD,CAApB;AACA,QAAI1D,IAAI,CAACwU,KAAD,CAAR,EAAiB;AACfD,kBAAY,CAAC7Q,IAAD,CAAZ,GAAqB0Q,QAAQ,CAACI,KAAD,CAA7B;AACA,aAAOpQ,MAAM,CAACV,IAAD,CAAb;AACD;AACF;AACD,SAAO6Q,YAAP;AACD;;AAED,IAAIE,GAAJ;AACA,IAAIC,SAAJ;AACA,IAAIC,OAAJ;;AAEA,SAASC,oBAAT,CAA+B5V,OAA/B,EAAwC;AACtC,MAAI;AACF,WAAOH,IAAI,CAACC,KAAL,CAAWE,OAAX,CAAP;AACD,GAFD,CAEE,OAAOqV,CAAP,EAAU,CAAE;AACd,SAAOrV,OAAP;AACD;;AAED,SAAS6V,kBAAT;AACEnJ,IADF;AAEE;AACA,MAAIA,IAAI,CAACoJ,IAAL,KAAc,SAAlB,EAA6B;AAC3BH,WAAO,GAAG,IAAV;AACD,GAFD,MAEO,IAAIjJ,IAAI,CAACoJ,IAAL,KAAc,UAAlB,EAA8B;AACnCL,OAAG,GAAG/I,IAAI,CAAC+I,GAAX;AACAC,aAAS,GAAGhJ,IAAI,CAACwH,MAAjB;AACA6B,6BAAyB,CAACN,GAAD,EAAM/I,IAAI,CAACwH,MAAX,CAAzB;AACD,GAJM,MAIA,IAAIxH,IAAI,CAACoJ,IAAL,KAAc,SAAlB,EAA6B;AAClC,QAAM9V,OAAO,GAAG;AACd8V,UAAI,EAAE,SADQ;AAEd9R,UAAI,EAAE4R,oBAAoB,CAAClJ,IAAI,CAAC1M,OAAN,CAFZ,EAAhB;;AAIA,SAAK,IAAIzB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyX,sBAAsB,CAAC9X,MAA3C,EAAmDK,CAAC,EAApD,EAAwD;AACtD,UAAMgG,QAAQ,GAAGyR,sBAAsB,CAACzX,CAAD,CAAvC;AACAgG,cAAQ,CAACvE,OAAD,CAAR;AACA;AACA,UAAIA,OAAO,CAACiW,OAAZ,EAAqB;AACnB;AACD;AACF;AACF,GAbM,MAaA,IAAIvJ,IAAI,CAACoJ,IAAL,KAAc,OAAlB,EAA2B;AAChCE,0BAAsB,CAAC1T,OAAvB,CAA+B,UAACiC,QAAD,EAAc;AAC3CA,cAAQ,CAAC;AACPuR,YAAI,EAAE,OADC;AAEP9R,YAAI,EAAE4R,oBAAoB,CAAClJ,IAAI,CAAC1M,OAAN,CAFnB,EAAD,CAAR;;AAID,KALD;AAMD;AACF;;AAED,IAAMkW,mBAAmB,GAAG,EAA5B;;AAEA,SAASH,yBAAT,CAAoCN,GAApC,EAAyCvB,MAAzC,EAAiD;AAC/CgC,qBAAmB,CAAC5T,OAApB,CAA4B,UAACiC,QAAD,EAAc;AACxCA,YAAQ,CAACkR,GAAD,EAAMvB,MAAN,CAAR;AACD,GAFD;AAGAgC,qBAAmB,CAAChY,MAApB,GAA6B,CAA7B;AACD;;AAED,SAASiY,eAAT,CAA0BzJ,IAA1B,EAAgC;AAC9B,MAAI,CAACvL,aAAa,CAACuL,IAAD,CAAlB,EAA0B;AACxBA,QAAI,GAAG,EAAP;AACD,GAH6B;;;;;AAQ1B4I,iBAAe,CAAC5I,IAAD,CARW,CAK5BhG,OAL4B,oBAK5BA,OAL4B,CAM5BC,IAN4B,oBAM5BA,IAN4B,CAO5BC,QAP4B,oBAO5BA,QAP4B;AAS9B,MAAMwP,UAAU,GAAGpV,IAAI,CAAC0F,OAAD,CAAvB;AACA,MAAM2P,OAAO,GAAGrV,IAAI,CAAC2F,IAAD,CAApB;AACA,MAAM2P,WAAW,GAAGtV,IAAI,CAAC4F,QAAD,CAAxB;AACAvC,SAAO,CAACC,OAAR,GAAkBJ,IAAlB,CAAuB,YAAM;AAC3B,QAAI,OAAOyR,OAAP,KAAmB,WAAvB,EAAoC;AAClCA,aAAO,GAAG,KAAV;AACAF,SAAG,GAAG,EAAN;AACAC,eAAS,GAAG,wBAAZ;AACD;AACDQ,uBAAmB,CAAC/S,IAApB,CAAyB,UAACsS,GAAD,EAAMvB,MAAN,EAAiB;AACxC,UAAIrR,GAAJ;AACA,UAAI4S,GAAJ,EAAS;AACP5S,WAAG,GAAG;AACJqR,gBAAM,EAAE,oBADJ;AAEJuB,aAAG,EAAHA,GAFI,EAAN;;AAIAW,kBAAU,IAAI1P,OAAO,CAAC7D,GAAD,CAArB;AACD,OAND,MAMO;AACLA,WAAG,GAAG;AACJqR,gBAAM,EAAE,0BAA0BA,MAAM,GAAG,MAAMA,MAAT,GAAkB,EAAlD,CADJ,EAAN;;AAGAmC,eAAO,IAAI1P,IAAI,CAAC9D,GAAD,CAAf;AACD;AACDyT,iBAAW,IAAI1P,QAAQ,CAAC/D,GAAD,CAAvB;AACD,KAfD;AAgBA,QAAI,OAAO4S,GAAP,KAAe,WAAnB,EAAgC;AAC9BM,+BAAyB,CAACN,GAAD,EAAMC,SAAN,CAAzB;AACD;AACF,GAzBD;AA0BD;;AAED,IAAMM,sBAAsB,GAAG,EAA/B;AACA;AACA,IAAMO,aAAa,GAAG,SAAhBA,aAAgB,CAACtV,EAAD,EAAQ;AAC5B,MAAI+U,sBAAsB,CAACxX,OAAvB,CAA+ByC,EAA/B,MAAuC,CAAC,CAA5C,EAA+C;AAC7C+U,0BAAsB,CAAC7S,IAAvB,CAA4BlC,EAA5B;AACD;AACF,CAJD;;AAMA,IAAMuV,cAAc,GAAG,SAAjBA,cAAiB,CAACvV,EAAD,EAAQ;AAC7B,MAAI,CAACA,EAAL,EAAS;AACP+U,0BAAsB,CAAC9X,MAAvB,GAAgC,CAAhC;AACD,GAFD,MAEO;AACL,QAAMoF,KAAK,GAAG0S,sBAAsB,CAACxX,OAAvB,CAA+ByC,EAA/B,CAAd;AACA,QAAIqC,KAAK,GAAG,CAAC,CAAb,EAAgB;AACd0S,4BAAsB,CAACzS,MAAvB,CAA8BD,KAA9B,EAAqC,CAArC;AACD;AACF;AACF,CATD;;AAWA,IAAI6B,GAAG,GAAG,aAAarE,MAAM,CAAC8K,MAAP,CAAc;AACnCC,WAAS,EAAE,IADwB;AAEnCsK,iBAAe,EAAEA,eAFkB;AAGnCI,eAAa,EAAEA,aAHoB;AAInCC,gBAAc,EAAEA,cAJmB;AAKnCX,oBAAkB,EAAEA,kBALe,EAAd,CAAvB;;;AAQA,IAAMY,MAAM,GAAGC,IAAf;AACA,IAAMC,WAAW,GAAGC,SAApB;;AAEA,IAAMC,WAAW,GAAG,IAApB;;AAEA,IAAMC,SAAS,GAAGrV,MAAM,CAAC,UAAC7D,GAAD,EAAS;AAChC,SAAOmE,QAAQ,CAACnE,GAAG,CAACE,OAAJ,CAAY+Y,WAAZ,EAAyB,GAAzB,CAAD,CAAf;AACD,CAFuB,CAAxB;;AAIA,SAASE,gBAAT,CAA2BC,UAA3B,EAAuC;AACrC,MAAMC,eAAe,GAAGD,UAAU,CAACE,YAAnC;AACA,MAAMC,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,KAAV,EAA0B,oCAAN1K,IAAM,6EAANA,IAAM;AAChD,WAAOuK,eAAe,CAACpD,KAAhB,CAAsBmD,UAAtB,GAAmCF,SAAS,CAACM,KAAD,CAA5C,SAAwD1K,IAAxD,EAAP;AACD,GAFD;AAGA,MAAI;AACF;AACAsK,cAAU,CAACE,YAAX,GAA0BC,eAA1B;AACD,GAHD,CAGE,OAAOpX,KAAP,EAAc;AACdiX,cAAU,CAACK,aAAX,GAA2BF,eAA3B;AACD;AACF;;AAED,SAASG,QAAT,CAAmB5S,IAAnB,EAAyBD,OAAzB,EAAkC8S,WAAlC,EAA+C;AAC7C,MAAMC,OAAO,GAAG/S,OAAO,CAACC,IAAD,CAAvB;AACA,MAAI,CAAC8S,OAAL,EAAc;AACZ/S,WAAO,CAACC,IAAD,CAAP,GAAgB,YAAY;AAC1BqS,sBAAgB,CAAC,IAAD,CAAhB;AACD,KAFD;AAGD,GAJD,MAIO;AACLtS,WAAO,CAACC,IAAD,CAAP,GAAgB,YAAmB;AACjCqS,sBAAgB,CAAC,IAAD,CAAhB,CADiC,mCAANrK,IAAM,yDAANA,IAAM;AAEjC,aAAO8K,OAAO,CAAC3D,KAAR,CAAc,IAAd,EAAoBnH,IAApB,CAAP;AACD,KAHD;AAID;AACF;AACD,IAAI,CAAC+J,MAAM,CAACgB,YAAZ,EAA0B;AACxBhB,QAAM,CAACgB,YAAP,GAAsB,IAAtB;AACAf,MAAI,GAAG,gBAAwB,KAAdjS,OAAc,uEAAJ,EAAI;AAC7B6S,YAAQ,CAAC,QAAD,EAAW7S,OAAX,CAAR;AACA,WAAOgS,MAAM,CAAChS,OAAD,CAAb;AACD,GAHD;AAIAiS,MAAI,CAACgB,KAAL,GAAajB,MAAM,CAACiB,KAApB;;AAEAd,WAAS,GAAG,qBAAwB,KAAdnS,OAAc,uEAAJ,EAAI;AAClC6S,YAAQ,CAAC,SAAD,EAAY7S,OAAZ,CAAR;AACA,WAAOkS,WAAW,CAAClS,OAAD,CAAlB;AACD,GAHD;AAID;;AAED,IAAMkT,gBAAgB,GAAG;AACvB,mBADuB;AAEvB,eAFuB;AAGvB,kBAHuB;AAIvB,iBAJuB;AAKvB,mBALuB;AAMvB,cANuB;AAOvB,UAPuB;AAQvB,cARuB,CAAzB;;;AAWA,SAASC,SAAT,CAAoBC,EAApB,EAAwBC,KAAxB,EAA+B;AAC7B,MAAMd,UAAU,GAAGa,EAAE,CAACE,GAAH,CAAOF,EAAE,CAACG,MAAV,CAAnB;AACAF,OAAK,CAACxV,OAAN,CAAc,UAAA2V,IAAI,EAAI;AACpB,QAAI3W,MAAM,CAAC0V,UAAD,EAAaiB,IAAb,CAAV,EAA8B;AAC5BJ,QAAE,CAACI,IAAD,CAAF,GAAWjB,UAAU,CAACiB,IAAD,CAArB;AACD;AACF,GAJD;AAKD;;AAED,SAASC,OAAT,CAAkB7U,IAAlB,EAAwB8U,UAAxB,EAAoC;AAClC,MAAI,CAACA,UAAL,EAAiB;AACf,WAAO,IAAP;AACD;;AAED,MAAI/X,aAAIqE,OAAJ,IAAe1B,KAAK,CAACC,OAAN,CAAc5C,aAAIqE,OAAJ,CAAYpB,IAAZ,CAAd,CAAnB,EAAqD;AACnD,WAAO,IAAP;AACD;;AAED8U,YAAU,GAAGA,UAAU,CAACC,OAAX,IAAsBD,UAAnC;;AAEA,MAAInX,IAAI,CAACmX,UAAD,CAAR,EAAsB;AACpB,QAAInX,IAAI,CAACmX,UAAU,CAACE,aAAX,CAAyBhV,IAAzB,CAAD,CAAR,EAA0C;AACxC,aAAO,IAAP;AACD;AACD,QAAI8U,UAAU,CAACG,KAAX;AACFH,cAAU,CAACG,KAAX,CAAiB7T,OADf;AAEF1B,SAAK,CAACC,OAAN,CAAcmV,UAAU,CAACG,KAAX,CAAiB7T,OAAjB,CAAyBpB,IAAzB,CAAd,CAFF,EAEiD;AAC/C,aAAO,IAAP;AACD;AACD,WAAO,KAAP;AACD;;AAED,MAAIrC,IAAI,CAACmX,UAAU,CAAC9U,IAAD,CAAX,CAAR,EAA4B;AAC1B,WAAO,IAAP;AACD;AACD,MAAMkV,MAAM,GAAGJ,UAAU,CAACI,MAA1B;AACA,MAAIxV,KAAK,CAACC,OAAN,CAAcuV,MAAd,CAAJ,EAA2B;AACzB,WAAO,CAAC,CAACA,MAAM,CAAC9N,IAAP,CAAY,UAAAxB,KAAK,UAAIiP,OAAO,CAAC7U,IAAD,EAAO4F,KAAP,CAAX,EAAjB,CAAT;AACD;AACF;;AAED,SAASuP,SAAT,CAAoBC,SAApB,EAA+BvV,KAA/B,EAAsCiV,UAAtC,EAAkD;AAChDjV,OAAK,CAACZ,OAAN,CAAc,UAAAe,IAAI,EAAI;AACpB,QAAI6U,OAAO,CAAC7U,IAAD,EAAO8U,UAAP,CAAX,EAA+B;AAC7BM,eAAS,CAACpV,IAAD,CAAT,GAAkB,UAAUqJ,IAAV,EAAgB;AAChC,eAAO,KAAKvB,GAAL,IAAY,KAAKA,GAAL,CAASuN,WAAT,CAAqBrV,IAArB,EAA2BqJ,IAA3B,CAAnB;AACD,OAFD;AAGD;AACF,GAND;AAOD;;AAED,SAASiM,gBAAT,CAA2BvY,GAA3B,EAAgC+X,UAAhC,EAA4C;AAC1CA,YAAU,GAAGA,UAAU,CAACC,OAAX,IAAsBD,UAAnC;AACA,MAAIS,YAAJ;AACA,MAAI5X,IAAI,CAACmX,UAAD,CAAR,EAAsB;AACpBS,gBAAY,GAAGT,UAAf;AACD,GAFD,MAEO;AACLS,gBAAY,GAAGxY,GAAG,CAACyY,MAAJ,CAAWV,UAAX,CAAf;AACD;AACDA,YAAU,GAAGS,YAAY,CAACnU,OAA1B;AACA,SAAO,CAACmU,YAAD,EAAeT,UAAf,CAAP;AACD;;AAED,SAASW,SAAT,CAAoBjB,EAApB,EAAwBkB,QAAxB,EAAkC;AAChC,MAAIhW,KAAK,CAACC,OAAN,CAAc+V,QAAd,KAA2BA,QAAQ,CAAC7a,MAAxC,EAAgD;AAC9C,QAAM8a,MAAM,GAAGlY,MAAM,CAACa,MAAP,CAAc,IAAd,CAAf;AACAoX,YAAQ,CAACzW,OAAT,CAAiB,UAAA2W,QAAQ,EAAI;AAC3BD,YAAM,CAACC,QAAD,CAAN,GAAmB,IAAnB;AACD,KAFD;AAGApB,MAAE,CAACqB,YAAH,GAAkBrB,EAAE,CAACmB,MAAH,GAAYA,MAA9B;AACD;AACF;;AAED,SAASG,UAAT,CAAqBC,MAArB,EAA6BpC,UAA7B,EAAyC;AACvCoC,QAAM,GAAG,CAACA,MAAM,IAAI,EAAX,EAAeva,KAAf,CAAqB,GAArB,CAAT;AACA,MAAMqN,GAAG,GAAGkN,MAAM,CAAClb,MAAnB;;AAEA,MAAIgO,GAAG,KAAK,CAAZ,EAAe;AACb8K,cAAU,CAACqC,OAAX,GAAqBD,MAAM,CAAC,CAAD,CAA3B;AACD,GAFD,MAEO,IAAIlN,GAAG,KAAK,CAAZ,EAAe;AACpB8K,cAAU,CAACqC,OAAX,GAAqBD,MAAM,CAAC,CAAD,CAA3B;AACApC,cAAU,CAACsC,QAAX,GAAsBF,MAAM,CAAC,CAAD,CAA5B;AACD;AACF;;AAED,SAASG,QAAT,CAAmBpB,UAAnB,EAA+BqB,OAA/B,EAAwC;AACtC,MAAIxV,IAAI,GAAGmU,UAAU,CAACnU,IAAX,IAAmB,EAA9B;AACA,MAAMuF,OAAO,GAAG4O,UAAU,CAAC5O,OAAX,IAAsB,EAAtC;;AAEA,MAAI,OAAOvF,IAAP,KAAgB,UAApB,EAAgC;AAC9B,QAAI;AACFA,UAAI,GAAGA,IAAI,CAAC3C,IAAL,CAAUmY,OAAV,CAAP,CADE,CACyB;AAC5B,KAFD,CAEE,OAAOnE,CAAP,EAAU;AACV,UAAI1F,uGAAA,CAAY8J,aAAhB,EAA+B;AAC7BnG,eAAO,CAACC,IAAR,CAAa,wEAAb,EAAuFvP,IAAvF;AACD;AACF;AACF,GARD,MAQO;AACL,QAAI;AACF;AACAA,UAAI,GAAGnE,IAAI,CAACC,KAAL,CAAWD,IAAI,CAAC6Z,SAAL,CAAe1V,IAAf,CAAX,CAAP;AACD,KAHD,CAGE,OAAOqR,CAAP,EAAU,CAAE;AACf;;AAED,MAAI,CAAClU,aAAa,CAAC6C,IAAD,CAAlB,EAA0B;AACxBA,QAAI,GAAG,EAAP;AACD;;AAEDlD,QAAM,CAACsB,IAAP,CAAYmH,OAAZ,EAAqBjH,OAArB,CAA6B,UAAAyQ,UAAU,EAAI;AACzC,QAAIyG,OAAO,CAACG,mBAAR,CAA4Bnb,OAA5B,CAAoCuU,UAApC,MAAoD,CAAC,CAArD,IAA0D,CAACzR,MAAM,CAAC0C,IAAD,EAAO+O,UAAP,CAArE,EAAyF;AACvF/O,UAAI,CAAC+O,UAAD,CAAJ,GAAmBxJ,OAAO,CAACwJ,UAAD,CAA1B;AACD;AACF,GAJD;;AAMA,SAAO/O,IAAP;AACD;;AAED,IAAM4V,UAAU,GAAG,CAAC/b,MAAD,EAAS8J,MAAT,EAAiBkS,OAAjB,EAA0B/Y,MAA1B,EAAkCiC,KAAlC,EAAyC,IAAzC,CAAnB;;AAEA,SAAS+W,cAAT,CAAyBpV,IAAzB,EAA+B;AAC7B,SAAO,SAASqV,QAAT,CAAmBC,MAAnB,EAA2BC,MAA3B,EAAmC;AACxC,QAAI,KAAK9O,GAAT,EAAc;AACZ,WAAKA,GAAL,CAASzG,IAAT,IAAiBsV,MAAjB,CADY,CACa;AAC1B;AACF,GAJD;AAKD;;AAED,SAASE,aAAT,CAAwB/B,UAAxB,EAAoCgC,YAApC,EAAkD;AAChD,MAAMC,YAAY,GAAGjC,UAAU,CAACkC,SAAhC;AACA,MAAMC,UAAU,GAAGnC,UAAU,CAACoC,OAA9B;AACA,MAAMC,SAAS,GAAGrC,UAAU,CAACI,MAA7B;;AAEA,MAAIkC,QAAQ,GAAGtC,UAAU,CAACuC,KAA1B;;AAEA,MAAI,CAACD,QAAL,EAAe;AACbtC,cAAU,CAACuC,KAAX,GAAmBD,QAAQ,GAAG,EAA9B;AACD;;AAED,MAAMJ,SAAS,GAAG,EAAlB;AACA,MAAItX,KAAK,CAACC,OAAN,CAAcoX,YAAd,CAAJ,EAAiC;AAC/BA,gBAAY,CAAC9X,OAAb,CAAqB,UAAAqY,QAAQ,EAAI;AAC/BN,eAAS,CAAClX,IAAV,CAAewX,QAAQ,CAAC7c,OAAT,CAAiB,QAAjB,EAA8B,IAA9B,eAAf;AACA,UAAI6c,QAAQ,KAAK,kBAAjB,EAAqC;AACnC,YAAI5X,KAAK,CAACC,OAAN,CAAcyX,QAAd,CAAJ,EAA6B;AAC3BA,kBAAQ,CAACtX,IAAT,CAAc,MAAd;AACAsX,kBAAQ,CAACtX,IAAT,CAAc,OAAd;AACD,SAHD,MAGO;AACLsX,kBAAQ,CAAC/V,IAAT,GAAgB;AACdoR,gBAAI,EAAEjY,MADQ;AAEdua,mBAAO,EAAE,EAFK,EAAhB;;AAIAqC,kBAAQ,CAACnU,KAAT,GAAiB;AACfwP,gBAAI,EAAE,CAACjY,MAAD,EAAS8J,MAAT,EAAiBkS,OAAjB,EAA0B9W,KAA1B,EAAiCjC,MAAjC,EAAyCH,IAAzC,CADS;AAEfyX,mBAAO,EAAE,EAFM,EAAjB;;AAID;AACF;AACF,KAjBD;AAkBD;AACD,MAAIjX,aAAa,CAACmZ,UAAD,CAAb,IAA6BA,UAAU,CAACI,KAA5C,EAAmD;AACjDL,aAAS,CAAClX,IAAV;AACEgX,gBAAY,CAAC;AACXS,gBAAU,EAAEC,cAAc,CAACP,UAAU,CAACI,KAAZ,EAAmB,IAAnB,CADf,EAAD,CADd;;;AAKD;AACD,MAAI3X,KAAK,CAACC,OAAN,CAAcwX,SAAd,CAAJ,EAA8B;AAC5BA,aAAS,CAAClY,OAAV,CAAkB,UAAAwY,QAAQ,EAAI;AAC5B,UAAI3Z,aAAa,CAAC2Z,QAAD,CAAb,IAA2BA,QAAQ,CAACJ,KAAxC,EAA+C;AAC7CL,iBAAS,CAAClX,IAAV;AACEgX,oBAAY,CAAC;AACXS,oBAAU,EAAEC,cAAc,CAACC,QAAQ,CAACJ,KAAV,EAAiB,IAAjB,CADf,EAAD,CADd;;;AAKD;AACF,KARD;AASD;AACD,SAAOL,SAAP;AACD;;AAED,SAASU,aAAT,CAAwBxZ,GAAxB,EAA6BuU,IAA7B,EAAmCkF,YAAnC,EAAiDC,IAAjD,EAAuD;AACrD;AACA,MAAIlY,KAAK,CAACC,OAAN,CAAc8S,IAAd,KAAuBA,IAAI,CAAC5X,MAAL,KAAgB,CAA3C,EAA8C;AAC5C,WAAO4X,IAAI,CAAC,CAAD,CAAX;AACD;AACD,SAAOA,IAAP;AACD;;AAED,SAAS+E,cAAT,CAAyBH,KAAzB,EAAwE,KAAxCQ,UAAwC,uEAA3B,KAA2B,KAApBD,IAAoB,uEAAb,EAAa,KAATxW,OAAS;AACtE,MAAMmW,UAAU,GAAG,EAAnB;AACA,MAAI,CAACM,UAAL,EAAiB;AACfN,cAAU,CAACO,KAAX,GAAmB;AACjBrF,UAAI,EAAEjY,MADW;AAEjByI,WAAK,EAAE,EAFU,EAAnB;;AAIA;AACE,UAAK7B,OAAO,CAAC2W,WAAb,EAA0B;AACxBR,kBAAU,CAACS,gBAAX,GAA8B;AAC5BvF,cAAI,EAAE,IADsB;AAE5BxP,eAAK,EAAE,EAFqB,EAA9B;;AAIAsU,kBAAU,CAACU,gBAAX,GAA8B;AAC5BxF,cAAI,EAAE,IADsB;AAE5BxP,eAAK,EAAE,EAFqB,EAA9B;;AAID;AACF;AACD;AACAsU,cAAU,CAACW,mBAAX,GAAiC;AAC/BzF,UAAI,EAAEjY,MADyB;AAE/ByI,WAAK,EAAE,EAFwB,EAAjC;;AAIAsU,cAAU,CAAC7B,QAAX,GAAsB,EAAE;AACtBjD,UAAI,EAAE,IADc;AAEpBxP,WAAK,EAAE,EAFa;AAGpByT,cAAQ,EAAE,kBAAUC,MAAV,EAAkBC,MAAlB,EAA0B;AAClC,YAAMjB,MAAM,GAAGlY,MAAM,CAACa,MAAP,CAAc,IAAd,CAAf;AACAqY,cAAM,CAAC1X,OAAP,CAAe,UAAA2W,QAAQ,EAAI;AACzBD,gBAAM,CAACC,QAAD,CAAN,GAAmB,IAAnB;AACD,SAFD;AAGA,aAAKuC,OAAL,CAAa;AACXxC,gBAAM,EAANA,MADW,EAAb;;AAGD,OAXmB,EAAtB;;AAaD;AACD,MAAIjW,KAAK,CAACC,OAAN,CAAc0X,KAAd,CAAJ,EAA0B,CAAE;AAC1BA,SAAK,CAACpY,OAAN,CAAc,UAAAf,GAAG,EAAI;AACnBqZ,gBAAU,CAACrZ,GAAD,CAAV,GAAkB;AAChBuU,YAAI,EAAE,IADU;AAEhBiE,gBAAQ,EAAED,cAAc,CAACvY,GAAD,CAFR,EAAlB;;AAID,KALD;AAMD,GAPD,MAOO,IAAIJ,aAAa,CAACuZ,KAAD,CAAjB,EAA0B,CAAE;AACjC5Z,UAAM,CAACsB,IAAP,CAAYsY,KAAZ,EAAmBpY,OAAnB,CAA2B,UAAAf,GAAG,EAAI;AAChC,UAAMka,IAAI,GAAGf,KAAK,CAACnZ,GAAD,CAAlB;AACA,UAAIJ,aAAa,CAACsa,IAAD,CAAjB,EAAyB,CAAE;AACzB,YAAInV,KAAK,GAAGmV,IAAI,CAACrD,OAAjB;AACA,YAAIpX,IAAI,CAACsF,KAAD,CAAR,EAAiB;AACfA,eAAK,GAAGA,KAAK,EAAb;AACD;;AAEDmV,YAAI,CAAC3F,IAAL,GAAYiF,aAAa,CAACxZ,GAAD,EAAMka,IAAI,CAAC3F,IAAX,CAAzB;;AAEA8E,kBAAU,CAACrZ,GAAD,CAAV,GAAkB;AAChBuU,cAAI,EAAE8D,UAAU,CAACpb,OAAX,CAAmBid,IAAI,CAAC3F,IAAxB,MAAkC,CAAC,CAAnC,GAAuC2F,IAAI,CAAC3F,IAA5C,GAAmD,IADzC;AAEhBxP,eAAK,EAALA,KAFgB;AAGhByT,kBAAQ,EAAED,cAAc,CAACvY,GAAD,CAHR,EAAlB;;AAKD,OAbD,MAaO,CAAE;AACP,YAAMuU,IAAI,GAAGiF,aAAa,CAACxZ,GAAD,EAAMka,IAAN,CAA1B;AACAb,kBAAU,CAACrZ,GAAD,CAAV,GAAkB;AAChBuU,cAAI,EAAE8D,UAAU,CAACpb,OAAX,CAAmBsX,IAAnB,MAA6B,CAAC,CAA9B,GAAkCA,IAAlC,GAAyC,IAD/B;AAEhBiE,kBAAQ,EAAED,cAAc,CAACvY,GAAD,CAFR,EAAlB;;AAID;AACF,KAtBD;AAuBD;AACD,SAAOqZ,UAAP;AACD;;AAED,SAASc,SAAT,CAAoBtE,KAApB,EAA2B;AACzB;AACA,MAAI;AACFA,SAAK,CAACuE,EAAN,GAAW9b,IAAI,CAACC,KAAL,CAAWD,IAAI,CAAC6Z,SAAL,CAAetC,KAAf,CAAX,CAAX;AACD,GAFD,CAEE,OAAO/B,CAAP,EAAU,CAAE;;AAEd+B,OAAK,CAACwE,eAAN,GAAwBpa,IAAxB;AACA4V,OAAK,CAACyE,cAAN,GAAuBra,IAAvB;;AAEA4V,OAAK,CAAC0E,MAAN,GAAe1E,KAAK,CAAC0E,MAAN,IAAgB,EAA/B;;AAEA,MAAI,CAACxa,MAAM,CAAC8V,KAAD,EAAQ,QAAR,CAAX,EAA8B;AAC5BA,SAAK,CAAC2E,MAAN,GAAe,EAAf;AACD;;AAED,MAAIza,MAAM,CAAC8V,KAAD,EAAQ,UAAR,CAAV,EAA+B;AAC7BA,SAAK,CAAC2E,MAAN,GAAe,OAAO3E,KAAK,CAAC2E,MAAb,KAAwB,QAAxB,GAAmC3E,KAAK,CAAC2E,MAAzC,GAAkD,EAAjE;AACA3E,SAAK,CAAC2E,MAAN,CAAaC,QAAb,GAAwB5E,KAAK,CAAC4E,QAA9B;AACD;;AAED,MAAI7a,aAAa,CAACiW,KAAK,CAAC2E,MAAP,CAAjB,EAAiC;AAC/B3E,SAAK,CAAC0E,MAAN,GAAehb,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBuQ,KAAK,CAAC0E,MAAxB,EAAgC1E,KAAK,CAAC2E,MAAtC,CAAf;AACD;;AAED,SAAO3E,KAAP;AACD;;AAED,SAAS6E,aAAT,CAAwBpE,EAAxB,EAA4BqE,cAA5B,EAA4C;AAC1C,MAAI1C,OAAO,GAAG3B,EAAd;AACAqE,gBAAc,CAAC5Z,OAAf,CAAuB,UAAA6Z,aAAa,EAAI;AACtC,QAAMC,QAAQ,GAAGD,aAAa,CAAC,CAAD,CAA9B;AACA,QAAM7V,KAAK,GAAG6V,aAAa,CAAC,CAAD,CAA3B;AACA,QAAIC,QAAQ,IAAI,OAAO9V,KAAP,KAAiB,WAAjC,EAA8C,CAAE;AAC9C,UAAM+V,QAAQ,GAAGF,aAAa,CAAC,CAAD,CAA9B;AACA,UAAMG,SAAS,GAAGH,aAAa,CAAC,CAAD,CAA/B;;AAEA,UAAII,IAAJ;AACA,UAAI5U,MAAM,CAAC6U,SAAP,CAAiBJ,QAAjB,CAAJ,EAAgC;AAC9BG,YAAI,GAAGH,QAAP;AACD,OAFD,MAEO,IAAI,CAACA,QAAL,EAAe;AACpBG,YAAI,GAAG/C,OAAP;AACD,OAFM,MAEA,IAAI,OAAO4C,QAAP,KAAoB,QAApB,IAAgCA,QAApC,EAA8C;AACnD,YAAIA,QAAQ,CAAC5d,OAAT,CAAiB,KAAjB,MAA4B,CAAhC,EAAmC;AACjC+d,cAAI,GAAGH,QAAQ,CAACK,MAAT,CAAgB,CAAhB,CAAP;AACD,SAFD,MAEO;AACLF,cAAI,GAAG1E,EAAE,CAAC6E,WAAH,CAAeN,QAAf,EAAyB5C,OAAzB,CAAP;AACD;AACF;;AAED,UAAI7R,MAAM,CAAC6U,SAAP,CAAiBD,IAAjB,CAAJ,EAA4B;AAC1B/C,eAAO,GAAGlT,KAAV;AACD,OAFD,MAEO,IAAI,CAAC+V,QAAL,EAAe;AACpB7C,eAAO,GAAG+C,IAAI,CAACjW,KAAD,CAAd;AACD,OAFM,MAEA;AACL,YAAIvD,KAAK,CAACC,OAAN,CAAcuZ,IAAd,CAAJ,EAAyB;AACvB/C,iBAAO,GAAG+C,IAAI,CAAC9R,IAAL,CAAU,UAAAkS,QAAQ,EAAI;AAC9B,mBAAO9E,EAAE,CAAC6E,WAAH,CAAeL,QAAf,EAAyBM,QAAzB,MAAuCrW,KAA9C;AACD,WAFS,CAAV;AAGD,SAJD,MAIO,IAAInF,aAAa,CAACob,IAAD,CAAjB,EAAyB;AAC9B/C,iBAAO,GAAG1Y,MAAM,CAACsB,IAAP,CAAYma,IAAZ,EAAkB9R,IAAlB,CAAuB,UAAAmS,OAAO,EAAI;AAC1C,mBAAO/E,EAAE,CAAC6E,WAAH,CAAeL,QAAf,EAAyBE,IAAI,CAACK,OAAD,CAA7B,MAA4CtW,KAAnD;AACD,WAFS,CAAV;AAGD,SAJM,MAIA;AACLgN,iBAAO,CAACvT,KAAR,CAAc,iBAAd,EAAiCwc,IAAjC;AACD;AACF;;AAED,UAAID,SAAJ,EAAe;AACb9C,eAAO,GAAG3B,EAAE,CAAC6E,WAAH,CAAeJ,SAAf,EAA0B9C,OAA1B,CAAV;AACD;AACF;AACF,GA1CD;AA2CA,SAAOA,OAAP;AACD;;AAED,SAASqD,iBAAT,CAA4BhF,EAA5B,EAAgCiF,KAAhC,EAAuC1F,KAAvC,EAA8C;AAC5C,MAAM2F,QAAQ,GAAG,EAAjB;;AAEA,MAAIha,KAAK,CAACC,OAAN,CAAc8Z,KAAd,KAAwBA,KAAK,CAAC5e,MAAlC,EAA0C;AACxC;;;;;;;;;;;AAWA4e,SAAK,CAACxa,OAAN,CAAc,UAAC8Z,QAAD,EAAW9Y,KAAX,EAAqB;AACjC,UAAI,OAAO8Y,QAAP,KAAoB,QAAxB,EAAkC;AAChC,YAAI,CAACA,QAAL,EAAe,CAAE;AACfW,kBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwBuU,EAAxB;AACD,SAFD,MAEO;AACL,cAAIuE,QAAQ,KAAK,QAAjB,EAA2B,CAAE;AAC3BW,oBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwB8T,KAAxB;AACD,WAFD,MAEO,IAAIgF,QAAQ,KAAK,WAAjB,EAA8B;AACnC,gBAAIhF,KAAK,CAAC2E,MAAN,IAAgB3E,KAAK,CAAC2E,MAAN,CAAaiB,QAAjC,EAA2C;AACzCD,sBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwB8T,KAAK,CAAC2E,MAAN,CAAaiB,QAArC;AACD,aAFD,MAEO;AACLD,sBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwB,CAAC8T,KAAD,CAAxB;AACD;AACF,WANM,MAMA,IAAIgF,QAAQ,CAAC5d,OAAT,CAAiB,SAAjB,MAAgC,CAApC,EAAuC,CAAE;AAC9Cue,oBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwBuU,EAAE,CAAC6E,WAAH,CAAeN,QAAQ,CAACte,OAAT,CAAiB,SAAjB,EAA4B,EAA5B,CAAf,EAAgDsZ,KAAhD,CAAxB;AACD,WAFM,MAEA;AACL2F,oBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwBuU,EAAE,CAAC6E,WAAH,CAAeN,QAAf,CAAxB;AACD;AACF;AACF,OAlBD,MAkBO;AACLW,gBAAQ,CAAC,MAAMzZ,KAAP,CAAR,GAAwB2Y,aAAa,CAACpE,EAAD,EAAKuE,QAAL,CAArC;AACD;AACF,KAtBD;AAuBD;;AAED,SAAOW,QAAP;AACD;;AAED,SAASE,aAAT,CAAwBC,GAAxB,EAA6B;AAC3B,MAAM9b,GAAG,GAAG,EAAZ;AACA,OAAK,IAAI7C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2e,GAAG,CAAChf,MAAxB,EAAgCK,CAAC,EAAjC,EAAqC;AACnC,QAAM4e,OAAO,GAAGD,GAAG,CAAC3e,CAAD,CAAnB;AACA6C,OAAG,CAAC+b,OAAO,CAAC,CAAD,CAAR,CAAH,GAAkBA,OAAO,CAAC,CAAD,CAAzB;AACD;AACD,SAAO/b,GAAP;AACD;;AAED,SAASgc,gBAAT,CAA2BvF,EAA3B,EAA+BT,KAA/B,EAAmF,KAA7C1K,IAA6C,uEAAtC,EAAsC,KAAlCoQ,KAAkC,uEAA1B,EAA0B,KAAtBO,QAAsB,uDAAZtK,UAAY;AACjF,MAAIuK,eAAe,GAAG,KAAtB,CADiF,CACpD;AAC7B,MAAID,QAAJ,EAAc,CAAE;AACdC,mBAAe,GAAGlG,KAAK,CAACmG,aAAN;AAChBnG,SAAK,CAACmG,aAAN,CAAoBC,OADJ;AAEhBpG,SAAK,CAACmG,aAAN,CAAoBC,OAApB,CAA4BC,OAA5B,KAAwC,IAF1C;AAGA,QAAI,CAAC/Q,IAAI,CAACxO,MAAV,EAAkB,CAAE;AAClB,UAAIof,eAAJ,EAAqB;AACnB,eAAO,CAAClG,KAAD,CAAP;AACD;AACD,aAAOA,KAAK,CAAC2E,MAAN,CAAaiB,QAAb,IAAyB5F,KAAK,CAAC2E,MAAtC;AACD;AACF;;AAED,MAAMgB,QAAQ,GAAGF,iBAAiB,CAAChF,EAAD,EAAKiF,KAAL,EAAY1F,KAAZ,CAAlC;;AAEA,MAAMsG,GAAG,GAAG,EAAZ;AACAhR,MAAI,CAACpK,OAAL,CAAa,UAAAqb,GAAG,EAAI;AAClB,QAAIA,GAAG,KAAK,QAAZ,EAAsB;AACpB,UAAI5K,UAAU,KAAK,aAAf,IAAgC,CAACsK,QAArC,EAA+C,CAAE;AAC/CK,WAAG,CAACva,IAAJ,CAASiU,KAAK,CAAC0E,MAAN,CAAaxV,KAAtB;AACD,OAFD,MAEO;AACL,YAAI+W,QAAQ,IAAI,CAACC,eAAjB,EAAkC;AAChCI,aAAG,CAACva,IAAJ,CAASiU,KAAK,CAAC2E,MAAN,CAAaiB,QAAb,CAAsB,CAAtB,CAAT;AACD,SAFD,MAEO,CAAE;AACPU,aAAG,CAACva,IAAJ,CAASiU,KAAT;AACD;AACF;AACF,KAVD,MAUO;AACL,UAAIrU,KAAK,CAACC,OAAN,CAAc2a,GAAd,KAAsBA,GAAG,CAAC,CAAD,CAAH,KAAW,GAArC,EAA0C;AACxCD,WAAG,CAACva,IAAJ,CAAS8Z,aAAa,CAACU,GAAD,CAAtB;AACD,OAFD,MAEO,IAAI,OAAOA,GAAP,KAAe,QAAf,IAA2Brc,MAAM,CAACyb,QAAD,EAAWY,GAAX,CAArC,EAAsD;AAC3DD,WAAG,CAACva,IAAJ,CAAS4Z,QAAQ,CAACY,GAAD,CAAjB;AACD,OAFM,MAEA;AACLD,WAAG,CAACva,IAAJ,CAASwa,GAAT;AACD;AACF;AACF,GApBD;;AAsBA,SAAOD,GAAP;AACD;;AAED,IAAME,IAAI,GAAG,GAAb;AACA,IAAMC,MAAM,GAAG,GAAf;;AAEA,SAASC,gBAAT,CAA2BC,SAA3B,EAAsCC,OAAtC,EAA+C;AAC7C,SAAQD,SAAS,KAAKC,OAAf;;AAEHA,SAAO,KAAK,cAAZ;;AAEED,WAAS,KAAK,OAAd;AACAA,WAAS,KAAK,KAHhB,CAFJ;;;AAQD;;AAED,SAASE,YAAT,CAAuBpG,EAAvB,EAA2B;AACzB,MAAIqG,OAAO,GAAGrG,EAAE,CAACqG,OAAjB;AACA;AACA,SAAOA,OAAO,IAAIA,OAAO,CAACA,OAAnB,KAA+BA,OAAO,CAACC,QAAR,CAAiBC,OAAjB,IAA4BF,OAAO,CAACA,OAAR,CAAgBC,QAAhB,CAAyBC,OAArD,IAAgEF,OAAO,CAACG,MAAR,CAAe/E,QAA9G,CAAP,EAAgI;AAC9H4E,WAAO,GAAGA,OAAO,CAACA,OAAlB;AACD;AACD,SAAOA,OAAO,IAAIA,OAAO,CAACA,OAA1B;AACD;;AAED,SAASI,WAAT,CAAsBlH,KAAtB,EAA6B;AAC3BA,OAAK,GAAGsE,SAAS,CAACtE,KAAD,CAAjB;;AAEA;AACA,MAAMoG,OAAO,GAAG,CAACpG,KAAK,CAACmG,aAAN,IAAuBnG,KAAK,CAAC0E,MAA9B,EAAsC0B,OAAtD;AACA,MAAI,CAACA,OAAL,EAAc;AACZ,WAAOlK,OAAO,CAACC,IAAR,CAAa,SAAb,CAAP;AACD;AACD,MAAMgL,SAAS,GAAGf,OAAO,CAACe,SAAR,IAAqBf,OAAO,CAAC,YAAD,CAA9C,CAR2B,CAQmC;AAC9D,MAAI,CAACe,SAAL,EAAgB;AACd,WAAOjL,OAAO,CAACC,IAAR,CAAa,SAAb,CAAP;AACD;;AAED;AACA,MAAMwK,SAAS,GAAG3G,KAAK,CAACtB,IAAxB;;AAEA,MAAM4H,GAAG,GAAG,EAAZ;;AAEAa,WAAS,CAACjc,OAAV,CAAkB,UAAAkc,QAAQ,EAAI;AAC5B,QAAI1I,IAAI,GAAG0I,QAAQ,CAAC,CAAD,CAAnB;AACA,QAAMC,WAAW,GAAGD,QAAQ,CAAC,CAAD,CAA5B;;AAEA,QAAMnB,QAAQ,GAAGvH,IAAI,CAACrX,MAAL,CAAY,CAAZ,MAAmBof,MAApC;AACA/H,QAAI,GAAGuH,QAAQ,GAAGvH,IAAI,CAAC7X,KAAL,CAAW,CAAX,CAAH,GAAmB6X,IAAlC;AACA,QAAM4I,MAAM,GAAG5I,IAAI,CAACrX,MAAL,CAAY,CAAZ,MAAmBmf,IAAlC;AACA9H,QAAI,GAAG4I,MAAM,GAAG5I,IAAI,CAAC7X,KAAL,CAAW,CAAX,CAAH,GAAmB6X,IAAhC;;AAEA,QAAI2I,WAAW,IAAIX,gBAAgB,CAACC,SAAD,EAAYjI,IAAZ,CAAnC,EAAsD;AACpD2I,iBAAW,CAACnc,OAAZ,CAAoB,UAAAqc,UAAU,EAAI;AAChC,YAAM5L,UAAU,GAAG4L,UAAU,CAAC,CAAD,CAA7B;AACA,YAAI5L,UAAJ,EAAgB;AACd,cAAI6L,UAAU,GAAG,MAAI,CAACzT,GAAtB;AACA,cAAIyT,UAAU,CAACT,QAAX,CAAoBC,OAAxB,EAAiC,CAAE;AACjCQ,sBAAU,GAAGX,YAAY,CAACW,UAAD,CAAZ,IAA4BA,UAAzC;AACD;AACD,cAAI7L,UAAU,KAAK,OAAnB,EAA4B;AAC1B6L,sBAAU,CAAC1J,KAAX,CAAiBrB,KAAjB,CAAuB+K,UAAvB;AACExB,4BAAgB;AACd,kBAAI,CAACjS,GADS;AAEdiM,iBAFc;AAGduH,sBAAU,CAAC,CAAD,CAHI;AAIdA,sBAAU,CAAC,CAAD,CAJI;AAKdtB,oBALc;AAMdtK,sBANc,CADlB;;AASA;AACD;AACD,cAAM8L,OAAO,GAAGD,UAAU,CAAC7L,UAAD,CAA1B;AACA,cAAI,CAAC/R,IAAI,CAAC6d,OAAD,CAAT,EAAoB;AAClB,gBAAM/I,KAAI,GAAG,MAAI,CAAC3K,GAAL,CAAS6M,MAAT,KAAoB,MAApB,GAA6B,MAA7B,GAAsC,WAAnD;AACA,gBAAM8G,IAAI,GAAG,MAAI,CAACC,KAAL,IAAc,MAAI,CAACC,EAAhC;AACA,kBAAM,IAAIhhB,KAAJ,WAAa8X,KAAb,gBAAsBgJ,IAAtB,yCAAuD/L,UAAvD,QAAN;AACD;AACD,cAAI2L,MAAJ,EAAY;AACV,gBAAIG,OAAO,CAACI,IAAZ,EAAkB;AAChB;AACD;AACDJ,mBAAO,CAACI,IAAR,GAAe,IAAf;AACD;AACD,cAAI7Z,MAAM,GAAGgY,gBAAgB;AAC3B,gBAAI,CAACjS,GADsB;AAE3BiM,eAF2B;AAG3BuH,oBAAU,CAAC,CAAD,CAHiB;AAI3BA,oBAAU,CAAC,CAAD,CAJiB;AAK3BtB,kBAL2B;AAM3BtK,oBAN2B,CAA7B;;AAQA3N,gBAAM,GAAGrC,KAAK,CAACC,OAAN,CAAcoC,MAAd,IAAwBA,MAAxB,GAAiC,EAA1C;AACA;AACA,cAAI,4DAA4DrH,IAA5D,CAAiE8gB,OAAO,CAAC5f,QAAR,EAAjE,CAAJ,EAA0F;AACxF;AACAmG,kBAAM,GAAGA,MAAM,CAACtC,MAAP,CAAc,YAAqBsU,KAArB,CAAd,CAAT;AACD;AACDsG,aAAG,CAACva,IAAJ,CAAS0b,OAAO,CAAChL,KAAR,CAAc+K,UAAd,EAA0BxZ,MAA1B,CAAT;AACD;AACF,OA/CD;AAgDD;AACF,GA3DD;;AA6DA;AACE2Y,WAAS,KAAK,OAAd;AACAL,KAAG,CAACxf,MAAJ,KAAe,CADf;AAEA,SAAOwf,GAAG,CAAC,CAAD,CAAV,KAAkB,WAHpB;AAIE;AACA,WAAOA,GAAG,CAAC,CAAD,CAAV;AACD;AACF;;AAED,IAAMwB,aAAa,GAAG,EAAtB;;AAEA,IAAMC,iBAAiB,GAAG,EAA1B;;AAEA,SAASC,eAAT,CAA0BC,EAA1B,EAA8B;AAC5B,MAAIA,EAAJ,EAAQ;AACN,QAAMC,YAAY,GAAGJ,aAAa,CAACG,EAAD,CAAlC;AACA,WAAOH,aAAa,CAACG,EAAD,CAApB;AACA,WAAOC,YAAP;AACD;AACD,SAAOH,iBAAiB,CAACI,KAAlB,EAAP;AACD;;AAED,IAAMrc,KAAK,GAAG;AACZ,QADY;AAEZ,QAFY;AAGZ,SAHY;AAIZ,gBAJY;AAKZ,eALY;AAMZ,sBANY,CAAd;;;AASA,SAASsc,gBAAT,GAA6B;AAC3Bpf,eAAIC,SAAJ,CAAcof,qBAAd,GAAsC,YAAY;AAChD;AACA;AACE,aAAO,KAAKpB,MAAL,CAAYoB,qBAAZ,EAAP;AACD;AACF,GALD;AAMA,MAAMC,QAAQ,GAAGtf,aAAIC,SAAJ,CAAcqY,WAA/B;AACAtY,eAAIC,SAAJ,CAAcqY,WAAd,GAA4B,UAAUrV,IAAV,EAAgBqJ,IAAhB,EAAsB;AAChD,QAAIrJ,IAAI,KAAK,QAAT,IAAqBqJ,IAArB,IAA6BA,IAAI,CAACiT,MAAtC,EAA8C;AAC5C,WAAKC,gBAAL,GAAwBR,eAAe,CAAC1S,IAAI,CAACiT,MAAN,CAAvC;AACA,aAAOjT,IAAI,CAACiT,MAAZ;AACD;AACD,WAAOD,QAAQ,CAACre,IAAT,CAAc,IAAd,EAAoBgC,IAApB,EAA0BqJ,IAA1B,CAAP;AACD,GAND;AAOD;;AAED,SAASmT,qBAAT,GAAkC;AAChC,MAAMC,MAAM,GAAG,EAAf;AACA,MAAMC,OAAO,GAAG,EAAhB;;AAEA3f,eAAIC,SAAJ,CAAc2f,qBAAd,GAAsC,UAAU7E,KAAV,EAAiB;AACrD,QAAM8E,GAAG,GAAGH,MAAM,CAAC3E,KAAD,CAAlB;AACA,QAAI,CAAC8E,GAAL,EAAU;AACRF,aAAO,CAAC5E,KAAD,CAAP,GAAiB,IAAjB;AACA,WAAKpG,GAAL,CAAS,gBAAT,EAA2B,YAAM;AAC/B,eAAOgL,OAAO,CAAC5E,KAAD,CAAd;AACD,OAFD;AAGD;AACD,WAAO8E,GAAP;AACD,GATD;;AAWA7f,eAAIC,SAAJ,CAAc6f,qBAAd,GAAsC,UAAU/E,KAAV,EAAiBzW,IAAjB,EAAuBnD,GAAvB,EAA4B;AAChE,QAAMyC,IAAI,GAAG8b,MAAM,CAAC3E,KAAD,CAAnB;AACA,QAAInX,IAAJ,EAAU;AACR,UAAMmc,MAAM,GAAGnc,IAAI,CAACU,IAAD,CAAJ,IAAc,EAA7B;AACA,aAAOnD,GAAG,GAAG4e,MAAM,CAAC5e,GAAD,CAAT,GAAiB4e,MAA3B;AACD,KAHD,MAGO;AACLJ,aAAO,CAAC5E,KAAD,CAAP,GAAiB,IAAjB;AACA,WAAKpG,GAAL,CAAS,gBAAT,EAA2B,YAAM;AAC/B,eAAOgL,OAAO,CAAC5E,KAAD,CAAd;AACD,OAFD;AAGD;AACF,GAXD;;AAaA/a,eAAIC,SAAJ,CAAc+f,qBAAd,GAAsC,UAAU1b,IAAV,EAAgB4B,KAAhB,EAAuB;AAC3D,QAAM8S,MAAM,GAAG,KAAK+E,QAAL,CAAckC,SAAd,CAAwBlF,KAAvC;AACA,QAAI/B,MAAJ,EAAY;AACV,UAAM+B,KAAK,GAAG/B,MAAM,CAACva,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAd;AACA,UAAMshB,MAAM,GAAGL,MAAM,CAAC3E,KAAD,CAAN,GAAgB2E,MAAM,CAAC3E,KAAD,CAAN,IAAiB,EAAhD;AACAgF,YAAM,CAACzb,IAAD,CAAN,GAAe4B,KAAf;AACA,UAAIyZ,OAAO,CAAC5E,KAAD,CAAX,EAAoB;AAClB4E,eAAO,CAAC5E,KAAD,CAAP,CAAe9R,YAAf;AACD;AACF;AACF,GAVD;;AAYAjJ,eAAI6I,KAAJ,CAAU;AACRqX,aADQ,uBACK;AACX,UAAMD,SAAS,GAAG,KAAKlC,QAAL,CAAckC,SAAhC;AACA,UAAMlF,KAAK,GAAGkF,SAAS,IAAIA,SAAS,CAAClF,KAArC;AACA,UAAIA,KAAJ,EAAW;AACT,eAAO2E,MAAM,CAAC3E,KAAD,CAAb;AACA,eAAO4E,OAAO,CAAC5E,KAAD,CAAd;AACD;AACF,KARO,EAAV;;AAUD;;AAED,SAASoF,YAAT,CAAuB1I,EAAvB;;;AAGG,KAFDC,KAEC,SAFDA,KAEC,CADD0I,QACC,SADDA,QACC;AACDhB,kBAAgB;AAChB;AACEK,yBAAqB;AACtB;AACD,MAAIhI,EAAE,CAACsG,QAAH,CAAYsC,KAAhB,EAAuB;AACrBrgB,iBAAIC,SAAJ,CAAcqgB,MAAd,GAAuB7I,EAAE,CAACsG,QAAH,CAAYsC,KAAnC;AACD;AACDtgB,YAAU,CAACC,YAAD,CAAV;;AAEAA,eAAIC,SAAJ,CAAcsgB,MAAd,GAAuB,WAAvB;;AAEAvgB,eAAI6I,KAAJ,CAAU;AACRC,gBADQ,0BACQ;AACd,UAAI,CAAC,KAAKiV,QAAL,CAAcnG,MAAnB,EAA2B;AACzB;AACD;;AAED,WAAKA,MAAL,GAAc,KAAKmG,QAAL,CAAcnG,MAA5B;;AAEA,WAAKD,GAAL;AACE/T,YAAI,EAAE,EADR;AAEG,WAAKgU,MAFR,EAEiB,KAAKmG,QAAL,CAAcnH,UAF/B;;;AAKA,WAAKqH,MAAL,GAAc,KAAKF,QAAL,CAAcnH,UAA5B;;AAEA,aAAO,KAAKmH,QAAL,CAAcnG,MAArB;AACA,aAAO,KAAKmG,QAAL,CAAcnH,UAArB;AACA,UAAI,KAAKgB,MAAL,KAAgB,MAAhB,IAA0B,OAAO/M,MAAP,KAAkB,UAAhD,EAA4D,CAAE;AAC5D,YAAMD,GAAG,GAAGC,MAAM,EAAlB;AACA,YAAID,GAAG,CAACG,GAAJ,IAAWH,GAAG,CAACG,GAAJ,CAAQyV,KAAvB,EAA8B;AAC5B,eAAKC,KAAL,GAAa7V,GAAG,CAACG,GAAJ,CAAQyV,KAArB;AACD;AACF;AACD,UAAI,KAAK5I,MAAL,KAAgB,KAApB,EAA2B;AACzBwI,gBAAQ,CAAC,IAAD,CAAR;AACA5I,iBAAS,CAAC,IAAD,EAAOE,KAAP,CAAT;AACD;AACF,KA3BO,EAAV;;;AA8BA,MAAMgJ,UAAU,GAAG;AACjBC,YADiB,oBACPrU,IADO,EACD;AACd,UAAI,KAAKvB,GAAT,EAAc,CAAE;AACd;AACD;AACD;AACE,YAAI9L,EAAE,CAAC2hB,OAAH,IAAc,CAAC3hB,EAAE,CAAC2hB,OAAH,CAAW,UAAX,CAAnB,EAA2C,CAAE;AAC3C1N,iBAAO,CAACvT,KAAR,CAAc,qDAAd;AACD;AACF;;AAED,WAAKoL,GAAL,GAAW0M,EAAX;;AAEA,WAAK1M,GAAL,CAAS4M,GAAT,GAAe;AACb/M,WAAG,EAAE,IADQ,EAAf;;;AAIA,WAAKG,GAAL,CAASkT,MAAT,GAAkB,IAAlB;AACA;AACA,WAAKlT,GAAL,CAAS8V,UAAT,GAAsB,KAAKA,UAA3B;;AAEA,WAAK9V,GAAL,CAAS+V,UAAT,GAAsB,IAAtB;AACA,WAAK/V,GAAL,CAASuN,WAAT,CAAqB,SAArB,EAAgChM,IAAhC;;AAEA,WAAKvB,GAAL,CAASuN,WAAT,CAAqB,UAArB,EAAiChM,IAAjC;AACD,KAzBgB,EAAnB;;;AA4BA;AACAoU,YAAU,CAACG,UAAX,GAAwBpJ,EAAE,CAACsG,QAAH,CAAY8C,UAAZ,IAA0B,EAAlD;AACA;AACA,MAAM1X,OAAO,GAAGsO,EAAE,CAACsG,QAAH,CAAY5U,OAA5B;AACA,MAAIA,OAAJ,EAAa;AACXzI,UAAM,CAACsB,IAAP,CAAYmH,OAAZ,EAAqBjH,OAArB,CAA6B,UAAAoC,IAAI,EAAI;AACnCoc,gBAAU,CAACpc,IAAD,CAAV,GAAmB6E,OAAO,CAAC7E,IAAD,CAA1B;AACD,KAFD;AAGD;;AAEDkF,eAAa,CAACxJ,YAAD,EAAMyX,EAAN,EAAUxP,eAAe,CAAChJ,EAAE,CAAC+H,iBAAH,GAAuBkB,QAAxB,CAAf,IAAoDN,SAA9D,CAAb;;AAEAwQ,WAAS,CAACsI,UAAD,EAAa5d,KAAb,CAAT;;AAEA,SAAO4d,UAAP;AACD;;AAED,IAAMhJ,KAAK,GAAG,CAAC,WAAD,EAAc,sBAAd,EAAsC,iBAAtC,CAAd;;AAEA,SAASqJ,aAAT,CAAwBtJ,EAAxB,EAA4BuJ,MAA5B,EAAoC;AAClC,MAAMC,SAAS,GAAGxJ,EAAE,CAACwJ,SAArB;AACA;AACA,OAAK,IAAI9iB,CAAC,GAAG8iB,SAAS,CAACnjB,MAAV,GAAmB,CAAhC,EAAmCK,CAAC,IAAI,CAAxC,EAA2CA,CAAC,EAA5C,EAAgD;AAC9C,QAAM+iB,OAAO,GAAGD,SAAS,CAAC9iB,CAAD,CAAzB;AACA,QAAI+iB,OAAO,CAACjD,MAAR,CAAehF,OAAf,KAA2B+H,MAA/B,EAAuC;AACrC,aAAOE,OAAP;AACD;AACF;AACD;AACA,MAAIC,QAAJ;AACA,OAAK,IAAIhjB,EAAC,GAAG8iB,SAAS,CAACnjB,MAAV,GAAmB,CAAhC,EAAmCK,EAAC,IAAI,CAAxC,EAA2CA,EAAC,EAA5C,EAAgD;AAC9CgjB,YAAQ,GAAGJ,aAAa,CAACE,SAAS,CAAC9iB,EAAD,CAAV,EAAe6iB,MAAf,CAAxB;AACA,QAAIG,QAAJ,EAAc;AACZ,aAAOA,QAAP;AACD;AACF;AACF;;AAED,SAASpH,YAAT,CAAuB1V,OAAvB,EAAgC;AAC9B,SAAO+c,QAAQ,CAAC/c,OAAD,CAAf;AACD;;AAED,SAASgd,MAAT,GAAmB;AACjB,SAAO,CAAC,CAAC,KAAK1C,KAAd;AACD;;AAED,SAAS2C,YAAT,CAAuB3F,MAAvB,EAA+B;AAC7B,OAAK7E,YAAL,CAAkB,KAAlB,EAAyB6E,MAAzB;AACD;;AAED,SAAS4F,mBAAT,CAA8B3K,UAA9B,EAA0C4K,QAA1C,EAAoDC,KAApD,EAA2D;AACzD,MAAMC,UAAU,GAAG9K,UAAU,CAAC2K,mBAAX,CAA+BC,QAA/B,CAAnB;AACAE,YAAU,CAACxf,OAAX,CAAmB,UAAAyf,SAAS,EAAI;AAC9B,QAAMC,GAAG,GAAGD,SAAS,CAACvE,OAAV,CAAkBwE,GAA9B;AACAH,SAAK,CAACG,GAAD,CAAL,GAAaD,SAAS,CAAC5W,GAAV,IAAiB4W,SAA9B;AACA;AACE,UAAIA,SAAS,CAACvE,OAAV,CAAkByE,UAAlB,KAAiC,QAArC,EAA+C;AAC7CF,iBAAS,CAACJ,mBAAV,CAA8B,aAA9B,EAA6Crf,OAA7C,CAAqD,UAAA4f,eAAe,EAAI;AACtEP,6BAAmB,CAACO,eAAD,EAAkBN,QAAlB,EAA4BC,KAA5B,CAAnB;AACD,SAFD;AAGD;AACF;AACF,GAVD;AAWD;;AAED,SAASrB,QAAT,CAAmB3I,EAAnB,EAAuB;AACrB,MAAMb,UAAU,GAAGa,EAAE,CAACwG,MAAtB;AACAvd,QAAM,CAACoJ,cAAP,CAAsB2N,EAAtB,EAA0B,OAA1B,EAAmC;AACjC1N,OADiC,iBAC1B;AACL,UAAM0X,KAAK,GAAG,EAAd;AACAF,yBAAmB,CAAC3K,UAAD,EAAa,UAAb,EAAyB6K,KAAzB,CAAnB;AACA;AACA,UAAMM,aAAa,GAAGnL,UAAU,CAAC2K,mBAAX,CAA+B,iBAA/B,CAAtB;AACAQ,mBAAa,CAAC7f,OAAd,CAAsB,UAAAyf,SAAS,EAAI;AACjC,YAAMC,GAAG,GAAGD,SAAS,CAACvE,OAAV,CAAkBwE,GAA9B;AACA,YAAI,CAACH,KAAK,CAACG,GAAD,CAAV,EAAiB;AACfH,eAAK,CAACG,GAAD,CAAL,GAAa,EAAb;AACD;AACDH,aAAK,CAACG,GAAD,CAAL,CAAW7e,IAAX,CAAgB4e,SAAS,CAAC5W,GAAV,IAAiB4W,SAAjC;AACD,OAND;AAOA,aAAOF,KAAP;AACD,KAdgC,EAAnC;;AAgBD;;AAED,SAASO,UAAT,CAAqBhL,KAArB,EAA4B;;;;AAItBA,OAAK,CAAC2E,MAAN,IAAgB3E,KAAK,CAAC9Q,KAJA,CAExB8a,MAFwB,SAExBA,MAFwB,CAGxBjJ,UAHwB,SAGxBA,UAHwB,EAIO;;AAEjC,MAAIoJ,QAAJ;;AAEA,MAAIH,MAAJ,EAAY;AACVG,YAAQ,GAAGJ,aAAa,CAAC,KAAKhW,GAAN,EAAWiW,MAAX,CAAxB;AACD;;AAED,MAAI,CAACG,QAAL,EAAe;AACbA,YAAQ,GAAG,KAAKpW,GAAhB;AACD;;AAEDgN,YAAU,CAACkK,MAAX,GAAoBd,QAApB;AACD;;AAED,SAASe,QAAT,CAAmBzK,EAAnB,EAAuB;AACrB,SAAO0I,YAAY,CAAC1I,EAAD,EAAK;AACtBC,SAAK,EAALA,KADsB;AAEtB0I,YAAQ,EAARA,QAFsB,EAAL,CAAnB;;AAID;;AAED,SAAS+B,SAAT,CAAoB1K,EAApB,EAAwB;AACtB2K,KAAG,CAACF,QAAQ,CAACzK,EAAD,CAAT,CAAH;AACA,SAAOA,EAAP;AACD;;AAED,IAAM4K,eAAe,GAAG,UAAxB;AACA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAA3jB,CAAC,UAAI,MAAMA,CAAC,CAACC,UAAF,CAAa,CAAb,EAAgBC,QAAhB,CAAyB,EAAzB,CAAV,EAA/B;AACA,IAAM0jB,OAAO,GAAG,MAAhB;;AAEA;AACA;AACA;AACA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAhlB,GAAG,UAAIilB,kBAAkB,CAACjlB,GAAD,CAAlB;AACnBE,SADmB,CACX2kB,eADW,EACMC,qBADN;AAEnB5kB,SAFmB,CAEX6kB,OAFW,EAEF,GAFE,CAAJ,EAAlB;;AAIA,SAASG,cAAT,CAAyB1hB,GAAzB,EAAkD,KAApB2hB,SAAoB,uEAARH,MAAQ;AAChD,MAAM/f,GAAG,GAAGzB,GAAG,GAAGN,MAAM,CAACsB,IAAP,CAAYhB,GAAZ,EAAiBtC,GAAjB,CAAqB,UAAAyC,GAAG,EAAI;AAC5C,QAAMyhB,GAAG,GAAG5hB,GAAG,CAACG,GAAD,CAAf;;AAEA,QAAIyhB,GAAG,KAAKlS,SAAZ,EAAuB;AACrB,aAAO,EAAP;AACD;;AAED,QAAIkS,GAAG,KAAK,IAAZ,EAAkB;AAChB,aAAOD,SAAS,CAACxhB,GAAD,CAAhB;AACD;;AAED,QAAIwB,KAAK,CAACC,OAAN,CAAcggB,GAAd,CAAJ,EAAwB;AACtB,UAAM5kB,MAAM,GAAG,EAAf;AACA4kB,SAAG,CAAC1gB,OAAJ,CAAY,UAAA2gB,IAAI,EAAI;AAClB,YAAIA,IAAI,KAAKnS,SAAb,EAAwB;AACtB;AACD;AACD,YAAImS,IAAI,KAAK,IAAb,EAAmB;AACjB7kB,gBAAM,CAAC+E,IAAP,CAAY4f,SAAS,CAACxhB,GAAD,CAArB;AACD,SAFD,MAEO;AACLnD,gBAAM,CAAC+E,IAAP,CAAY4f,SAAS,CAACxhB,GAAD,CAAT,GAAiB,GAAjB,GAAuBwhB,SAAS,CAACE,IAAD,CAA5C;AACD;AACF,OATD;AAUA,aAAO7kB,MAAM,CAACc,IAAP,CAAY,GAAZ,CAAP;AACD;;AAED,WAAO6jB,SAAS,CAACxhB,GAAD,CAAT,GAAiB,GAAjB,GAAuBwhB,SAAS,CAACC,GAAD,CAAvC;AACD,GA3BiB,EA2Bf9V,MA3Be,CA2BR,UAAAgW,CAAC,UAAIA,CAAC,CAAChlB,MAAF,GAAW,CAAf,EA3BO,EA2BWgB,IA3BX,CA2BgB,GA3BhB,CAAH,GA2B0B,IA3BzC;AA4BA,SAAO2D,GAAG,cAAOA,GAAP,IAAe,EAAzB;AACD;;AAED,SAASsgB,kBAAT,CAA6BC,mBAA7B;;;AAGQ,iFAAJ,EAAI,CAFN3B,MAEM,SAFNA,MAEM,CADNC,YACM,SADNA,YACM;AAC6B/I,kBAAgB,CAACvY,YAAD,EAAMgjB,mBAAN,CAD7C,2DACCxK,YADD,yBACeT,UADf;;AAGN,MAAM1T,OAAO;AACX4e,iBAAa,EAAE,IADJ;AAEXC,kBAAc,EAAE,IAFL;AAGPnL,YAAU,CAAC1T,OAAX,IAAsB,EAHf,CAAb;;;AAMA;AACE;AACA,QAAI0T,UAAU,CAAC,WAAD,CAAV,IAA2BA,UAAU,CAAC,WAAD,CAAV,CAAwB1T,OAAvD,EAAgE;AAC9D3D,YAAM,CAAC+F,MAAP,CAAcpC,OAAd,EAAuB0T,UAAU,CAAC,WAAD,CAAV,CAAwB1T,OAA/C;AACD;AACF;;AAED,MAAM8e,gBAAgB,GAAG;AACvB9e,WAAO,EAAPA,OADuB;AAEvBT,QAAI,EAAEuV,QAAQ,CAACpB,UAAD,EAAa/X,aAAIC,SAAjB,CAFS;AAGvBga,aAAS,EAAEH,aAAa,CAAC/B,UAAD,EAAagC,YAAb,CAHD;AAIvBS,cAAU,EAAEC,cAAc,CAAC1C,UAAU,CAACuC,KAAZ,EAAmB,KAAnB,EAA0BvC,UAAU,CAACqL,MAArC,EAA6C/e,OAA7C,CAJH;AAKvBgf,aAAS,EAAE;AACTC,cADS,sBACG;AACV,YAAM9I,UAAU,GAAG,KAAKA,UAAxB;;AAEA,YAAMnW,OAAO,GAAG;AACduT,gBAAM,EAAEyJ,MAAM,CAACpgB,IAAP,CAAY,IAAZ,IAAoB,MAApB,GAA6B,WADvB;AAEd2V,oBAAU,EAAE,IAFE;AAGdqJ,mBAAS,EAAEzF,UAHG,EAAhB;;;AAMAzB,kBAAU,CAACyB,UAAU,CAACO,KAAZ,EAAmB,IAAnB,CAAV;;AAEA;AACAuG,oBAAY,CAACrgB,IAAb,CAAkB,IAAlB,EAAwB;AACtB+f,gBAAM,EAAE,KAAK9H,QADS;AAEtBnB,oBAAU,EAAE1T,OAFU,EAAxB;;;AAKA;AACA,aAAK0G,GAAL,GAAW,IAAIyN,YAAJ,CAAiBnU,OAAjB,CAAX;;AAEA;AACAqU,iBAAS,CAAC,KAAK3N,GAAN,EAAWyP,UAAU,CAAC7B,QAAtB,CAAT;;AAEA;AACA,aAAK5N,GAAL,CAASwY,MAAT;AACD,OA1BQ;AA2BTC,WA3BS,mBA2BA;AACP;AACA;AACA,YAAI,KAAKzY,GAAT,EAAc;AACZ,eAAKA,GAAL,CAAS+V,UAAT,GAAsB,IAAtB;AACA,eAAK/V,GAAL,CAASuN,WAAT,CAAqB,SAArB;AACA,eAAKvN,GAAL,CAASuN,WAAT,CAAqB,SAArB;AACD;AACF,OAnCQ;AAoCTmL,cApCS,sBAoCG;AACV,aAAK1Y,GAAL,IAAY,KAAKA,GAAL,CAAS2Y,QAAT,EAAZ;AACD,OAtCQ,EALY;;AA6CvBC,iBAAa,EAAE;AACbC,UADa,gBACPtX,IADO,EACD;AACV,aAAKvB,GAAL,IAAY,KAAKA,GAAL,CAASuN,WAAT,CAAqB,YAArB,EAAmChM,IAAnC,CAAZ;AACD,OAHY;AAIbuX,UAJa,kBAIL;AACN,aAAK9Y,GAAL,IAAY,KAAKA,GAAL,CAASuN,WAAT,CAAqB,YAArB,CAAZ;AACD,OANY;AAObwL,YAPa,kBAOLC,IAPK,EAOC;AACZ,aAAKhZ,GAAL,IAAY,KAAKA,GAAL,CAASuN,WAAT,CAAqB,cAArB,EAAqCyL,IAArC,CAAZ;AACD,OATY,EA7CQ;;AAwDvB5a,WAAO,EAAE;AACP6a,SAAG,EAAEhC,UADE;AAEPiC,SAAG,EAAE/F,WAFE,EAxDc,EAAzB;;;AA6DA;AACA,MAAInG,UAAU,CAACmM,eAAf,EAAgC;AAC9Bf,oBAAgB,CAACe,eAAjB,GAAmCnM,UAAU,CAACmM,eAA9C;AACD;;AAED,MAAIvhB,KAAK,CAACC,OAAN,CAAcmV,UAAU,CAACoM,cAAzB,CAAJ,EAA8C;AAC5CpM,cAAU,CAACoM,cAAX,CAA0BjiB,OAA1B,CAAkC,UAAAkiB,UAAU,EAAI;AAC9CjB,sBAAgB,CAACha,OAAjB,CAAyBib,UAAzB,IAAuC,UAAU9X,IAAV,EAAgB;AACrD,eAAO,KAAKvB,GAAL,CAASqZ,UAAT,EAAqB9X,IAArB,CAAP;AACD,OAFD;AAGD,KAJD;AAKD;;AAED,MAAI+U,MAAJ,EAAY;AACV,WAAO8B,gBAAP;AACD;AACD,SAAO,CAACA,gBAAD,EAAmB3K,YAAnB,CAAP;AACD;;AAED,SAAS6L,cAAT,CAAyBrB,mBAAzB,EAA8C;AAC5C,SAAOD,kBAAkB,CAACC,mBAAD,EAAsB;AAC7C3B,UAAM,EAANA,MAD6C;AAE7CC,gBAAY,EAAZA,YAF6C,EAAtB,CAAzB;;AAID;;AAED,IAAMgD,OAAO,GAAG;AACd,QADc;AAEd,QAFc;AAGd,UAHc,CAAhB;;;AAMAA,OAAO,CAACvhB,IAAR,OAAAuhB,OAAO,EAAS/M,gBAAT,CAAP;;AAEA,SAASgN,aAAT,CAAwBC,cAAxB;;;AAGG,KAFDnD,MAEC,SAFDA,MAEC,CADDC,YACC,SADDA,YACC;AACD,MAAMmD,WAAW,GAAGJ,cAAc,CAACG,cAAD,CAAlC;;AAEApM,WAAS,CAACqM,WAAW,CAACtb,OAAb,EAAsBmb,OAAtB,EAA+BE,cAA/B,CAAT;;AAEAC,aAAW,CAACtb,OAAZ,CAAoBub,MAApB,GAA6B,UAAUC,KAAV,EAAiB;AAC5C,SAAKtgB,OAAL,GAAesgB,KAAf;AACA,QAAMC,SAAS,GAAGlkB,MAAM,CAAC+F,MAAP,CAAc,EAAd,EAAkBke,KAAlB,CAAlB;AACA,WAAOC,SAAS,CAACrF,MAAjB;AACA,SAAKvT,KAAL,GAAa;AACXC,cAAQ,EAAE,OAAO,KAAK0S,KAAL,IAAc,KAAKC,EAA1B,IAAgC8D,cAAc,CAACkC,SAAD,CAD7C,EAAb;;AAGA,SAAK7Z,GAAL,CAAS4M,GAAT,CAAagN,KAAb,GAAqBA,KAArB,CAP4C,CAOhB;AAC5B,SAAK5Z,GAAL,CAASuN,WAAT,CAAqB,QAArB,EAA+BqM,KAA/B;AACD,GATD;;AAWA,SAAOF,WAAP;AACD;;AAED,SAASI,SAAT,CAAoBL,cAApB,EAAoC;AAClC,SAAOD,aAAa,CAACC,cAAD,EAAiB;AACnCnD,UAAM,EAANA,MADmC;AAEnCC,gBAAY,EAAZA,YAFmC,EAAjB,CAApB;;AAID;;AAED,SAASwD,UAAT,CAAqBN,cAArB,EAAqC;AACnC;AACE,WAAOhO,SAAS,CAACqO,SAAS,CAACL,cAAD,CAAV,CAAhB;AACD;AACF;;AAED,SAASO,eAAT,CAA0BhN,UAA1B,EAAsC;AACpC;AACE,WAAOvB,SAAS,CAAC6N,cAAc,CAACtM,UAAD,CAAf,CAAhB;AACD;AACF;;AAED,SAASiN,mBAAT,CAA8BvN,EAA9B,EAAkC;AAChC,MAAMiJ,UAAU,GAAGwB,QAAQ,CAACzK,EAAD,CAA3B;AACA,MAAM7M,GAAG,GAAGC,MAAM,CAAC;AACjBC,gBAAY,EAAE,IADG,EAAD,CAAlB;;AAGA2M,IAAE,CAACwG,MAAH,GAAYrT,GAAZ;AACA,MAAMiW,UAAU,GAAGjW,GAAG,CAACiW,UAAvB;AACA,MAAIA,UAAJ,EAAgB;AACdngB,UAAM,CAACsB,IAAP,CAAY0e,UAAU,CAACG,UAAvB,EAAmC3e,OAAnC,CAA2C,UAAAoC,IAAI,EAAI;AACjD,UAAI,CAACpD,MAAM,CAAC2f,UAAD,EAAavc,IAAb,CAAX,EAA+B;AAC7Buc,kBAAU,CAACvc,IAAD,CAAV,GAAmBoc,UAAU,CAACG,UAAX,CAAsBvc,IAAtB,CAAnB;AACD;AACF,KAJD;AAKD;AACD5D,QAAM,CAACsB,IAAP,CAAY0e,UAAZ,EAAwBxe,OAAxB,CAAgC,UAAAoC,IAAI,EAAI;AACtC,QAAI,CAACpD,MAAM,CAAC0J,GAAD,EAAMtG,IAAN,CAAX,EAAwB;AACtBsG,SAAG,CAACtG,IAAD,CAAH,GAAYoc,UAAU,CAACpc,IAAD,CAAtB;AACD;AACF,GAJD;AAKA,MAAI1D,IAAI,CAAC8f,UAAU,CAACuE,MAAZ,CAAJ,IAA2BhmB,EAAE,CAACimB,SAAlC,EAA6C;AAC3CjmB,MAAE,CAACimB,SAAH,CAAa,YAAa,oCAAT5Y,IAAS,yDAATA,IAAS;AACxBmL,QAAE,CAACa,WAAH,CAAe,QAAf,EAAyBhM,IAAzB;AACD,KAFD;AAGD;AACD,MAAI1L,IAAI,CAAC8f,UAAU,CAACyE,MAAZ,CAAJ,IAA2BlmB,EAAE,CAACmmB,SAAlC,EAA6C;AAC3CnmB,MAAE,CAACmmB,SAAH,CAAa,YAAa,oCAAT9Y,IAAS,yDAATA,IAAS;AACxBmL,QAAE,CAACa,WAAH,CAAe,QAAf,EAAyBhM,IAAzB;AACD,KAFD;AAGD;AACD,MAAI1L,IAAI,CAAC8f,UAAU,CAACC,QAAZ,CAAR,EAA+B;AAC7B,QAAMrU,IAAI,GAAGrN,EAAE,CAAComB,oBAAH,IAA2BpmB,EAAE,CAAComB,oBAAH,EAAxC;AACA5N,MAAE,CAACa,WAAH,CAAe,UAAf,EAA2BhM,IAA3B;AACD;AACD,SAAOmL,EAAP;AACD;;AAED,SAAS6N,YAAT,CAAuB7N,EAAvB,EAA2B;AACzB,MAAMiJ,UAAU,GAAGwB,QAAQ,CAACzK,EAAD,CAA3B;AACA,MAAI7W,IAAI,CAAC8f,UAAU,CAACuE,MAAZ,CAAJ,IAA2BhmB,EAAE,CAACimB,SAAlC,EAA6C;AAC3CjmB,MAAE,CAACimB,SAAH,CAAa,YAAa,oCAAT5Y,IAAS,yDAATA,IAAS;AACxBmL,QAAE,CAACa,WAAH,CAAe,QAAf,EAAyBhM,IAAzB;AACD,KAFD;AAGD;AACD,MAAI1L,IAAI,CAAC8f,UAAU,CAACyE,MAAZ,CAAJ,IAA2BlmB,EAAE,CAACmmB,SAAlC,EAA6C;AAC3CnmB,MAAE,CAACmmB,SAAH,CAAa,YAAa,oCAAT9Y,IAAS,yDAATA,IAAS;AACxBmL,QAAE,CAACa,WAAH,CAAe,QAAf,EAAyBhM,IAAzB;AACD,KAFD;AAGD;AACD,MAAI1L,IAAI,CAAC8f,UAAU,CAACC,QAAZ,CAAR,EAA+B;AAC7B,QAAMrU,IAAI,GAAGrN,EAAE,CAAComB,oBAAH,IAA2BpmB,EAAE,CAAComB,oBAAH,EAAxC;AACA5N,MAAE,CAACa,WAAH,CAAe,UAAf,EAA2BhM,IAA3B;AACD;AACD,SAAOmL,EAAP;AACD;;AAEDlF,KAAK,CAACrQ,OAAN,CAAc,UAAA2R,OAAO,EAAI;AACvBvB,WAAS,CAACuB,OAAD,CAAT,GAAqB,KAArB;AACD,CAFD;;AAIArB,QAAQ,CAACtQ,OAAT,CAAiB,UAAAqjB,UAAU,EAAI;AAC7B,MAAMC,OAAO,GAAGlT,SAAS,CAACiT,UAAD,CAAT,IAAyBjT,SAAS,CAACiT,UAAD,CAAT,CAAsBjhB,IAA/C,GAAsDgO,SAAS,CAACiT,UAAD,CAAT,CAAsBjhB,IAA5E;AACZihB,YADJ;AAEA,MAAI,CAACtmB,EAAE,CAAC2hB,OAAH,CAAW4E,OAAX,CAAL,EAA0B;AACxBlT,aAAS,CAACiT,UAAD,CAAT,GAAwB,KAAxB;AACD;AACF,CAND;;AAQA,IAAIE,GAAG,GAAG,EAAV;;AAEA,IAAI,OAAOC,KAAP,KAAiB,WAAjB,IAAgC,gBAAgB,UAApD,EAAgE;AAC9DD,KAAG,GAAG,IAAIC,KAAJ,CAAU,EAAV,EAAc;AAClB3b,OADkB,eACb2R,MADa,EACLpX,IADK,EACC;AACjB,UAAIpD,MAAM,CAACwa,MAAD,EAASpX,IAAT,CAAV,EAA0B;AACxB,eAAOoX,MAAM,CAACpX,IAAD,CAAb;AACD;AACD,UAAIiH,OAAO,CAACjH,IAAD,CAAX,EAAmB;AACjB,eAAOiH,OAAO,CAACjH,IAAD,CAAd;AACD;AACD,UAAIS,GAAG,CAACT,IAAD,CAAP,EAAe;AACb,eAAO8B,SAAS,CAAC9B,IAAD,EAAOS,GAAG,CAACT,IAAD,CAAV,CAAhB;AACD;AACD;AACE,YAAIgQ,QAAQ,CAAChQ,IAAD,CAAZ,EAAoB;AAClB,iBAAO8B,SAAS,CAAC9B,IAAD,EAAOgQ,QAAQ,CAAChQ,IAAD,CAAf,CAAhB;AACD;AACD,YAAIoP,QAAQ,CAACpP,IAAD,CAAZ,EAAoB;AAClB,iBAAO8B,SAAS,CAAC9B,IAAD,EAAOoP,QAAQ,CAACpP,IAAD,CAAf,CAAhB;AACD;AACF;AACD,UAAIyQ,QAAQ,CAACzQ,IAAD,CAAZ,EAAoB;AAClB,eAAOyQ,QAAQ,CAACzQ,IAAD,CAAf;AACD;AACD,UAAI,CAACpD,MAAM,CAACjC,EAAD,EAAKqF,IAAL,CAAP,IAAqB,CAACpD,MAAM,CAACoR,SAAD,EAAYhO,IAAZ,CAAhC,EAAmD;AACjD;AACD;AACD,aAAO8B,SAAS,CAAC9B,IAAD,EAAO+O,OAAO,CAAC/O,IAAD,EAAOrF,EAAE,CAACqF,IAAD,CAAT,CAAd,CAAhB;AACD,KA1BiB;AA2BlB0F,OA3BkB,eA2Bb0R,MA3Ba,EA2BLpX,IA3BK,EA2BC4B,KA3BD,EA2BQ;AACxBwV,YAAM,CAACpX,IAAD,CAAN,GAAe4B,KAAf;AACA,aAAO,IAAP;AACD,KA9BiB,EAAd,CAAN;;AAgCD,CAjCD,MAiCO;AACLxF,QAAM,CAACsB,IAAP,CAAYuJ,OAAZ,EAAqBrJ,OAArB,CAA6B,UAAAoC,IAAI,EAAI;AACnCmhB,OAAG,CAACnhB,IAAD,CAAH,GAAYiH,OAAO,CAACjH,IAAD,CAAnB;AACD,GAFD;;AAIA;AACE5D,UAAM,CAACsB,IAAP,CAAY0R,QAAZ,EAAsBxR,OAAtB,CAA8B,UAAAoC,IAAI,EAAI;AACpCmhB,SAAG,CAACnhB,IAAD,CAAH,GAAY8B,SAAS,CAAC9B,IAAD,EAAOoP,QAAQ,CAACpP,IAAD,CAAf,CAArB;AACD,KAFD;AAGA5D,UAAM,CAACsB,IAAP,CAAYsS,QAAZ,EAAsBpS,OAAtB,CAA8B,UAAAoC,IAAI,EAAI;AACpCmhB,SAAG,CAACnhB,IAAD,CAAH,GAAY8B,SAAS,CAAC9B,IAAD,EAAOoP,QAAQ,CAACpP,IAAD,CAAf,CAArB;AACD,KAFD;AAGD;;AAED5D,QAAM,CAACsB,IAAP,CAAY+S,QAAZ,EAAsB7S,OAAtB,CAA8B,UAAAoC,IAAI,EAAI;AACpCmhB,OAAG,CAACnhB,IAAD,CAAH,GAAYyQ,QAAQ,CAACzQ,IAAD,CAApB;AACD,GAFD;;AAIA5D,QAAM,CAACsB,IAAP,CAAY+C,GAAZ,EAAiB7C,OAAjB,CAAyB,UAAAoC,IAAI,EAAI;AAC/BmhB,OAAG,CAACnhB,IAAD,CAAH,GAAY8B,SAAS,CAAC9B,IAAD,EAAOS,GAAG,CAACT,IAAD,CAAV,CAArB;AACD,GAFD;;AAIA5D,QAAM,CAACsB,IAAP,CAAY/C,EAAZ,EAAgBiD,OAAhB,CAAwB,UAAAoC,IAAI,EAAI;AAC9B,QAAIpD,MAAM,CAACjC,EAAD,EAAKqF,IAAL,CAAN,IAAoBpD,MAAM,CAACoR,SAAD,EAAYhO,IAAZ,CAA9B,EAAiD;AAC/CmhB,SAAG,CAACnhB,IAAD,CAAH,GAAY8B,SAAS,CAAC9B,IAAD,EAAO+O,OAAO,CAAC/O,IAAD,EAAOrF,EAAE,CAACqF,IAAD,CAAT,CAAd,CAArB;AACD;AACF,GAJD;AAKD;;AAEDrF,EAAE,CAACkjB,SAAH,GAAeA,SAAf;AACAljB,EAAE,CAAC6lB,UAAH,GAAgBA,UAAhB;AACA7lB,EAAE,CAAC8lB,eAAH,GAAqBA,eAArB;AACA9lB,EAAE,CAAC+lB,mBAAH,GAAyBA,mBAAzB;AACA/lB,EAAE,CAACqmB,YAAH,GAAkBA,YAAlB;;AAEA,IAAIK,KAAK,GAAGF,GAAZ,C;;AAEeE,K;;;;;;;;;;;;;ACv7Ef;AAAA;AAAA;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClHA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;;;;;ACnBCC,MAAM,CAACC,OAAP,GAAiB;AACfla,KAAG,EAAE,4BADU,EAAjB,C;;;;;;;;;;;;4nFCAD,IAAM/I,OAAO,GAAGD,KAAK,CAACC,OAAtB;AACA,IAAMkjB,QAAQ,GAAG,SAAXA,QAAW,CAAClD,GAAD,UAASA,GAAG,KAAK,IAAR,IAAgB,OAAOA,GAAP,KAAe,QAAxC,EAAjB;AACA,IAAMmD,iBAAiB,GAAG,CAAC,GAAD,EAAM,GAAN,CAA1B,C;AACMC,a;AACF,2BAAc;AACV,SAAKC,OAAL,GAAevlB,MAAM,CAACa,MAAP,CAAc,IAAd,CAAf;AACH,G;AACW3B,W,EAASyJ,M,EAAwC,KAAhC6c,UAAgC,uEAAnBH,iBAAmB;AACzD,UAAI,CAAC1c,MAAL,EAAa;AACT,eAAO,CAACzJ,OAAD,CAAP;AACH;AACD,UAAIumB,MAAM,GAAG,KAAKF,OAAL,CAAarmB,OAAb,CAAb;AACA,UAAI,CAACumB,MAAL,EAAa;AACTA,cAAM,GAAGzmB,KAAK,CAACE,OAAD,EAAUsmB,UAAV,CAAd;AACA,aAAKD,OAAL,CAAarmB,OAAb,IAAwBumB,MAAxB;AACH;AACD,aAAOC,OAAO,CAACD,MAAD,EAAS9c,MAAT,CAAd;AACH,K;;AAEL,IAAMgd,mBAAmB,GAAG,UAA5B;AACA,IAAMC,oBAAoB,GAAG,UAA7B;AACA,SAAS5mB,KAAT,CAAe6mB,MAAf,QAAuD,qCAA/BC,cAA+B,YAAfC,YAAe;AACnD,MAAMN,MAAM,GAAG,EAAf;AACA,MAAIO,QAAQ,GAAG,CAAf;AACA,MAAIC,IAAI,GAAG,EAAX;AACA,SAAOD,QAAQ,GAAGH,MAAM,CAACzoB,MAAzB,EAAiC;AAC7B,QAAI8oB,IAAI,GAAGL,MAAM,CAACG,QAAQ,EAAT,CAAjB;AACA,QAAIE,IAAI,KAAKJ,cAAb,EAA6B;AACzB,UAAIG,IAAJ,EAAU;AACNR,cAAM,CAACpjB,IAAP,CAAY,EAAE2S,IAAI,EAAE,MAAR,EAAgBxP,KAAK,EAAEygB,IAAvB,EAAZ;AACH;AACDA,UAAI,GAAG,EAAP;AACA,UAAIE,GAAG,GAAG,EAAV;AACAD,UAAI,GAAGL,MAAM,CAACG,QAAQ,EAAT,CAAb;AACA,aAAOE,IAAI,KAAKlW,SAAT,IAAsBkW,IAAI,KAAKH,YAAtC,EAAoD;AAChDI,WAAG,IAAID,IAAP;AACAA,YAAI,GAAGL,MAAM,CAACG,QAAQ,EAAT,CAAb;AACH;AACD,UAAMI,QAAQ,GAAGF,IAAI,KAAKH,YAA1B;AACA,UAAM/Q,IAAI,GAAG2Q,mBAAmB,CAAC1oB,IAApB,CAAyBkpB,GAAzB;AACP,YADO;AAEPC,cAAQ,IAAIR,oBAAoB,CAAC3oB,IAArB,CAA0BkpB,GAA1B,CAAZ;AACI,aADJ;AAEI,eAJV;AAKAV,YAAM,CAACpjB,IAAP,CAAY,EAAEmD,KAAK,EAAE2gB,GAAT,EAAcnR,IAAI,EAAJA,IAAd,EAAZ;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AAxBA,SAyBK;AACDiR,YAAI,IAAIC,IAAR;AACH;AACJ;AACDD,MAAI,IAAIR,MAAM,CAACpjB,IAAP,CAAY,EAAE2S,IAAI,EAAE,MAAR,EAAgBxP,KAAK,EAAEygB,IAAvB,EAAZ,CAAR;AACA,SAAOR,MAAP;AACH;AACD,SAASC,OAAT,CAAiBD,MAAjB,EAAyB9c,MAAzB,EAAiC;AAC7B,MAAM0d,QAAQ,GAAG,EAAjB;AACA,MAAI7jB,KAAK,GAAG,CAAZ;AACA,MAAM8jB,IAAI,GAAGpkB,OAAO,CAACyG,MAAD,CAAP;AACP,QADO;AAEPyc,UAAQ,CAACzc,MAAD,CAAR;AACI,SADJ;AAEI,WAJV;AAKA,MAAI2d,IAAI,KAAK,SAAb,EAAwB;AACpB,WAAOD,QAAP;AACH;AACD,SAAO7jB,KAAK,GAAGijB,MAAM,CAACroB,MAAtB,EAA8B;AAC1B,QAAMkB,KAAK,GAAGmnB,MAAM,CAACjjB,KAAD,CAApB;AACA,YAAQlE,KAAK,CAAC0W,IAAd;AACI,WAAK,MAAL;AACIqR,gBAAQ,CAAChkB,IAAT,CAAc/D,KAAK,CAACkH,KAApB;AACA;AACJ,WAAK,MAAL;AACI6gB,gBAAQ,CAAChkB,IAAT,CAAcsG,MAAM,CAACqD,QAAQ,CAAC1N,KAAK,CAACkH,KAAP,EAAc,EAAd,CAAT,CAApB;AACA;AACJ,WAAK,OAAL;AACI,YAAI8gB,IAAI,KAAK,OAAb,EAAsB;AAClBD,kBAAQ,CAAChkB,IAAT,CAAcsG,MAAM,CAACrK,KAAK,CAACkH,KAAP,CAApB;AACH,SAFD;AAGK;AACD,cAAIqJ,IAAJ,EAA2C;AACvC2D,mBAAO,CAACC,IAAR,0BAA+BnU,KAAK,CAAC0W,IAArC,oCAAmEsR,IAAnE;AACH;AACJ;AACD;AACJ,WAAK,SAAL;AACI,YAAIzX,IAAJ,EAA2C;AACvC2D,iBAAO,CAACC,IAAR;AACH;AACD,cArBR;;AAuBAjQ,SAAK;AACR;AACD,SAAO6jB,QAAP;AACH;;AAED,IAAMrf,cAAc,GAAG,SAAvB,C;AACA,IAAMC,cAAc,GAAG,SAAvB,C;AACA,IAAMC,SAAS,GAAG,IAAlB,C;AACA,IAAMC,SAAS,GAAG,IAAlB,C;AACA,IAAMC,SAAS,GAAG,IAAlB,C;AACA,IAAMnH,cAAc,GAAGD,MAAM,CAACT,SAAP,CAAiBU,cAAxC;AACA,IAAMO,MAAM,GAAG,SAATA,MAAS,CAAC0hB,GAAD,EAAMzhB,GAAN,UAAcR,cAAc,CAACM,IAAf,CAAoB2hB,GAApB,EAAyBzhB,GAAzB,CAAd,EAAf;AACA,IAAM8lB,gBAAgB,GAAG,IAAIjB,aAAJ,EAAzB;AACA,SAAS7b,OAAT,CAAiB3M,GAAjB,EAAsB4M,KAAtB,EAA6B;AACzB,SAAO,CAAC,CAACA,KAAK,CAACC,IAAN,CAAW,UAACC,IAAD,UAAU9M,GAAG,CAACY,OAAJ,CAAYkM,IAAZ,MAAsB,CAAC,CAAjC,EAAX,CAAT;AACH;AACD,SAASC,UAAT,CAAoB/M,GAApB,EAAyB4M,KAAzB,EAAgC;AAC5B,SAAOA,KAAK,CAACC,IAAN,CAAW,UAACC,IAAD,UAAU9M,GAAG,CAACY,OAAJ,CAAYkM,IAAZ,MAAsB,CAAhC,EAAX,CAAP;AACH;AACD,SAASrC,eAAT,CAAyBD,MAAzB,EAAiCD,QAAjC,EAA2C;AACvC,MAAI,CAACC,MAAL,EAAa;AACT;AACH;AACDA,QAAM,GAAGA,MAAM,CAACwC,IAAP,GAAc9M,OAAd,CAAsB,IAAtB,EAA4B,GAA5B,CAAT;AACA,MAAIqK,QAAQ,IAAIA,QAAQ,CAACC,MAAD,CAAxB,EAAkC;AAC9B,WAAOA,MAAP;AACH;AACDA,QAAM,GAAGA,MAAM,CAACyC,WAAP,EAAT;AACA,MAAIzC,MAAM,CAAC5J,OAAP,CAAe,IAAf,MAAyB,CAA7B,EAAgC;AAC5B,QAAI4J,MAAM,CAAC5J,OAAP,CAAe,OAAf,IAA0B,CAAC,CAA/B,EAAkC;AAC9B,aAAOsJ,cAAP;AACH;AACD,QAAIM,MAAM,CAAC5J,OAAP,CAAe,OAAf,IAA0B,CAAC,CAA/B,EAAkC;AAC9B,aAAOuJ,cAAP;AACH;AACD,QAAIwC,OAAO,CAACnC,MAAD,EAAS,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,MAAtB,CAAT,CAAX,EAAoD;AAChD,aAAOL,cAAP;AACH;AACD,WAAOD,cAAP;AACH;AACD,MAAMgD,IAAI,GAAGH,UAAU,CAACvC,MAAD,EAAS,CAACJ,SAAD,EAAYC,SAAZ,EAAuBC,SAAvB,CAAT,CAAvB;AACA,MAAI4C,IAAJ,EAAU;AACN,WAAOA,IAAP;AACH;AACJ,C;AACKwc,I;AACF,uBAAsE,KAAxDlf,MAAwD,SAAxDA,MAAwD,CAAhDmf,cAAgD,SAAhDA,cAAgD,CAAhCpf,QAAgC,SAAhCA,QAAgC,CAAtBqf,OAAsB,SAAtBA,OAAsB,CAAbC,QAAa,SAAbA,QAAa;AAClE,SAAKrf,MAAL,GAAcJ,SAAd;AACA,SAAKuf,cAAL,GAAsBvf,SAAtB;AACA,SAAKhI,OAAL,GAAe,EAAf;AACA,SAAKmI,QAAL,GAAgB,EAAhB;AACA,SAAKuf,QAAL,GAAgB,EAAhB;AACA,QAAIH,cAAJ,EAAoB;AAChB,WAAKA,cAAL,GAAsBA,cAAtB;AACH;AACD,SAAKE,QAAL,GAAgBA,QAAQ,IAAIJ,gBAA5B;AACA,SAAKlf,QAAL,GAAgBA,QAAQ,IAAI,EAA5B;AACA,SAAKuB,SAAL,CAAetB,MAAM,IAAIJ,SAAzB;AACA,QAAIwf,OAAJ,EAAa;AACT,WAAKpe,WAAL,CAAiBoe,OAAjB;AACH;AACJ,G;AACSpf,U,EAAQ;AACd,UAAMkD,SAAS,GAAG,KAAKlD,MAAvB;AACA,WAAKA,MAAL,GAAcC,eAAe,CAACD,MAAD,EAAS,KAAKD,QAAd,CAAf,IAA0C,KAAKof,cAA7D;AACA,UAAI,CAAC,KAAKpf,QAAL,CAAc,KAAKC,MAAnB,CAAL,EAAiC;AAC7B;AACA,aAAKD,QAAL,CAAc,KAAKC,MAAnB,IAA6B,EAA7B;AACH;AACD,WAAKpI,OAAL,GAAe,KAAKmI,QAAL,CAAc,KAAKC,MAAnB,CAAf;AACA;AACA,UAAIkD,SAAS,KAAK,KAAKlD,MAAvB,EAA+B;AAC3B,aAAKsf,QAAL,CAAcplB,OAAd,CAAsB,UAACklB,OAAD,EAAa;AAC/BA,iBAAO,CAAC,KAAI,CAACpf,MAAN,EAAckD,SAAd,CAAP;AACH,SAFD;AAGH;AACJ,K;AACW;AACR,aAAO,KAAKlD,MAAZ;AACH,K;AACWnH,M,EAAI;AACZ,UAAMqC,KAAK,GAAG,KAAKokB,QAAL,CAAcvkB,IAAd,CAAmBlC,EAAnB,IAAyB,CAAvC;AACA,aAAO,YAAM;AACT,cAAI,CAACymB,QAAL,CAAcnkB,MAAd,CAAqBD,KAArB,EAA4B,CAA5B;AACH,OAFD;AAGH,K;AACG8E,U,EAAQpI,O,EAA0B,KAAjB2nB,QAAiB,uEAAN,IAAM;AAClC,UAAM/e,WAAW,GAAG,KAAKT,QAAL,CAAcC,MAAd,CAApB;AACA,UAAIQ,WAAJ,EAAiB;AACb,YAAI+e,QAAJ,EAAc;AACV7mB,gBAAM,CAAC+F,MAAP,CAAc+B,WAAd,EAA2B5I,OAA3B;AACH,SAFD;AAGK;AACDc,gBAAM,CAACsB,IAAP,CAAYpC,OAAZ,EAAqBsC,OAArB,CAA6B,UAACf,GAAD,EAAS;AAClC,gBAAI,CAACD,MAAM,CAACsH,WAAD,EAAcrH,GAAd,CAAX,EAA+B;AAC3BqH,yBAAW,CAACrH,GAAD,CAAX,GAAmBvB,OAAO,CAACuB,GAAD,CAA1B;AACH;AACJ,WAJD;AAKH;AACJ,OAXD;AAYK;AACD,aAAK4G,QAAL,CAAcC,MAAd,IAAwBpI,OAAxB;AACH;AACJ,K;AACCA,W,EAASyJ,M,EAAQ6c,U,EAAY;AAC3B,aAAO,KAAKmB,QAAL,CAAcG,WAAd,CAA0B5nB,OAA1B,EAAmCyJ,MAAnC,EAA2C6c,UAA3C,EAAuDpnB,IAAvD,CAA4D,EAA5D,CAAP;AACH,K;AACCqC,O,EAAK6G,M,EAAQqB,M,EAAQ;AACnB,UAAIzJ,OAAO,GAAG,KAAKA,OAAnB;AACA,UAAI,OAAOoI,MAAP,KAAkB,QAAtB,EAAgC;AAC5BA,cAAM,GAAGC,eAAe,CAACD,MAAD,EAAS,KAAKD,QAAd,CAAxB;AACAC,cAAM,KAAKpI,OAAO,GAAG,KAAKmI,QAAL,CAAcC,MAAd,CAAf,CAAN;AACH,OAHD;AAIK;AACDqB,cAAM,GAAGrB,MAAT;AACH;AACD,UAAI,CAAC9G,MAAM,CAACtB,OAAD,EAAUuB,GAAV,CAAX,EAA2B;AACvB+R,eAAO,CAACC,IAAR,iDAAsDhS,GAAtD;AACA,eAAOA,GAAP;AACH;AACD,aAAO,KAAKkmB,QAAL,CAAcG,WAAd,CAA0B5nB,OAAO,CAACuB,GAAD,CAAjC,EAAwCkI,MAAxC,EAAgDvK,IAAhD,CAAqD,EAArD,CAAP;AACH,K;;;AAGL,SAAS2oB,cAAT,CAAwBhe,KAAxB,EAA+Bf,IAA/B,EAAqC;AACjC;AACA,MAAIe,KAAK,CAACI,YAAV,EAAwB;AACpB;AACAJ,SAAK,CAACI,YAAN,CAAmB,UAAC6d,SAAD,EAAe;AAC9Bhf,UAAI,CAACY,SAAL,CAAeoe,SAAf;AACH,KAFD;AAGH,GALD;AAMK;AACDje,SAAK,CAACke,MAAN,CAAa,oBAAMle,KAAK,CAACuB,OAAZ,EAAb,EAAkC,UAAC0c,SAAD,EAAe;AAC7Chf,UAAI,CAACY,SAAL,CAAeoe,SAAf;AACH,KAFD;AAGH;AACJ;AACD,SAASE,gBAAT,GAA4B;AACxB,MAAI,OAAOnC,GAAP,KAAe,WAAf,IAA8BA,GAAG,CAAClc,SAAtC,EAAiD;AAC7C,WAAOkc,GAAG,CAAClc,SAAJ,EAAP;AACH;AACD;AACA,MAAI,OAAO8B,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAAC9B,SAA5C,EAAuD;AACnD,WAAO8B,MAAM,CAAC9B,SAAP,EAAP;AACH;AACD,SAAO3B,SAAP;AACH;AACD,SAASigB,WAAT,CAAqB7f,MAArB,EAAqE,KAAxCD,QAAwC,uEAA7B,EAA6B,KAAzBof,cAAyB,uDAATC,OAAS;AACjE;AACA,MAAI,OAAOpf,MAAP,KAAkB,QAAtB,EAAgC;AACP;AACjBD,YADiB;AAEjBC,UAFiB,CADO,CAC3BA,MAD2B,YACnBD,QADmB;;AAK/B;AACD,MAAI,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;AAC5B;AACAA,UAAM,GAAG4f,gBAAgB,EAAzB;AACH;AACD,MAAI,OAAOT,cAAP,KAA0B,QAA9B,EAAwC;AACpCA,kBAAc;AACT,WAAO7e,WAAP,KAAuB,WAAvB,IAAsCA,WAAW,CAAC6e,cAAnD;AACIvf,aAFR;AAGH;AACD,MAAMc,IAAI,GAAG,IAAIwe,IAAJ,CAAS;AAClBlf,UAAM,EAANA,MADkB;AAElBmf,kBAAc,EAAdA,cAFkB;AAGlBpf,YAAQ,EAARA,QAHkB;AAIlBqf,WAAO,EAAPA,OAJkB,EAAT,CAAb;;AAMA,MAAIze,EAAC,GAAG,WAACxH,GAAD,EAAMkI,MAAN,EAAiB;AACrB,QAAI,OAAOwB,MAAP,KAAkB,UAAtB,EAAkC;AAC9B;AACA;AACAlC,QAAC,GAAG,WAAUxH,GAAV,EAAekI,MAAf,EAAuB;AACvB,eAAOX,IAAI,CAACC,CAAL,CAAOxH,GAAP,EAAYkI,MAAZ,CAAP;AACH,OAFD;AAGH,KAND;AAOK;AACD,UAAIye,kBAAkB,GAAG,KAAzB;AACAnf,QAAC,GAAG,WAAUxH,GAAV,EAAekI,MAAf,EAAuB;AACvB,YAAMI,KAAK,GAAGoB,MAAM,GAAGE,GAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAItB,KAAJ,EAAW;AACP;AACAA,eAAK,CAACuB,OAAN;AACA,cAAI,CAAC8c,kBAAL,EAAyB;AACrBA,8BAAkB,GAAG,IAArB;AACAL,0BAAc,CAAChe,KAAD,EAAQf,IAAR,CAAd;AACH;AACJ;AACD,eAAOA,IAAI,CAACC,CAAL,CAAOxH,GAAP,EAAYkI,MAAZ,CAAP;AACH,OAxBD;AAyBH;AACD,WAAOV,EAAC,CAACxH,GAAD,EAAMkI,MAAN,CAAR;AACH,GArCD;AAsCA,SAAO;AACHX,QAAI,EAAJA,IADG;AAEHqf,KAFG,aAEDnoB,OAFC,EAEQyJ,MAFR,EAEgB6c,UAFhB,EAE4B;AAC3B,aAAOxd,IAAI,CAACqf,CAAL,CAAOnoB,OAAP,EAAgByJ,MAAhB,EAAwB6c,UAAxB,CAAP;AACH,KAJE;AAKHvd,KALG,aAKDxH,GALC,EAKIkI,MALJ,EAKY;AACX,aAAOV,EAAC,CAACxH,GAAD,EAAMkI,MAAN,CAAR;AACH,KAPE;AAQH2e,OARG,eAQChgB,MARD,EAQSpI,OART,EAQmC,KAAjB2nB,QAAiB,uEAAN,IAAM;AAClC,aAAO7e,IAAI,CAACsf,GAAL,CAAShgB,MAAT,EAAiBpI,OAAjB,EAA0B2nB,QAA1B,CAAP;AACH,KAVE;AAWHrd,SAXG,iBAWGrJ,EAXH,EAWO;AACN,aAAO6H,IAAI,CAACM,WAAL,CAAiBnI,EAAjB,CAAP;AACH,KAbE;AAcH0I,aAdG,uBAcS;AACR,aAAOb,IAAI,CAACa,SAAL,EAAP;AACH,KAhBE;AAiBHD,aAjBG,qBAiBOoe,SAjBP,EAiBkB;AACjB,aAAOhf,IAAI,CAACY,SAAL,CAAeoe,SAAf,CAAP;AACH,KAnBE,EAAP;;AAqBH;;AAED,IAAMO,QAAQ,GAAG,SAAXA,QAAW,CAACrF,GAAD,UAAS,OAAOA,GAAP,KAAe,QAAxB,EAAjB,C;AACA,IAAIyE,QAAJ;AACA,SAASa,WAAT,CAAqBC,OAArB,EAA8BjC,UAA9B,EAA0C;AACtC,MAAI,CAACmB,QAAL,EAAe;AACXA,YAAQ,GAAG,IAAIrB,aAAJ,EAAX;AACH;AACD,SAAOoC,WAAW,CAACD,OAAD,EAAU,UAACA,OAAD,EAAUhnB,GAAV,EAAkB;AAC1C,QAAM+E,KAAK,GAAGiiB,OAAO,CAAChnB,GAAD,CAArB;AACA,QAAI8mB,QAAQ,CAAC/hB,KAAD,CAAZ,EAAqB;AACjB,UAAImiB,SAAS,CAACniB,KAAD,EAAQggB,UAAR,CAAb,EAAkC;AAC9B,eAAO,IAAP;AACH;AACJ,KAJD;AAKK;AACD,aAAOgC,WAAW,CAAChiB,KAAD,EAAQggB,UAAR,CAAlB;AACH;AACJ,GAViB,CAAlB;AAWH;AACD,SAASoC,aAAT,CAAuBH,OAAvB,EAAgC9e,MAAhC,EAAwC6c,UAAxC,EAAoD;AAChD,MAAI,CAACmB,QAAL,EAAe;AACXA,YAAQ,GAAG,IAAIrB,aAAJ,EAAX;AACH;AACDoC,aAAW,CAACD,OAAD,EAAU,UAACA,OAAD,EAAUhnB,GAAV,EAAkB;AACnC,QAAM+E,KAAK,GAAGiiB,OAAO,CAAChnB,GAAD,CAArB;AACA,QAAI8mB,QAAQ,CAAC/hB,KAAD,CAAZ,EAAqB;AACjB,UAAImiB,SAAS,CAACniB,KAAD,EAAQggB,UAAR,CAAb,EAAkC;AAC9BiC,eAAO,CAAChnB,GAAD,CAAP,GAAeonB,UAAU,CAACriB,KAAD,EAAQmD,MAAR,EAAgB6c,UAAhB,CAAzB;AACH;AACJ,KAJD;AAKK;AACDoC,mBAAa,CAACpiB,KAAD,EAAQmD,MAAR,EAAgB6c,UAAhB,CAAb;AACH;AACJ,GAVU,CAAX;AAWA,SAAOiC,OAAP;AACH;AACD,SAASK,kBAAT,CAA4BC,OAA5B,SAAuE,KAAhCzgB,MAAgC,SAAhCA,MAAgC,CAAxBO,OAAwB,SAAxBA,OAAwB,CAAf2d,UAAe,SAAfA,UAAe;AACnE,MAAI,CAACmC,SAAS,CAACI,OAAD,EAAUvC,UAAV,CAAd,EAAqC;AACjC,WAAOuC,OAAP;AACH;AACD,MAAI,CAACpB,QAAL,EAAe;AACXA,YAAQ,GAAG,IAAIrB,aAAJ,EAAX;AACH;AACD,MAAM0C,YAAY,GAAG,EAArB;AACAhoB,QAAM,CAACsB,IAAP,CAAYuG,OAAZ,EAAqBrG,OAArB,CAA6B,UAACoC,IAAD,EAAU;AACnC,QAAIA,IAAI,KAAK0D,MAAb,EAAqB;AACjB0gB,kBAAY,CAAC3lB,IAAb,CAAkB;AACdiF,cAAM,EAAE1D,IADM;AAEd+E,cAAM,EAAEd,OAAO,CAACjE,IAAD,CAFD,EAAlB;;AAIH;AACJ,GAPD;AAQAokB,cAAY,CAACC,OAAb,CAAqB,EAAE3gB,MAAM,EAANA,MAAF,EAAUqB,MAAM,EAAEd,OAAO,CAACP,MAAD,CAAzB,EAArB;AACA,MAAI;AACA,WAAOvI,IAAI,CAAC6Z,SAAL,CAAesP,cAAc,CAACnpB,IAAI,CAACC,KAAL,CAAW+oB,OAAX,CAAD,EAAsBC,YAAtB,EAAoCxC,UAApC,CAA7B,EAA8E,IAA9E,EAAoF,CAApF,CAAP;AACH;AACD,SAAOjR,CAAP,EAAU,CAAG;AACb,SAAOwT,OAAP;AACH;AACD,SAASJ,SAAT,CAAmBniB,KAAnB,EAA0BggB,UAA1B,EAAsC;AAClC,SAAOhgB,KAAK,CAAC9H,OAAN,CAAc8nB,UAAU,CAAC,CAAD,CAAxB,IAA+B,CAAC,CAAvC;AACH;AACD,SAASqC,UAAT,CAAoBriB,KAApB,EAA2BmD,MAA3B,EAAmC6c,UAAnC,EAA+C;AAC3C,SAAOmB,QAAQ,CAACG,WAAT,CAAqBthB,KAArB,EAA4BmD,MAA5B,EAAoC6c,UAApC,EAAgDpnB,IAAhD,CAAqD,EAArD,CAAP;AACH;AACD,SAAS+pB,YAAT,CAAsBV,OAAtB,EAA+BhnB,GAA/B,EAAoCunB,YAApC,EAAkDxC,UAAlD,EAA8D;AAC1D,MAAMhgB,KAAK,GAAGiiB,OAAO,CAAChnB,GAAD,CAArB;AACA,MAAI8mB,QAAQ,CAAC/hB,KAAD,CAAZ,EAAqB;AACjB;AACA,QAAImiB,SAAS,CAACniB,KAAD,EAAQggB,UAAR,CAAb,EAAkC;AAC9BiC,aAAO,CAAChnB,GAAD,CAAP,GAAeonB,UAAU,CAACriB,KAAD,EAAQwiB,YAAY,CAAC,CAAD,CAAZ,CAAgBrf,MAAxB,EAAgC6c,UAAhC,CAAzB;AACA,UAAIwC,YAAY,CAAC5qB,MAAb,GAAsB,CAA1B,EAA6B;AACzB;AACA,YAAMgrB,YAAY,GAAIX,OAAO,CAAChnB,GAAG,GAAG,SAAP,CAAP,GAA2B,EAAjD;AACAunB,oBAAY,CAACxmB,OAAb,CAAqB,UAAC6mB,UAAD,EAAgB;AACjCD,sBAAY,CAACC,UAAU,CAAC/gB,MAAZ,CAAZ,GAAkCugB,UAAU,CAACriB,KAAD,EAAQ6iB,UAAU,CAAC1f,MAAnB,EAA2B6c,UAA3B,CAA5C;AACH,SAFD;AAGH;AACJ;AACJ,GAZD;AAaK;AACD0C,kBAAc,CAAC1iB,KAAD,EAAQwiB,YAAR,EAAsBxC,UAAtB,CAAd;AACH;AACJ;AACD,SAAS0C,cAAT,CAAwBT,OAAxB,EAAiCO,YAAjC,EAA+CxC,UAA/C,EAA2D;AACvDkC,aAAW,CAACD,OAAD,EAAU,UAACA,OAAD,EAAUhnB,GAAV,EAAkB;AACnC0nB,gBAAY,CAACV,OAAD,EAAUhnB,GAAV,EAAeunB,YAAf,EAA6BxC,UAA7B,CAAZ;AACH,GAFU,CAAX;AAGA,SAAOiC,OAAP;AACH;AACD,SAASC,WAAT,CAAqBD,OAArB,EAA8Ba,IAA9B,EAAoC;AAChC,MAAIpmB,OAAO,CAACulB,OAAD,CAAX,EAAsB;AAClB,SAAK,IAAIhqB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgqB,OAAO,CAACrqB,MAA5B,EAAoCK,CAAC,EAArC,EAAyC;AACrC,UAAI6qB,IAAI,CAACb,OAAD,EAAUhqB,CAAV,CAAR,EAAsB;AAClB,eAAO,IAAP;AACH;AACJ;AACJ,GAND;AAOK,MAAI2nB,QAAQ,CAACqC,OAAD,CAAZ,EAAuB;AACxB,SAAK,IAAMhnB,GAAX,IAAkBgnB,OAAlB,EAA2B;AACvB,UAAIa,IAAI,CAACb,OAAD,EAAUhnB,GAAV,CAAR,EAAwB;AACpB,eAAO,IAAP;AACH;AACJ;AACJ;AACD,SAAO,KAAP;AACH;;AAED,SAAS8nB,aAAT,CAAuB1gB,OAAvB,EAAgC;AAC5B,SAAO,UAACP,MAAD,EAAY;AACf,QAAI,CAACA,MAAL,EAAa;AACT,aAAOA,MAAP;AACH;AACDA,UAAM,GAAGC,eAAe,CAACD,MAAD,CAAf,IAA2BA,MAApC;AACA,WAAOkhB,kBAAkB,CAAClhB,MAAD,CAAlB,CAA2BqC,IAA3B,CAAgC,UAACrC,MAAD,UAAYO,OAAO,CAACnK,OAAR,CAAgB4J,MAAhB,IAA0B,CAAC,CAAvC,EAAhC,CAAP;AACH,GAND;AAOH;AACD,SAASkhB,kBAAT,CAA4BlhB,MAA5B,EAAoC;AAChC,MAAMmhB,KAAK,GAAG,EAAd;AACA,MAAMhD,MAAM,GAAGne,MAAM,CAACvJ,KAAP,CAAa,GAAb,CAAf;AACA,SAAO0nB,MAAM,CAACroB,MAAd,EAAsB;AAClBqrB,SAAK,CAACpmB,IAAN,CAAWojB,MAAM,CAACrnB,IAAP,CAAY,GAAZ,CAAX;AACAqnB,UAAM,CAACiD,GAAP;AACH;AACD,SAAOD,KAAP;AACH,C;;;;;;;;;;;;;ACncD;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC;;AAElC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA,sBAAsB,+BAA+B;AACrD,sBAAsB,iBAAiB;AACvC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,kDAAkD,iCAAiC,EAAE;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,6BAA6B,cAAc;;AAE3C;;AAEA;AACA;AACA;AACA,6BAA6B,UAAU;;AAEvC;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC,kCAAkC;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,aAAoB;;AAErC;AACA;AACA;AACA,YAAY,aAAoB;;AAEhC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA,oCAAoC;AACpC;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA,iCAAiC;AACjC,uCAAuC,wBAAwB,EAAE;AACjE,0BAA0B;;AAE1B;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,wCAAwC,EAAE;AAC1C;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,+BAA+B,oBAAoB,EAAE;AACrD;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B,SAAS,qBAAqB;;AAExD;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;;AAEA,iBAAiB,iBAAiB;AAClC;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,uBAAuB;AACzD,iCAAiC,sBAAsB;AACvD;AACA,kBAAkB;AAClB,MAAM,IAAqC;AAC3C;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAoB;AACtC;AACA;AACA,mBAAmB;AACnB;AACA;AACA,iBAAiB,uBAAuB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,OAAO,UAAU,IAAqC;AACtD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,mBAAmB,mBAAmB;AACtC,+BAA+B;AAC/B;AACA,GAAG;AACH;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,oCAAoC;AACpC;AACA,qCAAqC;AACrC;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAEQ;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6CAA6C,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,6CAA6C,qCAAqC,EAAE;AACpF;;AAEA;AACA;AACA;;AAEA,oCAAoC,yCAAyC,EAAE;AAC/E;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,kBAAkB;AAC3C;AACA;AACA,4BAA4B;AAC5B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,sDAAsD,EAAE;AACtF;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;;AAEA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,yBAAyB;AAC1C,GAAG;AACH;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA;;AAEA;AACA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mBAAmB;AACxC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,qBAAqB;AAClC;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO,MAAM,EAEN;AACP,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,iBAAiB;AACpC;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,IAAqC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,sBAAsB,mBAAmB;AACzC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC,uCAAuC;AACvC;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB,sCAAsC;AACtC;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,4CAA4C,eAAe;AAC3D,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,kDAAkD;AAClD,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;AACA,KAAK,UAAU,KAAqC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,qCAAqC,gEAAgE;AACrG;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,4BAA4B,+BAA+B;AAC3D,4BAA4B,+BAA+B;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C,kDAAkD;AAClD;AACA;AACA,mCAAmC;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,sEAAsE;;AAEtE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,uFAAuF;AAC5F;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C,iBAAiB,yBAAyB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,+BAA+B;AAClC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,oBAAoB,oBAAoB;AACxC,sBAAsB,4BAA4B;AAClD;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,mBAAmB;AACnB,yBAAyB;AACzB;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,6CAA6C;AAC9E;AACA;AACA,6CAA6C,4CAA4C;;AAEzF;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,GAAG,MAAM,EAGN;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK,2CAA2C,8BAA8B,EAAE;;AAEhF;AACA,wCAAwC,OAAO;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;;AAEL;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,KAAqC;AACrD;AACA,oBAAoB,SAAI;AACxB;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,OAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B,kBAAkB;AAClB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM,IAAqC;AAC3C;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,OAAO;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,oBAAoB,EAAE;;AAEpD;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,oBAAoB;AACpB;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,oBAAoB,KAAqC;AACzD;AACA,MAAM,SAAE;AACR;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C,qBAAqB,+BAA+B;AACpD;AACA;AACA,GAAG;AACH,yBAAyB;AACzB;AACA,sBAAsB,iCAAiC;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kGAAkG;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,UAAU,IAAqC;AACpD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB,oBAAoB;AACzC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,8BAA8B;AAC9B,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,sCAAsC;AACtC,8C;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,eAAe;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC;;AAEjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,YAAY,KAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,0CAA0C,2BAA2B,EAAE;AACvE,KAAK;AACL;AACA,0CAA0C,4BAA4B,EAAE;AACxE,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,yBAAyB;AACzB;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,SAAS;AACT;AACA;AACA,aAAa;AACb;AACA;AACA,iBAAiB;AACjB;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,YAAY,uGAAW;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,gCAAgC,EAAE;AAC5E;;AAEA;AACA;AACA;AACA;AACA,WAAW,uGAAW;AACtB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,uGAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,0CAA0C;;AAE1C;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA,sCAAsC;AACtC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA,KAAK;AACL;AACA;AACA,UAAU,uGAAW;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,qDAAqD,EAAE,SAAS;AACtH;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,OAAO;AACxC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEe,kEAAG,EAAC;;;;;;;;;;;;;;;;;;;;;;;;AC95LnBvD,MAAM,CAACC,OAAP,GAAiBwD,mBAAO,CAAC,6BAAD,CAAxB,C;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,iBAAiB,mBAAO,CAAC,mBAAW;;AAEpC;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;ACpCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,WAAW;AACX;;AAEA;AACA;AACA,wCAAwC,WAAW;AACnD;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,SAAS;AACT;AACA;AACA;AACA;;AAEA;;AAEA,SAAS;AACT;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oCAAoC,cAAc;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC,kBAAkB;AACnD;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB;;AAEjB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,YAAY;AACZ;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,8CAA8C,QAAQ;AACtD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA,WAAW;AACX;AACA;AACA;;AAEA,WAAW;AACX;AACA;AACA;;AAEA,WAAW;AACX;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,8CAA8C,QAAQ;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,KAAK;;AAEL;AACA,8CAA8C,QAAQ;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,8CAA8C,QAAQ;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH", "file": "common/vendor.js", "sourcesContent": ["import { initVueI18n } from '@dcloudio/uni-i18n';\r\nimport Vue from 'vue';\r\n\r\nlet realAtob;\r\n\r\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\r\n\r\nif (typeof atob !== 'function') {\r\n  realAtob = function (str) {\r\n    str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\r\n    if (!b64re.test(str)) { throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\") }\r\n\r\n    // Adding the padding if missing, for semplicity\r\n    str += '=='.slice(2 - (str.length & 3));\r\n    var bitmap; var result = ''; var r1; var r2; var i = 0;\r\n    for (; i < str.length;) {\r\n      bitmap = b64.indexOf(str.charAt(i++)) << 18 | b64.indexOf(str.charAt(i++)) << 12 |\r\n                    (r1 = b64.indexOf(str.charAt(i++))) << 6 | (r2 = b64.indexOf(str.charAt(i++)));\r\n\r\n      result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255)\r\n        : r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255)\r\n          : String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);\r\n    }\r\n    return result\r\n  };\r\n} else {\r\n  // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\r\n  realAtob = atob;\r\n}\r\n\r\nfunction b64DecodeUnicode (str) {\r\n  return decodeURIComponent(realAtob(str).split('').map(function (c) {\r\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\r\n  }).join(''))\r\n}\r\n\r\nfunction getCurrentUserInfo () {\r\n  const token = ( wx).getStorageSync('uni_id_token') || '';\r\n  const tokenArr = token.split('.');\r\n  if (!token || tokenArr.length !== 3) {\r\n    return {\r\n      uid: null,\r\n      role: [],\r\n      permission: [],\r\n      tokenExpired: 0\r\n    }\r\n  }\r\n  let userInfo;\r\n  try {\r\n    userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\r\n  } catch (error) {\r\n    throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message)\r\n  }\r\n  userInfo.tokenExpired = userInfo.exp * 1000;\r\n  delete userInfo.exp;\r\n  delete userInfo.iat;\r\n  return userInfo\r\n}\r\n\r\nfunction uniIdMixin (Vue) {\r\n  Vue.prototype.uniIDHasRole = function (roleId) {\r\n    const {\r\n      role\r\n    } = getCurrentUserInfo();\r\n    return role.indexOf(roleId) > -1\r\n  };\r\n  Vue.prototype.uniIDHasPermission = function (permissionId) {\r\n    const {\r\n      permission\r\n    } = getCurrentUserInfo();\r\n    return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1\r\n  };\r\n  Vue.prototype.uniIDTokenValid = function () {\r\n    const {\r\n      tokenExpired\r\n    } = getCurrentUserInfo();\r\n    return tokenExpired > Date.now()\r\n  };\r\n}\r\n\r\nconst _toString = Object.prototype.toString;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction isFn (fn) {\r\n  return typeof fn === 'function'\r\n}\r\n\r\nfunction isStr (str) {\r\n  return typeof str === 'string'\r\n}\r\n\r\nfunction isPlainObject (obj) {\r\n  return _toString.call(obj) === '[object Object]'\r\n}\r\n\r\nfunction hasOwn (obj, key) {\r\n  return hasOwnProperty.call(obj, key)\r\n}\r\n\r\nfunction noop () { }\r\n\r\n/**\r\n * Create a cached version of a pure function.\r\n */\r\nfunction cached (fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn (str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str))\r\n  }\r\n}\r\n\r\n/**\r\n * Camelize a hyphen-delimited string.\r\n */\r\nconst camelizeRE = /-(\\w)/g;\r\nconst camelize = cached((str) => {\r\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')\r\n});\r\n\r\nfunction sortObject (obj) {\r\n  const sortObj = {};\r\n  if (isPlainObject(obj)) {\r\n    Object.keys(obj).sort().forEach(key => {\r\n      sortObj[key] = obj[key];\r\n    });\r\n  }\r\n  return !Object.keys(sortObj) ? obj : sortObj\r\n}\r\n\r\nconst HOOKS = [\r\n  'invoke',\r\n  'success',\r\n  'fail',\r\n  'complete',\r\n  'returnValue'\r\n];\r\n\r\nconst globalInterceptors = {};\r\nconst scopedInterceptors = {};\r\n\r\nfunction mergeHook (parentVal, childVal) {\r\n  const res = childVal\r\n    ? parentVal\r\n      ? parentVal.concat(childVal)\r\n      : Array.isArray(childVal)\r\n        ? childVal : [childVal]\r\n    : parentVal;\r\n  return res\r\n    ? dedupeHooks(res)\r\n    : res\r\n}\r\n\r\nfunction dedupeHooks (hooks) {\r\n  const res = [];\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    if (res.indexOf(hooks[i]) === -1) {\r\n      res.push(hooks[i]);\r\n    }\r\n  }\r\n  return res\r\n}\r\n\r\nfunction removeHook (hooks, hook) {\r\n  const index = hooks.indexOf(hook);\r\n  if (index !== -1) {\r\n    hooks.splice(index, 1);\r\n  }\r\n}\r\n\r\nfunction mergeInterceptorHook (interceptor, option) {\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      interceptor[hook] = mergeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction removeInterceptorHook (interceptor, option) {\r\n  if (!interceptor || !option) {\r\n    return\r\n  }\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      removeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction addInterceptor (method, option) {\r\n  if (typeof method === 'string' && isPlainObject(option)) {\r\n    mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), option);\r\n  } else if (isPlainObject(method)) {\r\n    mergeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction removeInterceptor (method, option) {\r\n  if (typeof method === 'string') {\r\n    if (isPlainObject(option)) {\r\n      removeInterceptorHook(scopedInterceptors[method], option);\r\n    } else {\r\n      delete scopedInterceptors[method];\r\n    }\r\n  } else if (isPlainObject(method)) {\r\n    removeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction wrapperHook (hook) {\r\n  return function (data) {\r\n    return hook(data) || data\r\n  }\r\n}\r\n\r\nfunction isPromise (obj) {\r\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function'\r\n}\r\n\r\nfunction queue (hooks, data) {\r\n  let promise = false;\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    const hook = hooks[i];\r\n    if (promise) {\r\n      promise = Promise.resolve(wrapperHook(hook));\r\n    } else {\r\n      const res = hook(data);\r\n      if (isPromise(res)) {\r\n        promise = Promise.resolve(res);\r\n      }\r\n      if (res === false) {\r\n        return {\r\n          then () { }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return promise || {\r\n    then (callback) {\r\n      return callback(data)\r\n    }\r\n  }\r\n}\r\n\r\nfunction wrapperOptions (interceptor, options = {}) {\r\n  ['success', 'fail', 'complete'].forEach(name => {\r\n    if (Array.isArray(interceptor[name])) {\r\n      const oldCallback = options[name];\r\n      options[name] = function callbackInterceptor (res) {\r\n        queue(interceptor[name], res).then((res) => {\r\n          /* eslint-disable no-mixed-operators */\r\n          return isFn(oldCallback) && oldCallback(res) || res\r\n        });\r\n      };\r\n    }\r\n  });\r\n  return options\r\n}\r\n\r\nfunction wrapperReturnValue (method, returnValue) {\r\n  const returnValueHooks = [];\r\n  if (Array.isArray(globalInterceptors.returnValue)) {\r\n    returnValueHooks.push(...globalInterceptors.returnValue);\r\n  }\r\n  const interceptor = scopedInterceptors[method];\r\n  if (interceptor && Array.isArray(interceptor.returnValue)) {\r\n    returnValueHooks.push(...interceptor.returnValue);\r\n  }\r\n  returnValueHooks.forEach(hook => {\r\n    returnValue = hook(returnValue) || returnValue;\r\n  });\r\n  return returnValue\r\n}\r\n\r\nfunction getApiInterceptorHooks (method) {\r\n  const interceptor = Object.create(null);\r\n  Object.keys(globalInterceptors).forEach(hook => {\r\n    if (hook !== 'returnValue') {\r\n      interceptor[hook] = globalInterceptors[hook].slice();\r\n    }\r\n  });\r\n  const scopedInterceptor = scopedInterceptors[method];\r\n  if (scopedInterceptor) {\r\n    Object.keys(scopedInterceptor).forEach(hook => {\r\n      if (hook !== 'returnValue') {\r\n        interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\r\n      }\r\n    });\r\n  }\r\n  return interceptor\r\n}\r\n\r\nfunction invokeApi (method, api, options, ...params) {\r\n  const interceptor = getApiInterceptorHooks(method);\r\n  if (interceptor && Object.keys(interceptor).length) {\r\n    if (Array.isArray(interceptor.invoke)) {\r\n      const res = queue(interceptor.invoke, options);\r\n      return res.then((options) => {\r\n        return api(wrapperOptions(interceptor, options), ...params)\r\n      })\r\n    } else {\r\n      return api(wrapperOptions(interceptor, options), ...params)\r\n    }\r\n  }\r\n  return api(options, ...params)\r\n}\r\n\r\nconst promiseInterceptor = {\r\n  returnValue (res) {\r\n    if (!isPromise(res)) {\r\n      return res\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then(res => {\r\n        if (res[0]) {\r\n          reject(res[0]);\r\n        } else {\r\n          resolve(res[1]);\r\n        }\r\n      });\r\n    })\r\n  }\r\n};\r\n\r\nconst SYNC_API_RE =\r\n  /^\\$|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting/;\r\n\r\nconst CONTEXT_API_RE = /^create|Manager$/;\r\n\r\n// Context例外情况\r\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\r\n\r\n// 同步例外情况\r\nconst ASYNC_API = ['createBLEConnection', 'createPushMessage'];\r\n\r\nconst CALLBACK_API_RE = /^on|^off/;\r\n\r\nfunction isContextApi (name) {\r\n  return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1\r\n}\r\nfunction isSyncApi (name) {\r\n  return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1\r\n}\r\n\r\nfunction isCallbackApi (name) {\r\n  return CALLBACK_API_RE.test(name) && name !== 'onPush'\r\n}\r\n\r\nfunction handlePromise (promise) {\r\n  return promise.then(data => {\r\n    return [null, data]\r\n  })\r\n    .catch(err => [err])\r\n}\r\n\r\nfunction shouldPromise (name) {\r\n  if (\r\n    isContextApi(name) ||\r\n    isSyncApi(name) ||\r\n    isCallbackApi(name)\r\n  ) {\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/* eslint-disable no-extend-native */\r\nif (!Promise.prototype.finally) {\r\n  Promise.prototype.finally = function (callback) {\r\n    const promise = this.constructor;\r\n    return this.then(\r\n      value => promise.resolve(callback()).then(() => value),\r\n      reason => promise.resolve(callback()).then(() => {\r\n        throw reason\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nfunction promisify (name, api) {\r\n  if (!shouldPromise(name)) {\r\n    return api\r\n  }\r\n  return function promiseApi (options = {}, ...params) {\r\n    if (isFn(options.success) || isFn(options.fail) || isFn(options.complete)) {\r\n      return wrapperReturnValue(name, invokeApi(name, api, options, ...params))\r\n    }\r\n    return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\r\n      invokeApi(name, api, Object.assign({}, options, {\r\n        success: resolve,\r\n        fail: reject\r\n      }), ...params);\r\n    })))\r\n  }\r\n}\r\n\r\nconst EPS = 1e-4;\r\nconst BASE_DEVICE_WIDTH = 750;\r\nlet isIOS = false;\r\nlet deviceWidth = 0;\r\nlet deviceDPR = 0;\r\n\r\nfunction checkDeviceWidth () {\r\n  const {\r\n    platform,\r\n    pixelRatio,\r\n    windowWidth\r\n  } = wx.getSystemInfoSync(); // uni=>wx runtime 编译目标是 uni 对象，内部不允许直接使用 uni\r\n\r\n  deviceWidth = windowWidth;\r\n  deviceDPR = pixelRatio;\r\n  isIOS = platform === 'ios';\r\n}\r\n\r\nfunction upx2px (number, newDeviceWidth) {\r\n  if (deviceWidth === 0) {\r\n    checkDeviceWidth();\r\n  }\r\n\r\n  number = Number(number);\r\n  if (number === 0) {\r\n    return 0\r\n  }\r\n  let result = (number / BASE_DEVICE_WIDTH) * (newDeviceWidth || deviceWidth);\r\n  if (result < 0) {\r\n    result = -result;\r\n  }\r\n  result = Math.floor(result + EPS);\r\n  if (result === 0) {\r\n    if (deviceDPR === 1 || !isIOS) {\r\n      result = 1;\r\n    } else {\r\n      result = 0.5;\r\n    }\r\n  }\r\n  return number < 0 ? -result : result\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\n\r\nconst messages = {};\r\n\r\nlet locale;\r\n\r\n{\r\n  locale = normalizeLocale(wx.getSystemInfoSync().language) || LOCALE_EN;\r\n}\r\n\r\nfunction initI18nMessages () {\r\n  if (!isEnableLocale()) {\r\n    return\r\n  }\r\n  const localeKeys = Object.keys(__uniConfig.locales);\r\n  if (localeKeys.length) {\r\n    localeKeys.forEach((locale) => {\r\n      const curMessages = messages[locale];\r\n      const userMessages = __uniConfig.locales[locale];\r\n      if (curMessages) {\r\n        Object.assign(curMessages, userMessages);\r\n      } else {\r\n        messages[locale] = userMessages;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\ninitI18nMessages();\r\n\r\nconst i18n = initVueI18n(\r\n  locale,\r\n   {}\r\n);\r\nconst t = i18n.t;\r\nconst i18nMixin = (i18n.mixin = {\r\n  beforeCreate () {\r\n    const unwatch = i18n.i18n.watchLocale(() => {\r\n      this.$forceUpdate();\r\n    });\r\n    this.$once('hook:beforeDestroy', function () {\r\n      unwatch();\r\n    });\r\n  },\r\n  methods: {\r\n    $$t (key, values) {\r\n      return t(key, values)\r\n    }\r\n  }\r\n});\r\nconst setLocale = i18n.setLocale;\r\nconst getLocale = i18n.getLocale;\r\n\r\nfunction initAppLocale (Vue, appVm, locale) {\r\n  const state = Vue.observable({\r\n    locale: locale || i18n.getLocale()\r\n  });\r\n  const localeWatchers = [];\r\n  appVm.$watchLocale = fn => {\r\n    localeWatchers.push(fn);\r\n  };\r\n  Object.defineProperty(appVm, '$locale', {\r\n    get () {\r\n      return state.locale\r\n    },\r\n    set (v) {\r\n      state.locale = v;\r\n      localeWatchers.forEach(watch => watch(v));\r\n    }\r\n  });\r\n}\r\n\r\nfunction isEnableLocale () {\r\n  return typeof __uniConfig !== 'undefined' && __uniConfig.locales && !!Object.keys(__uniConfig.locales).length\r\n}\r\n\r\nfunction include (str, parts) {\r\n  return !!parts.find((part) => str.indexOf(part) !== -1)\r\n}\r\n\r\nfunction startsWith (str, parts) {\r\n  return parts.find((part) => str.indexOf(part) === 0)\r\n}\r\n\r\nfunction normalizeLocale (locale, messages) {\r\n  if (!locale) {\r\n    return\r\n  }\r\n  locale = locale.trim().replace(/_/g, '-');\r\n  if (messages && messages[locale]) {\r\n    return locale\r\n  }\r\n  locale = locale.toLowerCase();\r\n  if (locale === 'chinese') {\r\n    // 支付宝\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  if (locale.indexOf('zh') === 0) {\r\n    if (locale.indexOf('-hans') > -1) {\r\n      return LOCALE_ZH_HANS\r\n    }\r\n    if (locale.indexOf('-hant') > -1) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n  if (lang) {\r\n    return lang\r\n  }\r\n}\r\n// export function initI18n() {\r\n//   const localeKeys = Object.keys(__uniConfig.locales || {})\r\n//   if (localeKeys.length) {\r\n//     localeKeys.forEach((locale) =>\r\n//       i18n.add(locale, __uniConfig.locales[locale])\r\n//     )\r\n//   }\r\n// }\r\n\r\nfunction getLocale$1 () {\r\n  // 优先使用 $locale\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  if (app && app.$vm) {\r\n    return app.$vm.$locale\r\n  }\r\n  return normalizeLocale(wx.getSystemInfoSync().language) || LOCALE_EN\r\n}\r\n\r\nfunction setLocale$1 (locale) {\r\n  const app = getApp();\r\n  if (!app) {\r\n    return false\r\n  }\r\n  const oldLocale = app.$vm.$locale;\r\n  if (oldLocale !== locale) {\r\n    app.$vm.$locale = locale;\r\n    onLocaleChangeCallbacks.forEach((fn) => fn({\r\n      locale\r\n    }));\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nconst onLocaleChangeCallbacks = [];\r\nfunction onLocaleChange (fn) {\r\n  if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\r\n    onLocaleChangeCallbacks.push(fn);\r\n  }\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.getLocale = getLocale$1;\r\n}\r\n\r\nconst interceptors = {\r\n  promiseInterceptor\r\n};\r\n\r\nvar baseApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  upx2px: upx2px,\r\n  getLocale: getLocale$1,\r\n  setLocale: setLocale$1,\r\n  onLocaleChange: onLocaleChange,\r\n  addInterceptor: addInterceptor,\r\n  removeInterceptor: removeInterceptor,\r\n  interceptors: interceptors\r\n});\r\n\r\nfunction findExistsPageIndex (url) {\r\n  const pages = getCurrentPages();\r\n  let len = pages.length;\r\n  while (len--) {\r\n    const page = pages[len];\r\n    if (page.$page && page.$page.fullPath === url) {\r\n      return len\r\n    }\r\n  }\r\n  return -1\r\n}\r\n\r\nvar redirectTo = {\r\n  name (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.delta) {\r\n      return 'navigateBack'\r\n    }\r\n    return 'redirectTo'\r\n  },\r\n  args (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.url) {\r\n      const existsPageIndex = findExistsPageIndex(fromArgs.url);\r\n      if (existsPageIndex !== -1) {\r\n        const delta = getCurrentPages().length - 1 - existsPageIndex;\r\n        if (delta > 0) {\r\n          fromArgs.delta = delta;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nvar previewImage = {\r\n  args (fromArgs) {\r\n    let currentIndex = parseInt(fromArgs.current);\r\n    if (isNaN(currentIndex)) {\r\n      return\r\n    }\r\n    const urls = fromArgs.urls;\r\n    if (!Array.isArray(urls)) {\r\n      return\r\n    }\r\n    const len = urls.length;\r\n    if (!len) {\r\n      return\r\n    }\r\n    if (currentIndex < 0) {\r\n      currentIndex = 0;\r\n    } else if (currentIndex >= len) {\r\n      currentIndex = len - 1;\r\n    }\r\n    if (currentIndex > 0) {\r\n      fromArgs.current = urls[currentIndex];\r\n      fromArgs.urls = urls.filter(\r\n        (item, index) => index < currentIndex ? item !== urls[currentIndex] : true\r\n      );\r\n    } else {\r\n      fromArgs.current = urls[0];\r\n    }\r\n    return {\r\n      indicator: false,\r\n      loop: false\r\n    }\r\n  }\r\n};\r\n\r\nconst UUID_KEY = '__DC_STAT_UUID';\r\nlet deviceId;\r\nfunction useDeviceId (result) {\r\n  deviceId = deviceId || wx.getStorageSync(UUID_KEY);\r\n  if (!deviceId) {\r\n    deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\r\n    wx.setStorage({\r\n      key: UUID_KEY,\r\n      data: deviceId\r\n    });\r\n  }\r\n  result.deviceId = deviceId;\r\n}\r\n\r\nfunction addSafeAreaInsets (result) {\r\n  if (result.safeArea) {\r\n    const safeArea = result.safeArea;\r\n    result.safeAreaInsets = {\r\n      top: safeArea.top,\r\n      left: safeArea.left,\r\n      right: result.windowWidth - safeArea.right,\r\n      bottom: result.screenHeight - safeArea.bottom\r\n    };\r\n  }\r\n}\r\n\r\nfunction populateParameters (result) {\r\n  const {\r\n    brand = '', model = '', system = '',\r\n    language = '', theme, version,\r\n    platform, fontSizeSetting,\r\n    SDKVersion, pixelRatio, deviceOrientation\r\n  } = result;\r\n  // const isQuickApp = \"mp-weixin\".indexOf('quickapp-webview') !== -1\r\n\r\n  // osName osVersion\r\n  let osName = '';\r\n  let osVersion = '';\r\n  {\r\n    osName = system.split(' ')[0] || '';\r\n    osVersion = system.split(' ')[1] || '';\r\n  }\r\n  let hostVersion = version;\r\n\r\n  // deviceType\r\n  const deviceType = getGetDeviceType(result, model);\r\n\r\n  // deviceModel\r\n  const deviceBrand = getDeviceBrand(brand);\r\n\r\n  // hostName\r\n  const _hostName = getHostName(result);\r\n\r\n  // deviceOrientation\r\n  let _deviceOrientation = deviceOrientation; // 仅 微信 百度 支持\r\n\r\n  // devicePixelRatio\r\n  let _devicePixelRatio = pixelRatio;\r\n\r\n  // SDKVersion\r\n  let _SDKVersion = SDKVersion;\r\n\r\n  // hostLanguage\r\n  const hostLanguage = language.replace(/_/g, '-');\r\n\r\n  // wx.getAccountInfoSync\r\n\r\n  const parameters = {\r\n    appId: process.env.UNI_APP_ID,\r\n    appName: process.env.UNI_APP_NAME,\r\n    appVersion: process.env.UNI_APP_VERSION_NAME,\r\n    appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n    appLanguage: getAppLanguage(hostLanguage),\r\n    uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n    deviceBrand,\r\n    deviceModel: model,\r\n    deviceType,\r\n    devicePixelRatio: _devicePixelRatio,\r\n    deviceOrientation: _deviceOrientation,\r\n    osName: osName.toLocaleLowerCase(),\r\n    osVersion,\r\n    hostTheme: theme,\r\n    hostVersion,\r\n    hostLanguage,\r\n    hostName: _hostName,\r\n    hostSDKVersion: _SDKVersion,\r\n    hostFontSizeSetting: fontSizeSetting,\r\n    windowTop: 0,\r\n    windowBottom: 0,\r\n    // TODO\r\n    osLanguage: undefined,\r\n    osTheme: undefined,\r\n    ua: undefined,\r\n    hostPackageName: undefined,\r\n    browserName: undefined,\r\n    browserVersion: undefined\r\n  };\r\n\r\n  Object.assign(result, parameters);\r\n}\r\n\r\nfunction getGetDeviceType (result, model) {\r\n  let deviceType = result.deviceType || 'phone';\r\n  {\r\n    const deviceTypeMaps = {\r\n      ipad: 'pad',\r\n      windows: 'pc',\r\n      mac: 'pc'\r\n    };\r\n    const deviceTypeMapsKeys = Object.keys(deviceTypeMaps);\r\n    const _model = model.toLocaleLowerCase();\r\n    for (let index = 0; index < deviceTypeMapsKeys.length; index++) {\r\n      const _m = deviceTypeMapsKeys[index];\r\n      if (_model.indexOf(_m) !== -1) {\r\n        deviceType = deviceTypeMaps[_m];\r\n        break\r\n      }\r\n    }\r\n  }\r\n  return deviceType\r\n}\r\n\r\nfunction getDeviceBrand (brand) {\r\n  let deviceBrand = brand;\r\n  if (deviceBrand) {\r\n    deviceBrand = brand.toLocaleLowerCase();\r\n  }\r\n  return deviceBrand\r\n}\r\n\r\nfunction getAppLanguage (defaultLanguage) {\r\n  return getLocale$1\r\n    ? getLocale$1()\r\n    : defaultLanguage\r\n}\r\n\r\nfunction getHostName (result) {\r\n  const _platform =  'WeChat' ;\r\n  let _hostName = result.hostName || _platform; // mp-jd\r\n  {\r\n    if (result.environment) {\r\n      _hostName = result.environment;\r\n    } else if (result.host && result.host.env) {\r\n      _hostName = result.host.env;\r\n    }\r\n  }\r\n\r\n  return _hostName\r\n}\r\n\r\nvar getSystemInfo = {\r\n  returnValue: function (result) {\r\n    useDeviceId(result);\r\n    addSafeAreaInsets(result);\r\n    populateParameters(result);\r\n  }\r\n};\r\n\r\nvar showActionSheet = {\r\n  args (fromArgs) {\r\n    if (typeof fromArgs === 'object') {\r\n      fromArgs.alertText = fromArgs.title;\r\n    }\r\n  }\r\n};\r\n\r\nvar getAppBaseInfo = {\r\n  returnValue: function (result) {\r\n    const { version, language, SDKVersion, theme } = result;\r\n\r\n    const _hostName = getHostName(result);\r\n\r\n    const hostLanguage = language.replace('_', '-');\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      appId: process.env.UNI_APP_ID,\r\n      appName: process.env.UNI_APP_NAME,\r\n      appVersion: process.env.UNI_APP_VERSION_NAME,\r\n      appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n      appLanguage: getAppLanguage(hostLanguage),\r\n      hostVersion: version,\r\n      hostLanguage,\r\n      hostName: _hostName,\r\n      hostSDKVersion: SDKVersion,\r\n      hostTheme: theme\r\n    }));\r\n  }\r\n};\r\n\r\nvar getDeviceInfo = {\r\n  returnValue: function (result) {\r\n    const { brand, model } = result;\r\n    const deviceType = getGetDeviceType(result, model);\r\n    const deviceBrand = getDeviceBrand(brand);\r\n    useDeviceId(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      deviceType,\r\n      deviceBrand,\r\n      deviceModel: model\r\n    }));\r\n  }\r\n};\r\n\r\nvar getWindowInfo = {\r\n  returnValue: function (result) {\r\n    addSafeAreaInsets(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      windowTop: 0,\r\n      windowBottom: 0\r\n    }));\r\n  }\r\n};\r\n\r\nvar getAppAuthorizeSetting = {\r\n  returnValue: function (result) {\r\n    const { locationReducedAccuracy } = result;\r\n\r\n    result.locationAccuracy = 'unsupported';\r\n    if (locationReducedAccuracy === true) {\r\n      result.locationAccuracy = 'reduced';\r\n    } else if (locationReducedAccuracy === false) {\r\n      result.locationAccuracy = 'full';\r\n    }\r\n  }\r\n};\r\n\r\n// import navigateTo from 'uni-helpers/navigate-to'\r\n\r\nconst protocols = {\r\n  redirectTo,\r\n  // navigateTo,  // 由于在微信开发者工具的页面参数，会显示__id__参数，因此暂时关闭mp-weixin对于navigateTo的AOP\r\n  previewImage,\r\n  getSystemInfo,\r\n  getSystemInfoSync: getSystemInfo,\r\n  showActionSheet,\r\n  getAppBaseInfo,\r\n  getDeviceInfo,\r\n  getWindowInfo,\r\n  getAppAuthorizeSetting\r\n};\r\nconst todos = [\r\n  'vibrate',\r\n  'preloadPage',\r\n  'unPreloadPage',\r\n  'loadSubPackage'\r\n];\r\nconst canIUses = [];\r\n\r\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\r\n\r\nfunction processCallback (methodName, method, returnValue) {\r\n  return function (res) {\r\n    return method(processReturnValue(methodName, res, returnValue))\r\n  }\r\n}\r\n\r\nfunction processArgs (methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\r\n  if (isPlainObject(fromArgs)) { // 一般 api 的参数解析\r\n    const toArgs = keepFromArgs === true ? fromArgs : {}; // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\r\n    if (isFn(argsOption)) {\r\n      argsOption = argsOption(fromArgs, toArgs) || {};\r\n    }\r\n    for (const key in fromArgs) {\r\n      if (hasOwn(argsOption, key)) {\r\n        let keyOption = argsOption[key];\r\n        if (isFn(keyOption)) {\r\n          keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\r\n        }\r\n        if (!keyOption) { // 不支持的参数\r\n          console.warn(`The '${methodName}' method of platform '微信小程序' does not support option '${key}'`);\r\n        } else if (isStr(keyOption)) { // 重写参数 key\r\n          toArgs[keyOption] = fromArgs[key];\r\n        } else if (isPlainObject(keyOption)) { // {name:newName,value:value}可重新指定参数 key:value\r\n          toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\r\n        }\r\n      } else if (CALLBACKS.indexOf(key) !== -1) {\r\n        if (isFn(fromArgs[key])) {\r\n          toArgs[key] = processCallback(methodName, fromArgs[key], returnValue);\r\n        }\r\n      } else {\r\n        if (!keepFromArgs) {\r\n          toArgs[key] = fromArgs[key];\r\n        }\r\n      }\r\n    }\r\n    return toArgs\r\n  } else if (isFn(fromArgs)) {\r\n    fromArgs = processCallback(methodName, fromArgs, returnValue);\r\n  }\r\n  return fromArgs\r\n}\r\n\r\nfunction processReturnValue (methodName, res, returnValue, keepReturnValue = false) {\r\n  if (isFn(protocols.returnValue)) { // 处理通用 returnValue\r\n    res = protocols.returnValue(methodName, res);\r\n  }\r\n  return processArgs(methodName, res, returnValue, {}, keepReturnValue)\r\n}\r\n\r\nfunction wrapper (methodName, method) {\r\n  if (hasOwn(protocols, methodName)) {\r\n    const protocol = protocols[methodName];\r\n    if (!protocol) { // 暂不支持的 api\r\n      return function () {\r\n        console.error(`Platform '微信小程序' does not support '${methodName}'.`);\r\n      }\r\n    }\r\n    return function (arg1, arg2) { // 目前 api 最多两个参数\r\n      let options = protocol;\r\n      if (isFn(protocol)) {\r\n        options = protocol(arg1);\r\n      }\r\n\r\n      arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\r\n\r\n      const args = [arg1];\r\n      if (typeof arg2 !== 'undefined') {\r\n        args.push(arg2);\r\n      }\r\n      if (isFn(options.name)) {\r\n        methodName = options.name(arg1);\r\n      } else if (isStr(options.name)) {\r\n        methodName = options.name;\r\n      }\r\n      const returnValue = wx[methodName].apply(wx, args);\r\n      if (isSyncApi(methodName)) { // 同步 api\r\n        return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName))\r\n      }\r\n      return returnValue\r\n    }\r\n  }\r\n  return method\r\n}\r\n\r\nconst todoApis = Object.create(null);\r\n\r\nconst TODOS = [\r\n  'onTabBarMidButtonTap',\r\n  'subscribePush',\r\n  'unsubscribePush',\r\n  'onPush',\r\n  'offPush',\r\n  'share'\r\n];\r\n\r\nfunction createTodoApi (name) {\r\n  return function todoApi ({\r\n    fail,\r\n    complete\r\n  }) {\r\n    const res = {\r\n      errMsg: `${name}:fail method '${name}' not supported`\r\n    };\r\n    isFn(fail) && fail(res);\r\n    isFn(complete) && complete(res);\r\n  }\r\n}\r\n\r\nTODOS.forEach(function (name) {\r\n  todoApis[name] = createTodoApi(name);\r\n});\r\n\r\nvar providers = {\r\n  oauth: ['weixin'],\r\n  share: ['weixin'],\r\n  payment: ['wxpay'],\r\n  push: ['weixin']\r\n};\r\n\r\nfunction getProvider ({\r\n  service,\r\n  success,\r\n  fail,\r\n  complete\r\n}) {\r\n  let res = false;\r\n  if (providers[service]) {\r\n    res = {\r\n      errMsg: 'getProvider:ok',\r\n      service,\r\n      provider: providers[service]\r\n    };\r\n    isFn(success) && success(res);\r\n  } else {\r\n    res = {\r\n      errMsg: 'getProvider:fail service not found'\r\n    };\r\n    isFn(fail) && fail(res);\r\n  }\r\n  isFn(complete) && complete(res);\r\n}\r\n\r\nvar extraApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getProvider: getProvider\r\n});\r\n\r\nconst getEmitter = (function () {\r\n  let Emitter;\r\n  return function getUniEmitter () {\r\n    if (!Emitter) {\r\n      Emitter = new Vue();\r\n    }\r\n    return Emitter\r\n  }\r\n})();\r\n\r\nfunction apply (ctx, method, args) {\r\n  return ctx[method].apply(ctx, args)\r\n}\r\n\r\nfunction $on () {\r\n  return apply(getEmitter(), '$on', [...arguments])\r\n}\r\nfunction $off () {\r\n  return apply(getEmitter(), '$off', [...arguments])\r\n}\r\nfunction $once () {\r\n  return apply(getEmitter(), '$once', [...arguments])\r\n}\r\nfunction $emit () {\r\n  return apply(getEmitter(), '$emit', [...arguments])\r\n}\r\n\r\nvar eventApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  $on: $on,\r\n  $off: $off,\r\n  $once: $once,\r\n  $emit: $emit\r\n});\r\n\r\n/**\r\n * 框架内 try-catch\r\n */\r\n/**\r\n * 开发者 try-catch\r\n */\r\nfunction tryCatch (fn) {\r\n  return function () {\r\n    try {\r\n      return fn.apply(fn, arguments)\r\n    } catch (e) {\r\n      // TODO\r\n      console.error(e);\r\n    }\r\n  }\r\n}\r\n\r\nfunction getApiCallbacks (params) {\r\n  const apiCallbacks = {};\r\n  for (const name in params) {\r\n    const param = params[name];\r\n    if (isFn(param)) {\r\n      apiCallbacks[name] = tryCatch(param);\r\n      delete params[name];\r\n    }\r\n  }\r\n  return apiCallbacks\r\n}\r\n\r\nlet cid;\r\nlet cidErrMsg;\r\nlet enabled;\r\n\r\nfunction normalizePushMessage (message) {\r\n  try {\r\n    return JSON.parse(message)\r\n  } catch (e) {}\r\n  return message\r\n}\r\n\r\nfunction invokePushCallback (\r\n  args\r\n) {\r\n  if (args.type === 'enabled') {\r\n    enabled = true;\r\n  } else if (args.type === 'clientId') {\r\n    cid = args.cid;\r\n    cidErrMsg = args.errMsg;\r\n    invokeGetPushCidCallbacks(cid, args.errMsg);\r\n  } else if (args.type === 'pushMsg') {\r\n    const message = {\r\n      type: 'receive',\r\n      data: normalizePushMessage(args.message)\r\n    };\r\n    for (let i = 0; i < onPushMessageCallbacks.length; i++) {\r\n      const callback = onPushMessageCallbacks[i];\r\n      callback(message);\r\n      // 该消息已被阻止\r\n      if (message.stopped) {\r\n        break\r\n      }\r\n    }\r\n  } else if (args.type === 'click') {\r\n    onPushMessageCallbacks.forEach((callback) => {\r\n      callback({\r\n        type: 'click',\r\n        data: normalizePushMessage(args.message)\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nconst getPushCidCallbacks = [];\r\n\r\nfunction invokeGetPushCidCallbacks (cid, errMsg) {\r\n  getPushCidCallbacks.forEach((callback) => {\r\n    callback(cid, errMsg);\r\n  });\r\n  getPushCidCallbacks.length = 0;\r\n}\r\n\r\nfunction getPushClientId (args) {\r\n  if (!isPlainObject(args)) {\r\n    args = {};\r\n  }\r\n  const {\r\n    success,\r\n    fail,\r\n    complete\r\n  } = getApiCallbacks(args);\r\n  const hasSuccess = isFn(success);\r\n  const hasFail = isFn(fail);\r\n  const hasComplete = isFn(complete);\r\n  Promise.resolve().then(() => {\r\n    if (typeof enabled === 'undefined') {\r\n      enabled = false;\r\n      cid = '';\r\n      cidErrMsg = 'unipush is not enabled';\r\n    }\r\n    getPushCidCallbacks.push((cid, errMsg) => {\r\n      let res;\r\n      if (cid) {\r\n        res = {\r\n          errMsg: 'getPushClientId:ok',\r\n          cid\r\n        };\r\n        hasSuccess && success(res);\r\n      } else {\r\n        res = {\r\n          errMsg: 'getPushClientId:fail' + (errMsg ? ' ' + errMsg : '')\r\n        };\r\n        hasFail && fail(res);\r\n      }\r\n      hasComplete && complete(res);\r\n    });\r\n    if (typeof cid !== 'undefined') {\r\n      invokeGetPushCidCallbacks(cid, cidErrMsg);\r\n    }\r\n  });\r\n}\r\n\r\nconst onPushMessageCallbacks = [];\r\n// 不使用 defineOnApi 实现，是因为 defineOnApi 依赖 UniServiceJSBridge ，该对象目前在小程序上未提供，故简单实现\r\nconst onPushMessage = (fn) => {\r\n  if (onPushMessageCallbacks.indexOf(fn) === -1) {\r\n    onPushMessageCallbacks.push(fn);\r\n  }\r\n};\r\n\r\nconst offPushMessage = (fn) => {\r\n  if (!fn) {\r\n    onPushMessageCallbacks.length = 0;\r\n  } else {\r\n    const index = onPushMessageCallbacks.indexOf(fn);\r\n    if (index > -1) {\r\n      onPushMessageCallbacks.splice(index, 1);\r\n    }\r\n  }\r\n};\r\n\r\nvar api = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getPushClientId: getPushClientId,\r\n  onPushMessage: onPushMessage,\r\n  offPushMessage: offPushMessage,\r\n  invokePushCallback: invokePushCallback\r\n});\r\n\r\nconst MPPage = Page;\r\nconst MPComponent = Component;\r\n\r\nconst customizeRE = /:/g;\r\n\r\nconst customize = cached((str) => {\r\n  return camelize(str.replace(customizeRE, '-'))\r\n});\r\n\r\nfunction initTriggerEvent (mpInstance) {\r\n  const oldTriggerEvent = mpInstance.triggerEvent;\r\n  const newTriggerEvent = function (event, ...args) {\r\n    return oldTriggerEvent.apply(mpInstance, [customize(event), ...args])\r\n  };\r\n  try {\r\n    // 京东小程序 triggerEvent 为只读\r\n    mpInstance.triggerEvent = newTriggerEvent;\r\n  } catch (error) {\r\n    mpInstance._triggerEvent = newTriggerEvent;\r\n  }\r\n}\r\n\r\nfunction initHook (name, options, isComponent) {\r\n  const oldHook = options[name];\r\n  if (!oldHook) {\r\n    options[name] = function () {\r\n      initTriggerEvent(this);\r\n    };\r\n  } else {\r\n    options[name] = function (...args) {\r\n      initTriggerEvent(this);\r\n      return oldHook.apply(this, args)\r\n    };\r\n  }\r\n}\r\nif (!MPPage.__$wrappered) {\r\n  MPPage.__$wrappered = true;\r\n  Page = function (options = {}) {\r\n    initHook('onLoad', options);\r\n    return MPPage(options)\r\n  };\r\n  Page.after = MPPage.after;\r\n\r\n  Component = function (options = {}) {\r\n    initHook('created', options);\r\n    return MPComponent(options)\r\n  };\r\n}\r\n\r\nconst PAGE_EVENT_HOOKS = [\r\n  'onPullDownRefresh',\r\n  'onReachBottom',\r\n  'onAddToFavorites',\r\n  'onShareTimeline',\r\n  'onShareAppMessage',\r\n  'onPageScroll',\r\n  'onResize',\r\n  'onTabItemTap'\r\n];\r\n\r\nfunction initMocks (vm, mocks) {\r\n  const mpInstance = vm.$mp[vm.mpType];\r\n  mocks.forEach(mock => {\r\n    if (hasOwn(mpInstance, mock)) {\r\n      vm[mock] = mpInstance[mock];\r\n    }\r\n  });\r\n}\r\n\r\nfunction hasHook (hook, vueOptions) {\r\n  if (!vueOptions) {\r\n    return true\r\n  }\r\n\r\n  if (Vue.options && Array.isArray(Vue.options[hook])) {\r\n    return true\r\n  }\r\n\r\n  vueOptions = vueOptions.default || vueOptions;\r\n\r\n  if (isFn(vueOptions)) {\r\n    if (isFn(vueOptions.extendOptions[hook])) {\r\n      return true\r\n    }\r\n    if (vueOptions.super &&\r\n      vueOptions.super.options &&\r\n      Array.isArray(vueOptions.super.options[hook])) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  if (isFn(vueOptions[hook])) {\r\n    return true\r\n  }\r\n  const mixins = vueOptions.mixins;\r\n  if (Array.isArray(mixins)) {\r\n    return !!mixins.find(mixin => hasHook(hook, mixin))\r\n  }\r\n}\r\n\r\nfunction initHooks (mpOptions, hooks, vueOptions) {\r\n  hooks.forEach(hook => {\r\n    if (hasHook(hook, vueOptions)) {\r\n      mpOptions[hook] = function (args) {\r\n        return this.$vm && this.$vm.__call_hook(hook, args)\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\nfunction initVueComponent (Vue, vueOptions) {\r\n  vueOptions = vueOptions.default || vueOptions;\r\n  let VueComponent;\r\n  if (isFn(vueOptions)) {\r\n    VueComponent = vueOptions;\r\n  } else {\r\n    VueComponent = Vue.extend(vueOptions);\r\n  }\r\n  vueOptions = VueComponent.options;\r\n  return [VueComponent, vueOptions]\r\n}\r\n\r\nfunction initSlots (vm, vueSlots) {\r\n  if (Array.isArray(vueSlots) && vueSlots.length) {\r\n    const $slots = Object.create(null);\r\n    vueSlots.forEach(slotName => {\r\n      $slots[slotName] = true;\r\n    });\r\n    vm.$scopedSlots = vm.$slots = $slots;\r\n  }\r\n}\r\n\r\nfunction initVueIds (vueIds, mpInstance) {\r\n  vueIds = (vueIds || '').split(',');\r\n  const len = vueIds.length;\r\n\r\n  if (len === 1) {\r\n    mpInstance._$vueId = vueIds[0];\r\n  } else if (len === 2) {\r\n    mpInstance._$vueId = vueIds[0];\r\n    mpInstance._$vuePid = vueIds[1];\r\n  }\r\n}\r\n\r\nfunction initData (vueOptions, context) {\r\n  let data = vueOptions.data || {};\r\n  const methods = vueOptions.methods || {};\r\n\r\n  if (typeof data === 'function') {\r\n    try {\r\n      data = data.call(context); // 支持 Vue.prototype 上挂的数据\r\n    } catch (e) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.warn('根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。', data);\r\n      }\r\n    }\r\n  } else {\r\n    try {\r\n      // 对 data 格式化\r\n      data = JSON.parse(JSON.stringify(data));\r\n    } catch (e) {}\r\n  }\r\n\r\n  if (!isPlainObject(data)) {\r\n    data = {};\r\n  }\r\n\r\n  Object.keys(methods).forEach(methodName => {\r\n    if (context.__lifecycle_hooks__.indexOf(methodName) === -1 && !hasOwn(data, methodName)) {\r\n      data[methodName] = methods[methodName];\r\n    }\r\n  });\r\n\r\n  return data\r\n}\r\n\r\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\r\n\r\nfunction createObserver (name) {\r\n  return function observer (newVal, oldVal) {\r\n    if (this.$vm) {\r\n      this.$vm[name] = newVal; // 为了触发其他非 render watcher\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehaviors (vueOptions, initBehavior) {\r\n  const vueBehaviors = vueOptions.behaviors;\r\n  const vueExtends = vueOptions.extends;\r\n  const vueMixins = vueOptions.mixins;\r\n\r\n  let vueProps = vueOptions.props;\r\n\r\n  if (!vueProps) {\r\n    vueOptions.props = vueProps = [];\r\n  }\r\n\r\n  const behaviors = [];\r\n  if (Array.isArray(vueBehaviors)) {\r\n    vueBehaviors.forEach(behavior => {\r\n      behaviors.push(behavior.replace('uni://', `${\"wx\"}://`));\r\n      if (behavior === 'uni://form-field') {\r\n        if (Array.isArray(vueProps)) {\r\n          vueProps.push('name');\r\n          vueProps.push('value');\r\n        } else {\r\n          vueProps.name = {\r\n            type: String,\r\n            default: ''\r\n          };\r\n          vueProps.value = {\r\n            type: [String, Number, Boolean, Array, Object, Date],\r\n            default: ''\r\n          };\r\n        }\r\n      }\r\n    });\r\n  }\r\n  if (isPlainObject(vueExtends) && vueExtends.props) {\r\n    behaviors.push(\r\n      initBehavior({\r\n        properties: initProperties(vueExtends.props, true)\r\n      })\r\n    );\r\n  }\r\n  if (Array.isArray(vueMixins)) {\r\n    vueMixins.forEach(vueMixin => {\r\n      if (isPlainObject(vueMixin) && vueMixin.props) {\r\n        behaviors.push(\r\n          initBehavior({\r\n            properties: initProperties(vueMixin.props, true)\r\n          })\r\n        );\r\n      }\r\n    });\r\n  }\r\n  return behaviors\r\n}\r\n\r\nfunction parsePropType (key, type, defaultValue, file) {\r\n  // [String]=>String\r\n  if (Array.isArray(type) && type.length === 1) {\r\n    return type[0]\r\n  }\r\n  return type\r\n}\r\n\r\nfunction initProperties (props, isBehavior = false, file = '', options) {\r\n  const properties = {};\r\n  if (!isBehavior) {\r\n    properties.vueId = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    {\r\n      if ( options.virtualHost) {\r\n        properties.virtualHostStyle = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n        properties.virtualHostClass = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n      }\r\n    }\r\n    // scopedSlotsCompiler auto\r\n    properties.scopedSlotsCompiler = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    properties.vueSlots = { // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\r\n      type: null,\r\n      value: [],\r\n      observer: function (newVal, oldVal) {\r\n        const $slots = Object.create(null);\r\n        newVal.forEach(slotName => {\r\n          $slots[slotName] = true;\r\n        });\r\n        this.setData({\r\n          $slots\r\n        });\r\n      }\r\n    };\r\n  }\r\n  if (Array.isArray(props)) { // ['title']\r\n    props.forEach(key => {\r\n      properties[key] = {\r\n        type: null,\r\n        observer: createObserver(key)\r\n      };\r\n    });\r\n  } else if (isPlainObject(props)) { // {title:{type:String,default:''},content:String}\r\n    Object.keys(props).forEach(key => {\r\n      const opts = props[key];\r\n      if (isPlainObject(opts)) { // title:{type:String,default:''}\r\n        let value = opts.default;\r\n        if (isFn(value)) {\r\n          value = value();\r\n        }\r\n\r\n        opts.type = parsePropType(key, opts.type);\r\n\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(opts.type) !== -1 ? opts.type : null,\r\n          value,\r\n          observer: createObserver(key)\r\n        };\r\n      } else { // content:String\r\n        const type = parsePropType(key, opts);\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(type) !== -1 ? type : null,\r\n          observer: createObserver(key)\r\n        };\r\n      }\r\n    });\r\n  }\r\n  return properties\r\n}\r\n\r\nfunction wrapper$1 (event) {\r\n  // TODO 又得兼容 mpvue 的 mp 对象\r\n  try {\r\n    event.mp = JSON.parse(JSON.stringify(event));\r\n  } catch (e) {}\r\n\r\n  event.stopPropagation = noop;\r\n  event.preventDefault = noop;\r\n\r\n  event.target = event.target || {};\r\n\r\n  if (!hasOwn(event, 'detail')) {\r\n    event.detail = {};\r\n  }\r\n\r\n  if (hasOwn(event, 'markerId')) {\r\n    event.detail = typeof event.detail === 'object' ? event.detail : {};\r\n    event.detail.markerId = event.markerId;\r\n  }\r\n\r\n  if (isPlainObject(event.detail)) {\r\n    event.target = Object.assign({}, event.target, event.detail);\r\n  }\r\n\r\n  return event\r\n}\r\n\r\nfunction getExtraValue (vm, dataPathsArray) {\r\n  let context = vm;\r\n  dataPathsArray.forEach(dataPathArray => {\r\n    const dataPath = dataPathArray[0];\r\n    const value = dataPathArray[2];\r\n    if (dataPath || typeof value !== 'undefined') { // ['','',index,'disable']\r\n      const propPath = dataPathArray[1];\r\n      const valuePath = dataPathArray[3];\r\n\r\n      let vFor;\r\n      if (Number.isInteger(dataPath)) {\r\n        vFor = dataPath;\r\n      } else if (!dataPath) {\r\n        vFor = context;\r\n      } else if (typeof dataPath === 'string' && dataPath) {\r\n        if (dataPath.indexOf('#s#') === 0) {\r\n          vFor = dataPath.substr(3);\r\n        } else {\r\n          vFor = vm.__get_value(dataPath, context);\r\n        }\r\n      }\r\n\r\n      if (Number.isInteger(vFor)) {\r\n        context = value;\r\n      } else if (!propPath) {\r\n        context = vFor[value];\r\n      } else {\r\n        if (Array.isArray(vFor)) {\r\n          context = vFor.find(vForItem => {\r\n            return vm.__get_value(propPath, vForItem) === value\r\n          });\r\n        } else if (isPlainObject(vFor)) {\r\n          context = Object.keys(vFor).find(vForKey => {\r\n            return vm.__get_value(propPath, vFor[vForKey]) === value\r\n          });\r\n        } else {\r\n          console.error('v-for 暂不支持循环数据：', vFor);\r\n        }\r\n      }\r\n\r\n      if (valuePath) {\r\n        context = vm.__get_value(valuePath, context);\r\n      }\r\n    }\r\n  });\r\n  return context\r\n}\r\n\r\nfunction processEventExtra (vm, extra, event) {\r\n  const extraObj = {};\r\n\r\n  if (Array.isArray(extra) && extra.length) {\r\n    /**\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *'test'\r\n     */\r\n    extra.forEach((dataPath, index) => {\r\n      if (typeof dataPath === 'string') {\r\n        if (!dataPath) { // model,prop.sync\r\n          extraObj['$' + index] = vm;\r\n        } else {\r\n          if (dataPath === '$event') { // $event\r\n            extraObj['$' + index] = event;\r\n          } else if (dataPath === 'arguments') {\r\n            if (event.detail && event.detail.__args__) {\r\n              extraObj['$' + index] = event.detail.__args__;\r\n            } else {\r\n              extraObj['$' + index] = [event];\r\n            }\r\n          } else if (dataPath.indexOf('$event.') === 0) { // $event.target.value\r\n            extraObj['$' + index] = vm.__get_value(dataPath.replace('$event.', ''), event);\r\n          } else {\r\n            extraObj['$' + index] = vm.__get_value(dataPath);\r\n          }\r\n        }\r\n      } else {\r\n        extraObj['$' + index] = getExtraValue(vm, dataPath);\r\n      }\r\n    });\r\n  }\r\n\r\n  return extraObj\r\n}\r\n\r\nfunction getObjByArray (arr) {\r\n  const obj = {};\r\n  for (let i = 1; i < arr.length; i++) {\r\n    const element = arr[i];\r\n    obj[element[0]] = element[1];\r\n  }\r\n  return obj\r\n}\r\n\r\nfunction processEventArgs (vm, event, args = [], extra = [], isCustom, methodName) {\r\n  let isCustomMPEvent = false; // wxcomponent 组件，传递原始 event 对象\r\n  if (isCustom) { // 自定义事件\r\n    isCustomMPEvent = event.currentTarget &&\r\n      event.currentTarget.dataset &&\r\n      event.currentTarget.dataset.comType === 'wx';\r\n    if (!args.length) { // 无参数，直接传入 event 或 detail 数组\r\n      if (isCustomMPEvent) {\r\n        return [event]\r\n      }\r\n      return event.detail.__args__ || event.detail\r\n    }\r\n  }\r\n\r\n  const extraObj = processEventExtra(vm, extra, event);\r\n\r\n  const ret = [];\r\n  args.forEach(arg => {\r\n    if (arg === '$event') {\r\n      if (methodName === '__set_model' && !isCustom) { // input v-model value\r\n        ret.push(event.target.value);\r\n      } else {\r\n        if (isCustom && !isCustomMPEvent) {\r\n          ret.push(event.detail.__args__[0]);\r\n        } else { // wxcomponent 组件或内置组件\r\n          ret.push(event);\r\n        }\r\n      }\r\n    } else {\r\n      if (Array.isArray(arg) && arg[0] === 'o') {\r\n        ret.push(getObjByArray(arg));\r\n      } else if (typeof arg === 'string' && hasOwn(extraObj, arg)) {\r\n        ret.push(extraObj[arg]);\r\n      } else {\r\n        ret.push(arg);\r\n      }\r\n    }\r\n  });\r\n\r\n  return ret\r\n}\r\n\r\nconst ONCE = '~';\r\nconst CUSTOM = '^';\r\n\r\nfunction isMatchEventType (eventType, optType) {\r\n  return (eventType === optType) ||\r\n    (\r\n      optType === 'regionchange' &&\r\n      (\r\n        eventType === 'begin' ||\r\n        eventType === 'end'\r\n      )\r\n    )\r\n}\r\n\r\nfunction getContextVm (vm) {\r\n  let $parent = vm.$parent;\r\n  // 父组件是 scoped slots 或者其他自定义组件时继续查找\r\n  while ($parent && $parent.$parent && ($parent.$options.generic || $parent.$parent.$options.generic || $parent.$scope._$vuePid)) {\r\n    $parent = $parent.$parent;\r\n  }\r\n  return $parent && $parent.$parent\r\n}\r\n\r\nfunction handleEvent (event) {\r\n  event = wrapper$1(event);\r\n\r\n  // [['tap',[['handle',[1,2,a]],['handle1',[1,2,a]]]]]\r\n  const dataset = (event.currentTarget || event.target).dataset;\r\n  if (!dataset) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n  const eventOpts = dataset.eventOpts || dataset['event-opts']; // 支付宝 web-view 组件 dataset 非驼峰\r\n  if (!eventOpts) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n\r\n  // [['handle',[1,2,a]],['handle1',[1,2,a]]]\r\n  const eventType = event.type;\r\n\r\n  const ret = [];\r\n\r\n  eventOpts.forEach(eventOpt => {\r\n    let type = eventOpt[0];\r\n    const eventsArray = eventOpt[1];\r\n\r\n    const isCustom = type.charAt(0) === CUSTOM;\r\n    type = isCustom ? type.slice(1) : type;\r\n    const isOnce = type.charAt(0) === ONCE;\r\n    type = isOnce ? type.slice(1) : type;\r\n\r\n    if (eventsArray && isMatchEventType(eventType, type)) {\r\n      eventsArray.forEach(eventArray => {\r\n        const methodName = eventArray[0];\r\n        if (methodName) {\r\n          let handlerCtx = this.$vm;\r\n          if (handlerCtx.$options.generic) { // mp-weixin,mp-toutiao 抽象节点模拟 scoped slots\r\n            handlerCtx = getContextVm(handlerCtx) || handlerCtx;\r\n          }\r\n          if (methodName === '$emit') {\r\n            handlerCtx.$emit.apply(handlerCtx,\r\n              processEventArgs(\r\n                this.$vm,\r\n                event,\r\n                eventArray[1],\r\n                eventArray[2],\r\n                isCustom,\r\n                methodName\r\n              ));\r\n            return\r\n          }\r\n          const handler = handlerCtx[methodName];\r\n          if (!isFn(handler)) {\r\n            const type = this.$vm.mpType === 'page' ? 'Page' : 'Component';\r\n            const path = this.route || this.is;\r\n            throw new Error(`${type} \"${path}\" does not have a method \"${methodName}\"`)\r\n          }\r\n          if (isOnce) {\r\n            if (handler.once) {\r\n              return\r\n            }\r\n            handler.once = true;\r\n          }\r\n          let params = processEventArgs(\r\n            this.$vm,\r\n            event,\r\n            eventArray[1],\r\n            eventArray[2],\r\n            isCustom,\r\n            methodName\r\n          );\r\n          params = Array.isArray(params) ? params : [];\r\n          // 参数尾部增加原始事件对象用于复杂表达式内获取额外数据\r\n          if (/=\\s*\\S+\\.eventParams\\s*\\|\\|\\s*\\S+\\[['\"]event-params['\"]\\]/.test(handler.toString())) {\r\n            // eslint-disable-next-line no-sparse-arrays\r\n            params = params.concat([, , , , , , , , , , event]);\r\n          }\r\n          ret.push(handler.apply(handlerCtx, params));\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (\r\n    eventType === 'input' &&\r\n    ret.length === 1 &&\r\n    typeof ret[0] !== 'undefined'\r\n  ) {\r\n    return ret[0]\r\n  }\r\n}\r\n\r\nconst eventChannels = {};\r\n\r\nconst eventChannelStack = [];\r\n\r\nfunction getEventChannel (id) {\r\n  if (id) {\r\n    const eventChannel = eventChannels[id];\r\n    delete eventChannels[id];\r\n    return eventChannel\r\n  }\r\n  return eventChannelStack.shift()\r\n}\r\n\r\nconst hooks = [\r\n  'onShow',\r\n  'onHide',\r\n  'onError',\r\n  'onPageNotFound',\r\n  'onThemeChange',\r\n  'onUnhandledRejection'\r\n];\r\n\r\nfunction initEventChannel () {\r\n  Vue.prototype.getOpenerEventChannel = function () {\r\n    // 微信小程序使用自身getOpenerEventChannel\r\n    {\r\n      return this.$scope.getOpenerEventChannel()\r\n    }\r\n  };\r\n  const callHook = Vue.prototype.__call_hook;\r\n  Vue.prototype.__call_hook = function (hook, args) {\r\n    if (hook === 'onLoad' && args && args.__id__) {\r\n      this.__eventChannel__ = getEventChannel(args.__id__);\r\n      delete args.__id__;\r\n    }\r\n    return callHook.call(this, hook, args)\r\n  };\r\n}\r\n\r\nfunction initScopedSlotsParams () {\r\n  const center = {};\r\n  const parents = {};\r\n\r\n  Vue.prototype.$hasScopedSlotsParams = function (vueId) {\r\n    const has = center[vueId];\r\n    if (!has) {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n    return has\r\n  };\r\n\r\n  Vue.prototype.$getScopedSlotsParams = function (vueId, name, key) {\r\n    const data = center[vueId];\r\n    if (data) {\r\n      const object = data[name] || {};\r\n      return key ? object[key] : object\r\n    } else {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n  };\r\n\r\n  Vue.prototype.$setScopedSlotsParams = function (name, value) {\r\n    const vueIds = this.$options.propsData.vueId;\r\n    if (vueIds) {\r\n      const vueId = vueIds.split(',')[0];\r\n      const object = center[vueId] = center[vueId] || {};\r\n      object[name] = value;\r\n      if (parents[vueId]) {\r\n        parents[vueId].$forceUpdate();\r\n      }\r\n    }\r\n  };\r\n\r\n  Vue.mixin({\r\n    destroyed () {\r\n      const propsData = this.$options.propsData;\r\n      const vueId = propsData && propsData.vueId;\r\n      if (vueId) {\r\n        delete center[vueId];\r\n        delete parents[vueId];\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction parseBaseApp (vm, {\r\n  mocks,\r\n  initRefs\r\n}) {\r\n  initEventChannel();\r\n  {\r\n    initScopedSlotsParams();\r\n  }\r\n  if (vm.$options.store) {\r\n    Vue.prototype.$store = vm.$options.store;\r\n  }\r\n  uniIdMixin(Vue);\r\n\r\n  Vue.prototype.mpHost = \"mp-weixin\";\r\n\r\n  Vue.mixin({\r\n    beforeCreate () {\r\n      if (!this.$options.mpType) {\r\n        return\r\n      }\r\n\r\n      this.mpType = this.$options.mpType;\r\n\r\n      this.$mp = {\r\n        data: {},\r\n        [this.mpType]: this.$options.mpInstance\r\n      };\r\n\r\n      this.$scope = this.$options.mpInstance;\r\n\r\n      delete this.$options.mpType;\r\n      delete this.$options.mpInstance;\r\n      if (this.mpType === 'page' && typeof getApp === 'function') { // hack vue-i18n\r\n        const app = getApp();\r\n        if (app.$vm && app.$vm.$i18n) {\r\n          this._i18n = app.$vm.$i18n;\r\n        }\r\n      }\r\n      if (this.mpType !== 'app') {\r\n        initRefs(this);\r\n        initMocks(this, mocks);\r\n      }\r\n    }\r\n  });\r\n\r\n  const appOptions = {\r\n    onLaunch (args) {\r\n      if (this.$vm) { // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\r\n        return\r\n      }\r\n      {\r\n        if (wx.canIUse && !wx.canIUse('nextTick')) { // 事实 上2.2.3 即可，简单使用 2.3.0 的 nextTick 判断\r\n          console.error('当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上');\r\n        }\r\n      }\r\n\r\n      this.$vm = vm;\r\n\r\n      this.$vm.$mp = {\r\n        app: this\r\n      };\r\n\r\n      this.$vm.$scope = this;\r\n      // vm 上也挂载 globalData\r\n      this.$vm.globalData = this.globalData;\r\n\r\n      this.$vm._isMounted = true;\r\n      this.$vm.__call_hook('mounted', args);\r\n\r\n      this.$vm.__call_hook('onLaunch', args);\r\n    }\r\n  };\r\n\r\n  // 兼容旧版本 globalData\r\n  appOptions.globalData = vm.$options.globalData || {};\r\n  // 将 methods 中的方法挂在 getApp() 中\r\n  const methods = vm.$options.methods;\r\n  if (methods) {\r\n    Object.keys(methods).forEach(name => {\r\n      appOptions[name] = methods[name];\r\n    });\r\n  }\r\n\r\n  initAppLocale(Vue, vm, normalizeLocale(wx.getSystemInfoSync().language) || LOCALE_EN);\r\n\r\n  initHooks(appOptions, hooks);\r\n\r\n  return appOptions\r\n}\r\n\r\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\r\n\r\nfunction findVmByVueId (vm, vuePid) {\r\n  const $children = vm.$children;\r\n  // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    const childVm = $children[i];\r\n    if (childVm.$scope._$vueId === vuePid) {\r\n      return childVm\r\n    }\r\n  }\r\n  // 反向递归查找\r\n  let parentVm;\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    parentVm = findVmByVueId($children[i], vuePid);\r\n    if (parentVm) {\r\n      return parentVm\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehavior (options) {\r\n  return Behavior(options)\r\n}\r\n\r\nfunction isPage () {\r\n  return !!this.route\r\n}\r\n\r\nfunction initRelation (detail) {\r\n  this.triggerEvent('__l', detail);\r\n}\r\n\r\nfunction selectAllComponents (mpInstance, selector, $refs) {\r\n  const components = mpInstance.selectAllComponents(selector);\r\n  components.forEach(component => {\r\n    const ref = component.dataset.ref;\r\n    $refs[ref] = component.$vm || component;\r\n    {\r\n      if (component.dataset.vueGeneric === 'scoped') {\r\n        component.selectAllComponents('.scoped-ref').forEach(scopedComponent => {\r\n          selectAllComponents(scopedComponent, selector, $refs);\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction initRefs (vm) {\r\n  const mpInstance = vm.$scope;\r\n  Object.defineProperty(vm, '$refs', {\r\n    get () {\r\n      const $refs = {};\r\n      selectAllComponents(mpInstance, '.vue-ref', $refs);\r\n      // TODO 暂不考虑 for 中的 scoped\r\n      const forComponents = mpInstance.selectAllComponents('.vue-ref-in-for');\r\n      forComponents.forEach(component => {\r\n        const ref = component.dataset.ref;\r\n        if (!$refs[ref]) {\r\n          $refs[ref] = [];\r\n        }\r\n        $refs[ref].push(component.$vm || component);\r\n      });\r\n      return $refs\r\n    }\r\n  });\r\n}\r\n\r\nfunction handleLink (event) {\r\n  const {\r\n    vuePid,\r\n    vueOptions\r\n  } = event.detail || event.value; // detail 是微信,value 是百度(dipatch)\r\n\r\n  let parentVm;\r\n\r\n  if (vuePid) {\r\n    parentVm = findVmByVueId(this.$vm, vuePid);\r\n  }\r\n\r\n  if (!parentVm) {\r\n    parentVm = this.$vm;\r\n  }\r\n\r\n  vueOptions.parent = parentVm;\r\n}\r\n\r\nfunction parseApp (vm) {\r\n  return parseBaseApp(vm, {\r\n    mocks,\r\n    initRefs\r\n  })\r\n}\r\n\r\nfunction createApp (vm) {\r\n  App(parseApp(vm));\r\n  return vm\r\n}\r\n\r\nconst encodeReserveRE = /[!'()*]/g;\r\nconst encodeReserveReplacer = c => '%' + c.charCodeAt(0).toString(16);\r\nconst commaRE = /%2C/g;\r\n\r\n// fixed encodeURIComponent which is more conformant to RFC3986:\r\n// - escapes [!'()*]\r\n// - preserve commas\r\nconst encode = str => encodeURIComponent(str)\r\n  .replace(encodeReserveRE, encodeReserveReplacer)\r\n  .replace(commaRE, ',');\r\n\r\nfunction stringifyQuery (obj, encodeStr = encode) {\r\n  const res = obj ? Object.keys(obj).map(key => {\r\n    const val = obj[key];\r\n\r\n    if (val === undefined) {\r\n      return ''\r\n    }\r\n\r\n    if (val === null) {\r\n      return encodeStr(key)\r\n    }\r\n\r\n    if (Array.isArray(val)) {\r\n      const result = [];\r\n      val.forEach(val2 => {\r\n        if (val2 === undefined) {\r\n          return\r\n        }\r\n        if (val2 === null) {\r\n          result.push(encodeStr(key));\r\n        } else {\r\n          result.push(encodeStr(key) + '=' + encodeStr(val2));\r\n        }\r\n      });\r\n      return result.join('&')\r\n    }\r\n\r\n    return encodeStr(key) + '=' + encodeStr(val)\r\n  }).filter(x => x.length > 0).join('&') : null;\r\n  return res ? `?${res}` : ''\r\n}\r\n\r\nfunction parseBaseComponent (vueComponentOptions, {\r\n  isPage,\r\n  initRelation\r\n} = {}) {\r\n  const [VueComponent, vueOptions] = initVueComponent(Vue, vueComponentOptions);\r\n\r\n  const options = {\r\n    multipleSlots: true,\r\n    addGlobalClass: true,\r\n    ...(vueOptions.options || {})\r\n  };\r\n\r\n  {\r\n    // 微信 multipleSlots 部分情况有 bug，导致内容顺序错乱 如 u-list，提供覆盖选项\r\n    if (vueOptions['mp-weixin'] && vueOptions['mp-weixin'].options) {\r\n      Object.assign(options, vueOptions['mp-weixin'].options);\r\n    }\r\n  }\r\n\r\n  const componentOptions = {\r\n    options,\r\n    data: initData(vueOptions, Vue.prototype),\r\n    behaviors: initBehaviors(vueOptions, initBehavior),\r\n    properties: initProperties(vueOptions.props, false, vueOptions.__file, options),\r\n    lifetimes: {\r\n      attached () {\r\n        const properties = this.properties;\r\n\r\n        const options = {\r\n          mpType: isPage.call(this) ? 'page' : 'component',\r\n          mpInstance: this,\r\n          propsData: properties\r\n        };\r\n\r\n        initVueIds(properties.vueId, this);\r\n\r\n        // 处理父子关系\r\n        initRelation.call(this, {\r\n          vuePid: this._$vuePid,\r\n          vueOptions: options\r\n        });\r\n\r\n        // 初始化 vue 实例\r\n        this.$vm = new VueComponent(options);\r\n\r\n        // 处理$slots,$scopedSlots（暂不支持动态变化$slots）\r\n        initSlots(this.$vm, properties.vueSlots);\r\n\r\n        // 触发首次 setData\r\n        this.$vm.$mount();\r\n      },\r\n      ready () {\r\n        // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\r\n        // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\r\n        if (this.$vm) {\r\n          this.$vm._isMounted = true;\r\n          this.$vm.__call_hook('mounted');\r\n          this.$vm.__call_hook('onReady');\r\n        }\r\n      },\r\n      detached () {\r\n        this.$vm && this.$vm.$destroy();\r\n      }\r\n    },\r\n    pageLifetimes: {\r\n      show (args) {\r\n        this.$vm && this.$vm.__call_hook('onPageShow', args);\r\n      },\r\n      hide () {\r\n        this.$vm && this.$vm.__call_hook('onPageHide');\r\n      },\r\n      resize (size) {\r\n        this.$vm && this.$vm.__call_hook('onPageResize', size);\r\n      }\r\n    },\r\n    methods: {\r\n      __l: handleLink,\r\n      __e: handleEvent\r\n    }\r\n  };\r\n  // externalClasses\r\n  if (vueOptions.externalClasses) {\r\n    componentOptions.externalClasses = vueOptions.externalClasses;\r\n  }\r\n\r\n  if (Array.isArray(vueOptions.wxsCallMethods)) {\r\n    vueOptions.wxsCallMethods.forEach(callMethod => {\r\n      componentOptions.methods[callMethod] = function (args) {\r\n        return this.$vm[callMethod](args)\r\n      };\r\n    });\r\n  }\r\n\r\n  if (isPage) {\r\n    return componentOptions\r\n  }\r\n  return [componentOptions, VueComponent]\r\n}\r\n\r\nfunction parseComponent (vueComponentOptions) {\r\n  return parseBaseComponent(vueComponentOptions, {\r\n    isPage,\r\n    initRelation\r\n  })\r\n}\r\n\r\nconst hooks$1 = [\r\n  'onShow',\r\n  'onHide',\r\n  'onUnload'\r\n];\r\n\r\nhooks$1.push(...PAGE_EVENT_HOOKS);\r\n\r\nfunction parseBasePage (vuePageOptions, {\r\n  isPage,\r\n  initRelation\r\n}) {\r\n  const pageOptions = parseComponent(vuePageOptions);\r\n\r\n  initHooks(pageOptions.methods, hooks$1, vuePageOptions);\r\n\r\n  pageOptions.methods.onLoad = function (query) {\r\n    this.options = query;\r\n    const copyQuery = Object.assign({}, query);\r\n    delete copyQuery.__id__;\r\n    this.$page = {\r\n      fullPath: '/' + (this.route || this.is) + stringifyQuery(copyQuery)\r\n    };\r\n    this.$vm.$mp.query = query; // 兼容 mpvue\r\n    this.$vm.__call_hook('onLoad', query);\r\n  };\r\n\r\n  return pageOptions\r\n}\r\n\r\nfunction parsePage (vuePageOptions) {\r\n  return parseBasePage(vuePageOptions, {\r\n    isPage,\r\n    initRelation\r\n  })\r\n}\r\n\r\nfunction createPage (vuePageOptions) {\r\n  {\r\n    return Component(parsePage(vuePageOptions))\r\n  }\r\n}\r\n\r\nfunction createComponent (vueOptions) {\r\n  {\r\n    return Component(parseComponent(vueOptions))\r\n  }\r\n}\r\n\r\nfunction createSubpackageApp (vm) {\r\n  const appOptions = parseApp(vm);\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  vm.$scope = app;\r\n  const globalData = app.globalData;\r\n  if (globalData) {\r\n    Object.keys(appOptions.globalData).forEach(name => {\r\n      if (!hasOwn(globalData, name)) {\r\n        globalData[name] = appOptions.globalData[name];\r\n      }\r\n    });\r\n  }\r\n  Object.keys(appOptions).forEach(name => {\r\n    if (!hasOwn(app, name)) {\r\n      app[name] = appOptions[name];\r\n    }\r\n  });\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\nfunction createPlugin (vm) {\r\n  const appOptions = parseApp(vm);\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\ntodos.forEach(todoApi => {\r\n  protocols[todoApi] = false;\r\n});\r\n\r\ncanIUses.forEach(canIUseApi => {\r\n  const apiName = protocols[canIUseApi] && protocols[canIUseApi].name ? protocols[canIUseApi].name\r\n    : canIUseApi;\r\n  if (!wx.canIUse(apiName)) {\r\n    protocols[canIUseApi] = false;\r\n  }\r\n});\r\n\r\nlet uni = {};\r\n\r\nif (typeof Proxy !== 'undefined' && \"mp-weixin\" !== 'app-plus') {\r\n  uni = new Proxy({}, {\r\n    get (target, name) {\r\n      if (hasOwn(target, name)) {\r\n        return target[name]\r\n      }\r\n      if (baseApi[name]) {\r\n        return baseApi[name]\r\n      }\r\n      if (api[name]) {\r\n        return promisify(name, api[name])\r\n      }\r\n      {\r\n        if (extraApi[name]) {\r\n          return promisify(name, extraApi[name])\r\n        }\r\n        if (todoApis[name]) {\r\n          return promisify(name, todoApis[name])\r\n        }\r\n      }\r\n      if (eventApi[name]) {\r\n        return eventApi[name]\r\n      }\r\n      if (!hasOwn(wx, name) && !hasOwn(protocols, name)) {\r\n        return\r\n      }\r\n      return promisify(name, wrapper(name, wx[name]))\r\n    },\r\n    set (target, name, value) {\r\n      target[name] = value;\r\n      return true\r\n    }\r\n  });\r\n} else {\r\n  Object.keys(baseApi).forEach(name => {\r\n    uni[name] = baseApi[name];\r\n  });\r\n\r\n  {\r\n    Object.keys(todoApis).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n    Object.keys(extraApi).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n  }\r\n\r\n  Object.keys(eventApi).forEach(name => {\r\n    uni[name] = eventApi[name];\r\n  });\r\n\r\n  Object.keys(api).forEach(name => {\r\n    uni[name] = promisify(name, api[name]);\r\n  });\r\n\r\n  Object.keys(wx).forEach(name => {\r\n    if (hasOwn(wx, name) || hasOwn(protocols, name)) {\r\n      uni[name] = promisify(name, wrapper(name, wx[name]));\r\n    }\r\n  });\r\n}\r\n\r\nwx.createApp = createApp;\r\nwx.createPage = createPage;\r\nwx.createComponent = createComponent;\r\nwx.createSubpackageApp = createSubpackageApp;\r\nwx.createPlugin = createPlugin;\r\n\r\nvar uni$1 = uni;\r\n\r\nexport default uni$1;\r\nexport { createApp, createComponent, createPage, createPlugin, createSubpackageApp };\r\n", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode, /* vue-cli only */\n  components, // fixed by xxxxxx auto components\n  renderjs // fixed by xxxxxx renderjs\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // fixed by xxxxxx auto components\n  if (components) {\n    if (!options.components) {\n      options.components = {}\n    }\n    var hasOwn = Object.prototype.hasOwnProperty\n    for (var name in components) {\n      if (hasOwn.call(components, name) && !hasOwn.call(options.components, name)) {\n        options.components[name] = components[name]\n      }\n    }\n  }\n  // fixed by xxxxxx renderjs\n  if (renderjs) {\n    (renderjs.beforeCreate || (renderjs.beforeCreate = [])).unshift(function() {\n      this[renderjs.__module] = this\n    });\n    (options.mixins || (options.mixins = [])).push(renderjs)\n  }\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", " module.exports = {\n   url: 'http://127.0.0.1:3000/data'\n }\n", "const isArray = Array.isArray;\r\nconst isObject = (val) => val !== null && typeof val === 'object';\r\nconst defaultDelimiters = ['{', '}'];\r\nclass BaseFormatter {\r\n    constructor() {\r\n        this._caches = Object.create(null);\r\n    }\r\n    interpolate(message, values, delimiters = defaultDelimiters) {\r\n        if (!values) {\r\n            return [message];\r\n        }\r\n        let tokens = this._caches[message];\r\n        if (!tokens) {\r\n            tokens = parse(message, delimiters);\r\n            this._caches[message] = tokens;\r\n        }\r\n        return compile(tokens, values);\r\n    }\r\n}\r\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\r\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\r\nfunction parse(format, [startDelimiter, endDelimiter]) {\r\n    const tokens = [];\r\n    let position = 0;\r\n    let text = '';\r\n    while (position < format.length) {\r\n        let char = format[position++];\r\n        if (char === startDelimiter) {\r\n            if (text) {\r\n                tokens.push({ type: 'text', value: text });\r\n            }\r\n            text = '';\r\n            let sub = '';\r\n            char = format[position++];\r\n            while (char !== undefined && char !== endDelimiter) {\r\n                sub += char;\r\n                char = format[position++];\r\n            }\r\n            const isClosed = char === endDelimiter;\r\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\r\n                ? 'list'\r\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\r\n                    ? 'named'\r\n                    : 'unknown';\r\n            tokens.push({ value: sub, type });\r\n        }\r\n        //  else if (char === '%') {\r\n        //   // when found rails i18n syntax, skip text capture\r\n        //   if (format[position] !== '{') {\r\n        //     text += char\r\n        //   }\r\n        // }\r\n        else {\r\n            text += char;\r\n        }\r\n    }\r\n    text && tokens.push({ type: 'text', value: text });\r\n    return tokens;\r\n}\r\nfunction compile(tokens, values) {\r\n    const compiled = [];\r\n    let index = 0;\r\n    const mode = isArray(values)\r\n        ? 'list'\r\n        : isObject(values)\r\n            ? 'named'\r\n            : 'unknown';\r\n    if (mode === 'unknown') {\r\n        return compiled;\r\n    }\r\n    while (index < tokens.length) {\r\n        const token = tokens[index];\r\n        switch (token.type) {\r\n            case 'text':\r\n                compiled.push(token.value);\r\n                break;\r\n            case 'list':\r\n                compiled.push(values[parseInt(token.value, 10)]);\r\n                break;\r\n            case 'named':\r\n                if (mode === 'named') {\r\n                    compiled.push(values[token.value]);\r\n                }\r\n                else {\r\n                    if (process.env.NODE_ENV !== 'production') {\r\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\r\n                    }\r\n                }\r\n                break;\r\n            case 'unknown':\r\n                if (process.env.NODE_ENV !== 'production') {\r\n                    console.warn(`Detect 'unknown' type of token!`);\r\n                }\r\n                break;\r\n        }\r\n        index++;\r\n    }\r\n    return compiled;\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\r\nconst defaultFormatter = new BaseFormatter();\r\nfunction include(str, parts) {\r\n    return !!parts.find((part) => str.indexOf(part) !== -1);\r\n}\r\nfunction startsWith(str, parts) {\r\n    return parts.find((part) => str.indexOf(part) === 0);\r\n}\r\nfunction normalizeLocale(locale, messages) {\r\n    if (!locale) {\r\n        return;\r\n    }\r\n    locale = locale.trim().replace(/_/g, '-');\r\n    if (messages && messages[locale]) {\r\n        return locale;\r\n    }\r\n    locale = locale.toLowerCase();\r\n    if (locale.indexOf('zh') === 0) {\r\n        if (locale.indexOf('-hans') > -1) {\r\n            return LOCALE_ZH_HANS;\r\n        }\r\n        if (locale.indexOf('-hant') > -1) {\r\n            return LOCALE_ZH_HANT;\r\n        }\r\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n            return LOCALE_ZH_HANT;\r\n        }\r\n        return LOCALE_ZH_HANS;\r\n    }\r\n    const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n    if (lang) {\r\n        return lang;\r\n    }\r\n}\r\nclass I18n {\r\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\r\n        this.locale = LOCALE_EN;\r\n        this.fallbackLocale = LOCALE_EN;\r\n        this.message = {};\r\n        this.messages = {};\r\n        this.watchers = [];\r\n        if (fallbackLocale) {\r\n            this.fallbackLocale = fallbackLocale;\r\n        }\r\n        this.formater = formater || defaultFormatter;\r\n        this.messages = messages || {};\r\n        this.setLocale(locale || LOCALE_EN);\r\n        if (watcher) {\r\n            this.watchLocale(watcher);\r\n        }\r\n    }\r\n    setLocale(locale) {\r\n        const oldLocale = this.locale;\r\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\r\n        if (!this.messages[this.locale]) {\r\n            // 可能初始化时不存在\r\n            this.messages[this.locale] = {};\r\n        }\r\n        this.message = this.messages[this.locale];\r\n        // 仅发生变化时，通知\r\n        if (oldLocale !== this.locale) {\r\n            this.watchers.forEach((watcher) => {\r\n                watcher(this.locale, oldLocale);\r\n            });\r\n        }\r\n    }\r\n    getLocale() {\r\n        return this.locale;\r\n    }\r\n    watchLocale(fn) {\r\n        const index = this.watchers.push(fn) - 1;\r\n        return () => {\r\n            this.watchers.splice(index, 1);\r\n        };\r\n    }\r\n    add(locale, message, override = true) {\r\n        const curMessages = this.messages[locale];\r\n        if (curMessages) {\r\n            if (override) {\r\n                Object.assign(curMessages, message);\r\n            }\r\n            else {\r\n                Object.keys(message).forEach((key) => {\r\n                    if (!hasOwn(curMessages, key)) {\r\n                        curMessages[key] = message[key];\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        else {\r\n            this.messages[locale] = message;\r\n        }\r\n    }\r\n    f(message, values, delimiters) {\r\n        return this.formater.interpolate(message, values, delimiters).join('');\r\n    }\r\n    t(key, locale, values) {\r\n        let message = this.message;\r\n        if (typeof locale === 'string') {\r\n            locale = normalizeLocale(locale, this.messages);\r\n            locale && (message = this.messages[locale]);\r\n        }\r\n        else {\r\n            values = locale;\r\n        }\r\n        if (!hasOwn(message, key)) {\r\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\r\n            return key;\r\n        }\r\n        return this.formater.interpolate(message[key], values).join('');\r\n    }\r\n}\r\n\r\nfunction watchAppLocale(appVm, i18n) {\r\n    // 需要保证 watch 的触发在组件渲染之前\r\n    if (appVm.$watchLocale) {\r\n        // vue2\r\n        appVm.$watchLocale((newLocale) => {\r\n            i18n.setLocale(newLocale);\r\n        });\r\n    }\r\n    else {\r\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\r\n            i18n.setLocale(newLocale);\r\n        });\r\n    }\r\n}\r\nfunction getDefaultLocale() {\r\n    if (typeof uni !== 'undefined' && uni.getLocale) {\r\n        return uni.getLocale();\r\n    }\r\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\r\n    if (typeof global !== 'undefined' && global.getLocale) {\r\n        return global.getLocale();\r\n    }\r\n    return LOCALE_EN;\r\n}\r\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\r\n    // 兼容旧版本入参\r\n    if (typeof locale !== 'string') {\r\n        [locale, messages] = [\r\n            messages,\r\n            locale,\r\n        ];\r\n    }\r\n    if (typeof locale !== 'string') {\r\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\r\n        locale = getDefaultLocale();\r\n    }\r\n    if (typeof fallbackLocale !== 'string') {\r\n        fallbackLocale =\r\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\r\n                LOCALE_EN;\r\n    }\r\n    const i18n = new I18n({\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        watcher,\r\n    });\r\n    let t = (key, values) => {\r\n        if (typeof getApp !== 'function') {\r\n            // app view\r\n            /* eslint-disable no-func-assign */\r\n            t = function (key, values) {\r\n                return i18n.t(key, values);\r\n            };\r\n        }\r\n        else {\r\n            let isWatchedAppLocale = false;\r\n            t = function (key, values) {\r\n                const appVm = getApp().$vm;\r\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\r\n                // options: {\r\n                // \ttype: Array,\r\n                // \tdefault () {\r\n                // \t\treturn [{\r\n                // \t\t\ticon: 'shop',\r\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\r\n                // \t\t}, {\r\n                // \t\t\ticon: 'cart',\r\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\r\n                // \t\t}]\r\n                // \t}\r\n                // },\r\n                if (appVm) {\r\n                    // 触发响应式\r\n                    appVm.$locale;\r\n                    if (!isWatchedAppLocale) {\r\n                        isWatchedAppLocale = true;\r\n                        watchAppLocale(appVm, i18n);\r\n                    }\r\n                }\r\n                return i18n.t(key, values);\r\n            };\r\n        }\r\n        return t(key, values);\r\n    };\r\n    return {\r\n        i18n,\r\n        f(message, values, delimiters) {\r\n            return i18n.f(message, values, delimiters);\r\n        },\r\n        t(key, values) {\r\n            return t(key, values);\r\n        },\r\n        add(locale, message, override = true) {\r\n            return i18n.add(locale, message, override);\r\n        },\r\n        watch(fn) {\r\n            return i18n.watchLocale(fn);\r\n        },\r\n        getLocale() {\r\n            return i18n.getLocale();\r\n        },\r\n        setLocale(newLocale) {\r\n            return i18n.setLocale(newLocale);\r\n        },\r\n    };\r\n}\r\n\r\nconst isString = (val) => typeof val === 'string';\r\nlet formater;\r\nfunction hasI18nJson(jsonObj, delimiters) {\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        const value = jsonObj[key];\r\n        if (isString(value)) {\r\n            if (isI18nStr(value, delimiters)) {\r\n                return true;\r\n            }\r\n        }\r\n        else {\r\n            return hasI18nJson(value, delimiters);\r\n        }\r\n    });\r\n}\r\nfunction parseI18nJson(jsonObj, values, delimiters) {\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        const value = jsonObj[key];\r\n        if (isString(value)) {\r\n            if (isI18nStr(value, delimiters)) {\r\n                jsonObj[key] = compileStr(value, values, delimiters);\r\n            }\r\n        }\r\n        else {\r\n            parseI18nJson(value, values, delimiters);\r\n        }\r\n    });\r\n    return jsonObj;\r\n}\r\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\r\n    if (!isI18nStr(jsonStr, delimiters)) {\r\n        return jsonStr;\r\n    }\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    const localeValues = [];\r\n    Object.keys(locales).forEach((name) => {\r\n        if (name !== locale) {\r\n            localeValues.push({\r\n                locale: name,\r\n                values: locales[name],\r\n            });\r\n        }\r\n    });\r\n    localeValues.unshift({ locale, values: locales[locale] });\r\n    try {\r\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\r\n    }\r\n    catch (e) { }\r\n    return jsonStr;\r\n}\r\nfunction isI18nStr(value, delimiters) {\r\n    return value.indexOf(delimiters[0]) > -1;\r\n}\r\nfunction compileStr(value, values, delimiters) {\r\n    return formater.interpolate(value, values, delimiters).join('');\r\n}\r\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\r\n    const value = jsonObj[key];\r\n    if (isString(value)) {\r\n        // 存在国际化\r\n        if (isI18nStr(value, delimiters)) {\r\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\r\n            if (localeValues.length > 1) {\r\n                // 格式化国际化语言\r\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\r\n                localeValues.forEach((localValue) => {\r\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\r\n                });\r\n            }\r\n        }\r\n    }\r\n    else {\r\n        compileJsonObj(value, localeValues, delimiters);\r\n    }\r\n}\r\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\r\n    walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        compileValue(jsonObj, key, localeValues, delimiters);\r\n    });\r\n    return jsonObj;\r\n}\r\nfunction walkJsonObj(jsonObj, walk) {\r\n    if (isArray(jsonObj)) {\r\n        for (let i = 0; i < jsonObj.length; i++) {\r\n            if (walk(jsonObj, i)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    else if (isObject(jsonObj)) {\r\n        for (const key in jsonObj) {\r\n            if (walk(jsonObj, key)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\nfunction resolveLocale(locales) {\r\n    return (locale) => {\r\n        if (!locale) {\r\n            return locale;\r\n        }\r\n        locale = normalizeLocale(locale) || locale;\r\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\r\n    };\r\n}\r\nfunction resolveLocaleChain(locale) {\r\n    const chain = [];\r\n    const tokens = locale.split('-');\r\n    while (tokens.length) {\r\n        chain.push(tokens.join('-'));\r\n        tokens.pop();\r\n    }\r\n    return chain;\r\n}\r\n\r\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\r\n", "/*!\n * Vue.js v2.6.11\n * (c) 2014-2022 Evan You\n * Released under the MIT License.\n */\n/*  */\n\nvar emptyObject = Object.freeze({});\n\n// These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\nfunction isUndef (v) {\n  return v === undefined || v === null\n}\n\nfunction isDef (v) {\n  return v !== undefined && v !== null\n}\n\nfunction isTrue (v) {\n  return v === true\n}\n\nfunction isFalse (v) {\n  return v === false\n}\n\n/**\n * Check if value is primitive.\n */\nfunction isPrimitive (value) {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    // $flow-disable-line\n    typeof value === 'symbol' ||\n    typeof value === 'boolean'\n  )\n}\n\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\nvar _toString = Object.prototype.toString;\n\nfunction toRawType (value) {\n  return _toString.call(value).slice(8, -1)\n}\n\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\nfunction isPlainObject (obj) {\n  return _toString.call(obj) === '[object Object]'\n}\n\nfunction isRegExp (v) {\n  return _toString.call(v) === '[object RegExp]'\n}\n\n/**\n * Check if val is a valid array index.\n */\nfunction isValidArrayIndex (val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val)\n}\n\nfunction isPromise (val) {\n  return (\n    isDef(val) &&\n    typeof val.then === 'function' &&\n    typeof val.catch === 'function'\n  )\n}\n\n/**\n * Convert a value to a string that is actually rendered.\n */\nfunction toString (val) {\n  return val == null\n    ? ''\n    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)\n      ? JSON.stringify(val, null, 2)\n      : String(val)\n}\n\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\nfunction toNumber (val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n\n}\n\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\nfunction makeMap (\n  str,\n  expectsLowerCase\n) {\n  var map = Object.create(null);\n  var list = str.split(',');\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase\n    ? function (val) { return map[val.toLowerCase()]; }\n    : function (val) { return map[val]; }\n}\n\n/**\n * Check if a tag is a built-in tag.\n */\nvar isBuiltInTag = makeMap('slot,component', true);\n\n/**\n * Check if an attribute is a reserved attribute.\n */\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n\n/**\n * Remove an item from an array.\n */\nfunction remove (arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n    if (index > -1) {\n      return arr.splice(index, 1)\n    }\n  }\n}\n\n/**\n * Check whether an object has the property.\n */\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\n/**\n * Create a cached version of a pure function.\n */\nfunction cached (fn) {\n  var cache = Object.create(null);\n  return (function cachedFn (str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str))\n  })\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) { return c ? c.toUpperCase() : ''; })\n});\n\n/**\n * Capitalize a string.\n */\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n});\n\n/**\n * Hyphenate a camelCase string.\n */\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase()\n});\n\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\nfunction polyfillBind (fn, ctx) {\n  function boundFn (a) {\n    var l = arguments.length;\n    return l\n      ? l > 1\n        ? fn.apply(ctx, arguments)\n        : fn.call(ctx, a)\n      : fn.call(ctx)\n  }\n\n  boundFn._length = fn.length;\n  return boundFn\n}\n\nfunction nativeBind (fn, ctx) {\n  return fn.bind(ctx)\n}\n\nvar bind = Function.prototype.bind\n  ? nativeBind\n  : polyfillBind;\n\n/**\n * Convert an Array-like object to a real Array.\n */\nfunction toArray (list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n  return ret\n}\n\n/**\n * Mix properties into target object.\n */\nfunction extend (to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n  return to\n}\n\n/**\n * Merge an Array of Objects into a single Object.\n */\nfunction toObject (arr) {\n  var res = {};\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n  return res\n}\n\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\nfunction noop (a, b, c) {}\n\n/**\n * Always return false.\n */\nvar no = function (a, b, c) { return false; };\n\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\nvar identity = function (_) { return _; };\n\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime()\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\nfunction looseIndexOf (arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) { return i }\n  }\n  return -1\n}\n\n/**\n * Ensure a function is called only once.\n */\nfunction once (fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  }\n}\n\nvar ASSET_TYPES = [\n  'component',\n  'directive',\n  'filter'\n];\n\nvar LIFECYCLE_HOOKS = [\n  'beforeCreate',\n  'created',\n  'beforeMount',\n  'mounted',\n  'beforeUpdate',\n  'updated',\n  'beforeDestroy',\n  'destroyed',\n  'activated',\n  'deactivated',\n  'errorCaptured',\n  'serverPrefetch'\n];\n\n/*  */\n\n\n\nvar config = ({\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n});\n\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n\n/**\n * Check if a string starts with $ or _\n */\nfunction isReserved (str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F\n}\n\n/**\n * Define a property.\n */\nfunction def (obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n\n/**\n * Parse simple path.\n */\nvar bailRE = new RegExp((\"[^\" + (unicodeRegExp.source) + \".$_\\\\d]\"));\nfunction parsePath (path) {\n  if (bailRE.test(path)) {\n    return\n  }\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) { return }\n      obj = obj[segments[i]];\n    }\n    return obj\n  }\n}\n\n/*  */\n\n// can we use __proto__?\nvar hasProto = '__proto__' in {};\n\n// Browser environment sniffing\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = (UA && UA.indexOf('android') > 0) || (weexPlatform === 'android');\nvar isIOS = (UA && /iphone|ipad|ipod|ios/.test(UA)) || (weexPlatform === 'ios');\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/);\n\n// Firefox has a \"watch\" function on Object.prototype...\nvar nativeWatch = ({}).watch;\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', ({\n      get: function get () {\n      }\n    })); // https://github.com/facebook/flow/issues/285\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}\n\n// this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\nvar _isServer;\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n  return _isServer\n};\n\n// detect devtools\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n\n/* istanbul ignore next */\nfunction isNative (Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString())\n}\n\nvar hasSymbol =\n  typeof Symbol !== 'undefined' && isNative(Symbol) &&\n  typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */ // $flow-disable-line\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/(function () {\n    function Set () {\n      this.set = Object.create(null);\n    }\n    Set.prototype.has = function has (key) {\n      return this.set[key] === true\n    };\n    Set.prototype.add = function add (key) {\n      this.set[key] = true;\n    };\n    Set.prototype.clear = function clear () {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }());\n}\n\n/*  */\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = (noop); // work around flow check\nvar formatComponentName = (noop);\n\nif (process.env.NODE_ENV !== 'production') {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n  var classify = function (str) { return str\n    .replace(classifyRE, function (c) { return c.toUpperCase(); })\n    .replace(/[-_]/g, ''); };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && (!config.silent)) {\n      console.error((\"[Vue warn]: \" + msg + trace));\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && (!config.silent)) {\n      console.warn(\"[Vue tip]: \" + msg + (\n        vm ? generateComponentTrace(vm) : ''\n      ));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      if (vm.$options && vm.$options.__file) { // fixed by xxxxxx\n        return ('') + vm.$options.__file\n      }\n      return '<Root>'\n    }\n    var options = typeof vm === 'function' && vm.cid != null\n      ? vm.options\n      : vm._isVue\n        ? vm.$options || vm.constructor.options\n        : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (\n      (name ? (\"<\" + (classify(name)) + \">\") : \"<Anonymous>\") +\n      (file && includeFile !== false ? (\" at \" + file) : '')\n    )\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n    while (n) {\n      if (n % 2 === 1) { res += str; }\n      if (n > 1) { str += str; }\n      n >>= 1;\n    }\n    return res\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n      while (vm && vm.$options.name !== 'PageBody') {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n        !vm.$options.isReserved && tree.push(vm);\n        vm = vm.$parent;\n      }\n      return '\\n\\nfound in\\n\\n' + tree\n        .map(function (vm, i) { return (\"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm)\n            ? ((formatComponentName(vm[0])) + \"... (\" + (vm[1]) + \" recursive calls)\")\n            : formatComponentName(vm))); })\n        .join('\\n')\n    } else {\n      return (\"\\n\\n(found in \" + (formatComponentName(vm)) + \")\")\n    }\n  };\n}\n\n/*  */\n\nvar uid = 0;\n\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\nvar Dep = function Dep () {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub (sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub (sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend () {\n  if (Dep.SharedObject.target) {\n    Dep.SharedObject.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify () {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n  if (process.env.NODE_ENV !== 'production' && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) { return a.id - b.id; });\n  }\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n};\n\n// The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n// fixed by xxxxxx (nvue shared vuex)\n/* eslint-disable no-undef */\nDep.SharedObject = {};\nDep.SharedObject.target = null;\nDep.SharedObject.targetStack = [];\n\nfunction pushTarget (target) {\n  Dep.SharedObject.targetStack.push(target);\n  Dep.SharedObject.target = target;\n  Dep.target = target;\n}\n\nfunction popTarget () {\n  Dep.SharedObject.targetStack.pop();\n  Dep.SharedObject.target = Dep.SharedObject.targetStack[Dep.SharedObject.targetStack.length - 1];\n  Dep.target = Dep.SharedObject.target;\n}\n\n/*  */\n\nvar VNode = function VNode (\n  tag,\n  data,\n  children,\n  text,\n  elm,\n  context,\n  componentOptions,\n  asyncFactory\n) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = { child: { configurable: true } };\n\n// DEPRECATED: alias for componentInstance for backwards compat.\n/* istanbul ignore next */\nprototypeAccessors.child.get = function () {\n  return this.componentInstance\n};\n\nObject.defineProperties( VNode.prototype, prototypeAccessors );\n\nvar createEmptyVNode = function (text) {\n  if ( text === void 0 ) text = '';\n\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node\n};\n\nfunction createTextVNode (val) {\n  return new VNode(undefined, undefined, undefined, String(val))\n}\n\n// optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\nfunction cloneVNode (vnode) {\n  var cloned = new VNode(\n    vnode.tag,\n    vnode.data,\n    // #7975\n    // clone children array to avoid mutating original in case of cloning\n    // a child.\n    vnode.children && vnode.children.slice(),\n    vnode.text,\n    vnode.elm,\n    vnode.context,\n    vnode.componentOptions,\n    vnode.asyncFactory\n  );\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned\n}\n\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\n\nvar methodsToPatch = [\n  'push',\n  'pop',\n  'shift',\n  'unshift',\n  'splice',\n  'sort',\n  'reverse'\n];\n\n/**\n * Intercept mutating methods and emit events\n */\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break\n      case 'splice':\n        inserted = args.slice(2);\n        break\n    }\n    if (inserted) { ob.observeArray(inserted); }\n    // notify change\n    ob.dep.notify();\n    return result\n  });\n});\n\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\nvar shouldObserve = true;\n\nfunction toggleObserving (value) {\n  shouldObserve = value;\n}\n\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\nvar Observer = function Observer (value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      {// fixed by xxxxxx 微信小程序使用 plugins 之后，数组方法被直接挂载到了数组对象上，需要执行 copyAugment 逻辑\n        if(value.push !== value.__proto__.push){\n          copyAugment(value, arrayMethods, arrayKeys);\n        } else {\n          protoAugment(value, arrayMethods);\n        }\n      }\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\nObserver.prototype.walk = function walk (obj) {\n  var keys = Object.keys(obj);\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n\n/**\n * Observe a list of Array items.\n */\nObserver.prototype.observeArray = function observeArray (items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n};\n\n// helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\nfunction protoAugment (target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n/* istanbul ignore next */\nfunction copyAugment (target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\nfunction observe (value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return\n  }\n  var ob;\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (\n    shouldObserve &&\n    !isServerRendering() &&\n    (Array.isArray(value) || isPlainObject(value)) &&\n    Object.isExtensible(value) &&\n    !value._isVue\n  ) {\n    ob = new Observer(value);\n  }\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n  return ob\n}\n\n/**\n * Define a reactive property on an Object.\n */\nfunction defineReactive$$1 (\n  obj,\n  key,\n  val,\n  customSetter,\n  shallow\n) {\n  var dep = new Dep();\n\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n  if (property && property.configurable === false) {\n    return\n  }\n\n  // cater for pre-defined getter/setters\n  var getter = property && property.get;\n  var setter = property && property.set;\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter () {\n      var value = getter ? getter.call(obj) : val;\n      if (Dep.SharedObject.target) { // fixed by xxxxxx\n        dep.depend();\n        if (childOb) {\n          childOb.dep.depend();\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n      return value\n    },\n    set: function reactiveSetter (newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n      if (newVal === value || (newVal !== newVal && value !== value)) {\n        return\n      }\n      /* eslint-enable no-self-compare */\n      if (process.env.NODE_ENV !== 'production' && customSetter) {\n        customSetter();\n      }\n      // #7981: for accessor properties without setter\n      if (getter && !setter) { return }\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\nfunction set (target, key, val) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot set reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val\n  }\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid adding reactive properties to a Vue instance or its root $data ' +\n      'at runtime - declare it upfront in the data option.'\n    );\n    return val\n  }\n  if (!ob) {\n    target[key] = val;\n    return val\n  }\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val\n}\n\n/**\n * Delete a property and trigger change if necessary.\n */\nfunction del (target, key) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot delete reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid deleting properties on a Vue instance or its root $data ' +\n      '- just set it to null.'\n    );\n    return\n  }\n  if (!hasOwn(target, key)) {\n    return\n  }\n  delete target[key];\n  if (!ob) {\n    return\n  }\n  ob.dep.notify();\n}\n\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\nfunction dependArray (value) {\n  for (var e = (void 0), i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\nvar strats = config.optionMergeStrategies;\n\n/**\n * Options with restrictions\n */\nif (process.env.NODE_ENV !== 'production') {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\n        \"option \\\"\" + key + \"\\\" can only be used during instance \" +\n        'creation with the `new` keyword.'\n      );\n    }\n    return defaultStrat(parent, child)\n  };\n}\n\n/**\n * Helper that recursively merges two data objects together.\n */\nfunction mergeData (to, from) {\n  if (!from) { return to }\n  var key, toVal, fromVal;\n\n  var keys = hasSymbol\n    ? Reflect.ownKeys(from)\n    : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i];\n    // in case the object is already observed...\n    if (key === '__ob__') { continue }\n    toVal = to[key];\n    fromVal = from[key];\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (\n      toVal !== fromVal &&\n      isPlainObject(toVal) &&\n      isPlainObject(fromVal)\n    ) {\n      mergeData(toVal, fromVal);\n    }\n  }\n  return to\n}\n\n/**\n * Data\n */\nfunction mergeDataOrFn (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal\n    }\n    if (!parentVal) {\n      return childVal\n    }\n    // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n    return function mergedDataFn () {\n      return mergeData(\n        typeof childVal === 'function' ? childVal.call(this, this) : childVal,\n        typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal\n      )\n    }\n  } else {\n    return function mergedInstanceDataFn () {\n      // instance merge\n      var instanceData = typeof childVal === 'function'\n        ? childVal.call(vm, vm)\n        : childVal;\n      var defaultData = typeof parentVal === 'function'\n        ? parentVal.call(vm, vm)\n        : parentVal;\n      if (instanceData) {\n        return mergeData(instanceData, defaultData)\n      } else {\n        return defaultData\n      }\n    }\n  }\n}\n\nstrats.data = function (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n      process.env.NODE_ENV !== 'production' && warn(\n        'The \"data\" option should be a function ' +\n        'that returns a per-instance value in component ' +\n        'definitions.',\n        vm\n      );\n\n      return parentVal\n    }\n    return mergeDataOrFn(parentVal, childVal)\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm)\n};\n\n/**\n * Hooks and props are merged as arrays.\n */\nfunction mergeHook (\n  parentVal,\n  childVal\n) {\n  var res = childVal\n    ? parentVal\n      ? parentVal.concat(childVal)\n      : Array.isArray(childVal)\n        ? childVal\n        : [childVal]\n    : parentVal;\n  return res\n    ? dedupeHooks(res)\n    : res\n}\n\nfunction dedupeHooks (hooks) {\n  var res = [];\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n  return res\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\nfunction mergeAssets (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  var res = Object.create(parentVal || null);\n  if (childVal) {\n    process.env.NODE_ENV !== 'production' && assertObjectType(key, childVal, vm);\n    return extend(res, childVal)\n  } else {\n    return res\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\nstrats.watch = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) { parentVal = undefined; }\n  if (childVal === nativeWatch) { childVal = undefined; }\n  /* istanbul ignore if */\n  if (!childVal) { return Object.create(parentVal || null) }\n  if (process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = {};\n  extend(ret, parentVal);\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n    ret[key$1] = parent\n      ? parent.concat(child)\n      : Array.isArray(child) ? child : [child];\n  }\n  return ret\n};\n\n/**\n * Other object hashes.\n */\nstrats.props =\nstrats.methods =\nstrats.inject =\nstrats.computed = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  if (childVal && process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n  if (childVal) { extend(ret, childVal); }\n  return ret\n};\nstrats.provide = mergeDataOrFn;\n\n/**\n * Default strategy.\n */\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined\n    ? parentVal\n    : childVal\n};\n\n/**\n * Validate component names\n */\nfunction checkComponents (options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName (name) {\n  if (!new RegExp((\"^[a-zA-Z][\\\\-\\\\.0-9_\" + (unicodeRegExp.source) + \"]*$\")).test(name)) {\n    warn(\n      'Invalid component name: \"' + name + '\". Component names ' +\n      'should conform to valid custom element name in html5 specification.'\n    );\n  }\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn(\n      'Do not use built-in or reserved HTML elements as component ' +\n      'id: ' + name\n    );\n  }\n}\n\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\nfunction normalizeProps (options, vm) {\n  var props = options.props;\n  if (!props) { return }\n  var res = {};\n  var i, val, name;\n  if (Array.isArray(props)) {\n    i = props.length;\n    while (i--) {\n      val = props[i];\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = { type: null };\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val)\n        ? val\n        : { type: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"props\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(props)) + \".\",\n      vm\n    );\n  }\n  options.props = res;\n}\n\n/**\n * Normalize all injections into Object-based format\n */\nfunction normalizeInject (options, vm) {\n  var inject = options.inject;\n  if (!inject) { return }\n  var normalized = options.inject = {};\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = { from: inject[i] };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val)\n        ? extend({ from: key }, val)\n        : { from: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(inject)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Normalize raw function directives into object format.\n */\nfunction normalizeDirectives (options) {\n  var dirs = options.directives;\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n      if (typeof def$$1 === 'function') {\n        dirs[key] = { bind: def$$1, update: def$$1 };\n      }\n    }\n  }\n}\n\nfunction assertObjectType (name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\n      \"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" +\n      \"but got \" + (toRawType(value)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\nfunction mergeOptions (\n  parent,\n  child,\n  vm\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child);\n\n  // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n  for (key in parent) {\n    mergeField(key);\n  }\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n  function mergeField (key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n  return options\n}\n\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\nfunction resolveAsset (\n  options,\n  type,\n  id,\n  warnMissing\n) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return\n  }\n  var assets = options[type];\n  // check local registration variations first\n  if (hasOwn(assets, id)) { return assets[id] }\n  var camelizedId = camelize(id);\n  if (hasOwn(assets, camelizedId)) { return assets[camelizedId] }\n  var PascalCaseId = capitalize(camelizedId);\n  if (hasOwn(assets, PascalCaseId)) { return assets[PascalCaseId] }\n  // fallback to prototype chain\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n  if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n    warn(\n      'Failed to resolve ' + type.slice(0, -1) + ': ' + id,\n      options\n    );\n  }\n  return res\n}\n\n/*  */\n\n\n\nfunction validateProp (\n  key,\n  propOptions,\n  propsData,\n  vm\n) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key];\n  // boolean casting\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  }\n  // check default value\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key);\n    // since the default value is a fresh copy,\n    // make sure to observe it.\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    // skip validation for weex recycle-list child component props\n    !(false)\n  ) {\n    assertProp(prop, key, value, vm, absent);\n  }\n  return value\n}\n\n/**\n * Get the default value of a prop.\n */\nfunction getPropDefaultValue (vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined\n  }\n  var def = prop.default;\n  // warn against non-factory defaults for Object & Array\n  if (process.env.NODE_ENV !== 'production' && isObject(def)) {\n    warn(\n      'Invalid default value for prop \"' + key + '\": ' +\n      'Props with type Object/Array must use a factory function ' +\n      'to return the default value.',\n      vm\n    );\n  }\n  // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n  if (vm && vm.$options.propsData &&\n    vm.$options.propsData[key] === undefined &&\n    vm._props[key] !== undefined\n  ) {\n    return vm._props[key]\n  }\n  // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n  return typeof def === 'function' && getType(prop.type) !== 'Function'\n    ? def.call(vm)\n    : def\n}\n\n/**\n * Assert whether a prop is valid.\n */\nfunction assertProp (\n  prop,\n  name,\n  value,\n  vm,\n  absent\n) {\n  if (prop.required && absent) {\n    warn(\n      'Missing required prop: \"' + name + '\"',\n      vm\n    );\n    return\n  }\n  if (value == null && !prop.required) {\n    return\n  }\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i]);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  if (!valid) {\n    warn(\n      getInvalidTypeMessage(name, value, expectedTypes),\n      vm\n    );\n    return\n  }\n  var validator = prop.validator;\n  if (validator) {\n    if (!validator(value)) {\n      warn(\n        'Invalid prop: custom validator check failed for prop \"' + name + '\".',\n        vm\n      );\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol)$/;\n\nfunction assertType (value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  }\n}\n\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\nfunction getType (fn) {\n  var match = fn && fn.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ''\n}\n\nfunction isSameType (a, b) {\n  return getType(a) === getType(b)\n}\n\nfunction getTypeIndex (type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1\n  }\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction getInvalidTypeMessage (name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" +\n    \" Expected \" + (expectedTypes.map(capitalize).join(', '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 &&\n      isExplicable(expectedType) &&\n      !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + expectedValue;\n  }\n  message += \", got \" + receivedType + \" \";\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + receivedValue + \".\";\n  }\n  return message\n}\n\nfunction styleValue (value, type) {\n  if (type === 'String') {\n    return (\"\\\"\" + value + \"\\\"\")\n  } else if (type === 'Number') {\n    return (\"\" + (Number(value)))\n  } else {\n    return (\"\" + value)\n  }\n}\n\nfunction isExplicable (value) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) { return value.toLowerCase() === elem; })\n}\n\nfunction isBoolean () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  return args.some(function (elem) { return elem.toLowerCase() === 'boolean'; })\n}\n\n/*  */\n\nfunction handleError (err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n  try {\n    if (vm) {\n      var cur = vm;\n      while ((cur = cur.$parent)) {\n        var hooks = cur.$options.errorCaptured;\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n              if (capture) { return }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling (\n  handler,\n  context,\n  args,\n  vm,\n  info\n) {\n  var res;\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) { return handleError(e, vm, info + \" (Promise/async)\"); });\n      // issue #9511\n      // avoid catch triggering multiple times when nested calls\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n  return res\n}\n\nfunction globalHandleError (err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info)\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n  logError(err, vm, info);\n}\n\nfunction logError (err, vm, info) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n  }\n  /* istanbul ignore else */\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err\n  }\n}\n\n/*  */\n\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks () {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n}\n\n// Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\nvar timerFunc;\n\n// The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n/* istanbul ignore next, $flow-disable-line */\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n  timerFunc = function () {\n    p.then(flushCallbacks);\n    // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n    if (isIOS) { setTimeout(noop); }\n  };\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (\n  isNative(MutationObserver) ||\n  // PhantomJS and iOS 7.x\n  MutationObserver.toString() === '[object MutationObserverConstructor]'\n)) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick (cb, ctx) {\n  var _resolve;\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  }\n  // $flow-disable-line\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    })\n  }\n}\n\n/*  */\n\n/* not type checking this file because flow doesn't play well with Proxy */\n\nvar initProxy;\n\nif (process.env.NODE_ENV !== 'production') {\n  var allowedGlobals = makeMap(\n    'Infinity,undefined,NaN,isFinite,isNaN,' +\n    'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' +\n    'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,' +\n    'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\n      \"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" +\n      'referenced during render. Make sure that this property is reactive, ' +\n      'either in the data option, or for class-based components, by ' +\n      'initializing the property. ' +\n      'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',\n      target\n    );\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\n      \"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" +\n      'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' +\n      'prevent conflicts with Vue internals. ' +\n      'See: https://vuejs.org/v2/api/#data',\n      target\n    );\n  };\n\n  var hasProxy =\n    typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set (target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn((\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key));\n          return false\n        } else {\n          target[key] = value;\n          return true\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has (target, key) {\n      var has = key in target;\n      var isAllowed = allowedGlobals(key) ||\n        (typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data));\n      if (!has && !isAllowed) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return has || !isAllowed\n    }\n  };\n\n  var getHandler = {\n    get: function get (target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return target[key]\n    }\n  };\n\n  initProxy = function initProxy (vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped\n        ? getHandler\n        : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n\n/*  */\n\nvar seenObjects = new _Set();\n\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\nfunction traverse (val) {\n  _traverse(val, seenObjects);\n  seenObjects.clear();\n}\n\nfunction _traverse (val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n  if ((!isA && !isObject(val)) || Object.isFrozen(val) || val instanceof VNode) {\n    return\n  }\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n    if (seen.has(depId)) {\n      return\n    }\n    seen.add(depId);\n  }\n  if (isA) {\n    i = val.length;\n    while (i--) { _traverse(val[i], seen); }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n    while (i--) { _traverse(val[keys[i]], seen); }\n  }\n}\n\nvar mark;\nvar measure;\n\nif (process.env.NODE_ENV !== 'production') {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n  if (\n    perf &&\n    perf.mark &&\n    perf.measure &&\n    perf.clearMarks &&\n    perf.clearMeasures\n  ) {\n    mark = function (tag) { return perf.mark(tag); };\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n      // perf.clearMeasures(name)\n    };\n  }\n}\n\n/*  */\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  }\n});\n\nfunction createFnInvoker (fns, vm) {\n  function invoker () {\n    var arguments$1 = arguments;\n\n    var fns = invoker.fns;\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\")\n    }\n  }\n  invoker.fns = fns;\n  return invoker\n}\n\nfunction updateListeners (\n  on,\n  oldOn,\n  add,\n  remove$$1,\n  createOnceHandler,\n  vm\n) {\n  var name, def$$1, cur, old, event;\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n    if (isUndef(cur)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Invalid handler for event \\\"\" + (event.name) + \"\\\": got \" + String(cur),\n        vm\n      );\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\n// fixed by xxxxxx (mp properties)\nfunction extractPropertiesFromVNodeData(data, Ctor, res, context) {\n  var propOptions = Ctor.options.mpOptions && Ctor.options.mpOptions.properties;\n  if (isUndef(propOptions)) {\n    return res\n  }\n  var externalClasses = Ctor.options.mpOptions.externalClasses || [];\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      var result = checkProp(res, props, key, altKey, true) ||\n          checkProp(res, attrs, key, altKey, false);\n      // externalClass\n      if (\n        result &&\n        res[key] &&\n        externalClasses.indexOf(altKey) !== -1 &&\n        context[camelize(res[key])]\n      ) {\n        // 赋值 externalClass 真正的值(模板里 externalClass 的值可能是字符串)\n        res[key] = context[camelize(res[key])];\n      }\n    }\n  }\n  return res\n}\n\nfunction extractPropsFromVNodeData (\n  data,\n  Ctor,\n  tag,\n  context// fixed by xxxxxx\n) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n  if (isUndef(propOptions)) {\n    // fixed by xxxxxx\n    return extractPropertiesFromVNodeData(data, Ctor, {}, context)\n  }\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      if (process.env.NODE_ENV !== 'production') {\n        var keyInLowerCase = key.toLowerCase();\n        if (\n          key !== keyInLowerCase &&\n          attrs && hasOwn(attrs, keyInLowerCase)\n        ) {\n          tip(\n            \"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" +\n            (formatComponentName(tag || Ctor)) + \", but the declared prop name is\" +\n            \" \\\"\" + key + \"\\\". \" +\n            \"Note that HTML attributes are case-insensitive and camelCased \" +\n            \"props need to use their kebab-case equivalents when using in-DOM \" +\n            \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\"\n          );\n        }\n      }\n      checkProp(res, props, key, altKey, true) ||\n      checkProp(res, attrs, key, altKey, false);\n    }\n  }\n  // fixed by xxxxxx\n  return extractPropertiesFromVNodeData(data, Ctor, res, context)\n}\n\nfunction checkProp (\n  res,\n  hash,\n  key,\n  altKey,\n  preserve\n) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n      if (!preserve) {\n        delete hash[key];\n      }\n      return true\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n      if (!preserve) {\n        delete hash[altKey];\n      }\n      return true\n    }\n  }\n  return false\n}\n\n/*  */\n\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\nfunction simpleNormalizeChildren (children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children)\n    }\n  }\n  return children\n}\n\n// 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\nfunction normalizeChildren (children) {\n  return isPrimitive(children)\n    ? [createTextVNode(children)]\n    : Array.isArray(children)\n      ? normalizeArrayChildren(children)\n      : undefined\n}\n\nfunction isTextNode (node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment)\n}\n\nfunction normalizeArrayChildren (children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n    if (isUndef(c) || typeof c === 'boolean') { continue }\n    lastIndex = res.length - 1;\n    last = res[lastIndex];\n    //  nested\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, ((nestedIndex || '') + \"_\" + i));\n        // merge adjacent text nodes\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + (c[0]).text);\n          c.shift();\n        }\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) &&\n          isDef(c.tag) &&\n          isUndef(c.key) &&\n          isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n        res.push(c);\n      }\n    }\n  }\n  return res\n}\n\n/*  */\n\nfunction initProvide (vm) {\n  var provide = vm.$options.provide;\n  if (provide) {\n    vm._provided = typeof provide === 'function'\n      ? provide.call(vm)\n      : provide;\n  }\n}\n\nfunction initInjections (vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\n            \"Avoid mutating an injected value directly since the changes will be \" +\n            \"overwritten whenever the provided component re-renders. \" +\n            \"injection being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        });\n      } else {\n        defineReactive$$1(vm, key, result[key]);\n      }\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject (inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol\n      ? Reflect.ownKeys(inject)\n      : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      // #6574 in case the inject object is observed...\n      if (key === '__ob__') { continue }\n      var provideKey = inject[key].from;\n      var source = vm;\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break\n        }\n        source = source.$parent;\n      }\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function'\n            ? provideDefault.call(vm)\n            : provideDefault;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn((\"Injection \\\"\" + key + \"\\\" not found\"), vm);\n        }\n      }\n    }\n    return result\n  }\n}\n\n/*  */\n\n\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\nfunction resolveSlots (\n  children,\n  context\n) {\n  if (!children || !children.length) {\n    return {}\n  }\n  var slots = {};\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data;\n    // remove slot attribute if the node is resolved as a Vue slot node\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    }\n    // named slots should only be respected if the vnode was rendered in the\n    // same context.\n    if ((child.context === context || child.fnContext === context) &&\n      data && data.slot != null\n    ) {\n      var name = data.slot;\n      var slot = (slots[name] || (slots[name] = []));\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      // fixed by xxxxxx 临时 hack 掉 uni-app 中的异步 name slot page\n      if(child.asyncMeta && child.asyncMeta.data && child.asyncMeta.data.slot === 'page'){\n        (slots['page'] || (slots['page'] = [])).push(child);\n      }else{\n        (slots.default || (slots.default = [])).push(child);\n      }\n    }\n  }\n  // ignore slots that contains only whitespace\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n  return slots\n}\n\nfunction isWhitespace (node) {\n  return (node.isComment && !node.asyncFactory) || node.text === ' '\n}\n\n/*  */\n\nfunction normalizeScopedSlots (\n  slots,\n  normalSlots,\n  prevSlots\n) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized\n  } else if (\n    isStable &&\n    prevSlots &&\n    prevSlots !== emptyObject &&\n    key === prevSlots.$key &&\n    !hasNormalSlots &&\n    !prevSlots.$hasNormal\n  ) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots\n  } else {\n    res = {};\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  }\n  // expose normal slots on scopedSlots\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  }\n  // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n  if (slots && Object.isExtensible(slots)) {\n    (slots)._normalized = res;\n  }\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res)\n      ? [res] // single vnode\n      : normalizeChildren(res);\n    return res && (\n      res.length === 0 ||\n      (res.length === 1 && res[0].isComment) // #9658\n    ) ? undefined\n      : res\n  };\n  // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return normalized\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () { return slots[key]; }\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\nfunction renderList (\n  val,\n  render\n) {\n  var ret, i, l, keys, key;\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i, i, i); // fixed by xxxxxx\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i, i, i); // fixed by xxxxxx\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n      while (!result.done) {\n        ret.push(render(result.value, ret.length, i, i++)); // fixed by xxxxxx\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i, i); // fixed by xxxxxx\n      }\n    }\n  }\n  if (!isDef(ret)) {\n    ret = [];\n  }\n  (ret)._isVList = true;\n  return ret\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\nfunction renderSlot (\n  name,\n  fallback,\n  props,\n  bindObject\n) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n  if (scopedSlotFn) { // scoped slot\n    props = props || {};\n    if (bindObject) {\n      if (process.env.NODE_ENV !== 'production' && !isObject(bindObject)) {\n        warn(\n          'slot v-bind without argument expects an Object',\n          this\n        );\n      }\n      props = extend(extend({}, bindObject), props);\n    }\n    // fixed by xxxxxx app-plus scopedSlot\n    nodes = scopedSlotFn(props, this, props._i) || fallback;\n  } else {\n    nodes = this.$slots[name] || fallback;\n  }\n\n  var target = props && props.slot;\n  if (target) {\n    return this.$createElement('template', { slot: target }, nodes)\n  } else {\n    return nodes\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\nfunction resolveFilter (id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity\n}\n\n/*  */\n\nfunction isKeyNotMatch (expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1\n  } else {\n    return expect !== actual\n  }\n}\n\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\nfunction checkKeyCodes (\n  eventKeyCode,\n  key,\n  builtInKeyCode,\n  eventKeyName,\n  builtInKeyName\n) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName)\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode)\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\nfunction bindObjectProps (\n  data,\n  tag,\n  value,\n  asProp,\n  isSync\n) {\n  if (value) {\n    if (!isObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-bind without argument expects an Object or Array value',\n        this\n      );\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n      var hash;\n      var loop = function ( key ) {\n        if (\n          key === 'class' ||\n          key === 'style' ||\n          isReservedAttribute(key)\n        ) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key)\n            ? data.domProps || (data.domProps = {})\n            : data.attrs || (data.attrs = {});\n        }\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n            on[(\"update:\" + key)] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop( key );\n    }\n  }\n  return data\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\nfunction renderStatic (\n  index,\n  isInFor\n) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index];\n  // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n  if (tree && !isInFor) {\n    return tree\n  }\n  // otherwise, render a fresh tree.\n  tree = cached[index] = this.$options.staticRenderFns[index].call(\n    this._renderProxy,\n    null,\n    this // for render fns generated for functional component templates\n  );\n  markStatic(tree, (\"__static__\" + index), false);\n  return tree\n}\n\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\nfunction markOnce (\n  tree,\n  index,\n  key\n) {\n  markStatic(tree, (\"__once__\" + index + (key ? (\"_\" + key) : \"\")), true);\n  return tree\n}\n\nfunction markStatic (\n  tree,\n  key,\n  isOnce\n) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], (key + \"_\" + i), isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode (node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n\n/*  */\n\nfunction bindObjectListeners (data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-on without argument expects an Object value',\n        this\n      );\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n  return data\n}\n\n/*  */\n\nfunction resolveScopedSlots (\n  fns, // see flow/vnode\n  res,\n  // the following are added in 2.6\n  hasDynamicKeys,\n  contentHashKey\n) {\n  res = res || { $stable: !hasDynamicKeys };\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n      res[slot.key] = slot.fn;\n    }\n  }\n  if (contentHashKey) {\n    (res).$key = contentHashKey;\n  }\n  return res\n}\n\n/*  */\n\nfunction bindDynamicKeys (baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if (process.env.NODE_ENV !== 'production' && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\n        (\"Invalid value for dynamic directive argument (expected string or null): \" + key),\n        this\n      );\n    }\n  }\n  return baseObj\n}\n\n// helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\nfunction prependModifier (value, symbol) {\n  return typeof value === 'string' ? symbol + value : value\n}\n\n/*  */\n\nfunction installRenderHelpers (target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n\n/*  */\n\nfunction FunctionalRenderContext (\n  data,\n  props,\n  children,\n  parent,\n  Ctor\n) {\n  var this$1 = this;\n\n  var options = Ctor.options;\n  // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n  var contextVm;\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent);\n    // $flow-disable-line\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent;\n    // $flow-disable-line\n    parent = parent._original;\n  }\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(\n        data.scopedSlots,\n        this$1.$slots = resolveSlots(children, parent)\n      );\n    }\n    return this$1.$slots\n  };\n\n  Object.defineProperty(this, 'scopedSlots', ({\n    enumerable: true,\n    get: function get () {\n      return normalizeScopedSlots(data.scopedSlots, this.slots())\n    }\n  }));\n\n  // support for compiled functional template\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options;\n    // pre-resolve slots for renderSlot()\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n      return vnode\n    };\n  } else {\n    this._c = function (a, b, c, d) { return createElement(contextVm, a, b, c, d, needNormalization); };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent (\n  Ctor,\n  propsData,\n  data,\n  contextVm,\n  children\n) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) { mergeProps(props, data.attrs); }\n    if (isDef(data.props)) { mergeProps(props, data.props); }\n  }\n\n  var renderContext = new FunctionalRenderContext(\n    data,\n    props,\n    children,\n    contextVm,\n    Ctor\n  );\n\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext)\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n    return res\n  }\n}\n\nfunction cloneAndMarkFunctionalResult (vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n  if (process.env.NODE_ENV !== 'production') {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n  return clone\n}\n\nfunction mergeProps (to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n\n// inline hooks to be invoked on component VNodes during patch\nvar componentVNodeHooks = {\n  init: function init (vnode, hydrating) {\n    if (\n      vnode.componentInstance &&\n      !vnode.componentInstance._isDestroyed &&\n      vnode.data.keepAlive\n    ) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(\n        vnode,\n        activeInstance\n      );\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n\n  prepatch: function prepatch (oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(\n      child,\n      options.propsData, // updated props\n      options.listeners, // updated listeners\n      vnode, // new parent vnode\n      options.children // new children\n    );\n  },\n\n  insert: function insert (vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isMounted) {\n      callHook(componentInstance, 'onServiceCreated');\n      callHook(componentInstance, 'onServiceAttached');\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  },\n\n  destroy: function destroy (vnode) {\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  }\n};\n\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent (\n  Ctor,\n  data,\n  context,\n  children,\n  tag\n) {\n  if (isUndef(Ctor)) {\n    return\n  }\n\n  var baseCtor = context.$options._base;\n\n  // plain options object: turn it into a constructor\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  }\n\n  // if at this stage it's not a constructor or an async component factory,\n  // reject.\n  if (typeof Ctor !== 'function') {\n    if (process.env.NODE_ENV !== 'production') {\n      warn((\"Invalid Component definition: \" + (String(Ctor))), context);\n    }\n    return\n  }\n\n  // async component\n  var asyncFactory;\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(\n        asyncFactory,\n        data,\n        context,\n        children,\n        tag\n      )\n    }\n  }\n\n  data = data || {};\n\n  // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n  resolveConstructorOptions(Ctor);\n\n  // transform component v-model data into props & events\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  }\n\n  // extract props\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag, context); // fixed by xxxxxx\n\n  // functional component\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children)\n  }\n\n  // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n  var listeners = data.on;\n  // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n\n    // work around flow\n    var slot = data.slot;\n    data = {};\n    if (slot) {\n      data.slot = slot;\n    }\n  }\n\n  // install component management hooks onto the placeholder node\n  installComponentHooks(data);\n\n  // return a placeholder vnode\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\n    (\"vue-component-\" + (Ctor.cid) + (name ? (\"-\" + name) : '')),\n    data, undefined, undefined, undefined, context,\n    { Ctor: Ctor, propsData: propsData, listeners: listeners, tag: tag, children: children },\n    asyncFactory\n  );\n\n  return vnode\n}\n\nfunction createComponentInstanceForVnode (\n  vnode, // we know it's MountedComponentVNode but flow doesn't\n  parent // activeInstance in lifecycle state\n) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  };\n  // check inline-template render functions\n  var inlineTemplate = vnode.data.inlineTemplate;\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n  return new vnode.componentOptions.Ctor(options)\n}\n\nfunction installComponentHooks (data) {\n  var hooks = data.hook || (data.hook = {});\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1 (f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n  merged._merged = true;\n  return merged\n}\n\n// transform component v-model info (value and callback) into\n// prop and event handler respectively.\nfunction transformModel (options, data) {\n  var prop = (options.model && options.model.prop) || 'value';\n  var event = (options.model && options.model.event) || 'input'\n  ;(data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n  if (isDef(existing)) {\n    if (\n      Array.isArray(existing)\n        ? existing.indexOf(callback) === -1\n        : existing !== callback\n    ) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n\n/*  */\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2;\n\n// wrapper function for providing a more flexible interface\n// without getting yelled at by flow\nfunction createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType,\n  alwaysNormalize\n) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n  return _createElement(context, tag, data, children, normalizationType)\n}\n\nfunction _createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType\n) {\n  if (isDef(data) && isDef((data).__ob__)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      \"Avoid using observed data object as vnode data: \" + (JSON.stringify(data)) + \"\\n\" +\n      'Always create fresh vnode data objects in each render!',\n      context\n    );\n    return createEmptyVNode()\n  }\n  // object syntax in v-bind\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode()\n  }\n  // warn against non-primitive key\n  if (process.env.NODE_ENV !== 'production' &&\n    isDef(data) && isDef(data.key) && !isPrimitive(data.key)\n  ) {\n    {\n      warn(\n        'Avoid using non-primitive value as key, ' +\n        'use string/number value instead.',\n        context\n      );\n    }\n  }\n  // support single function children as default scoped slot\n  if (Array.isArray(children) &&\n    typeof children[0] === 'function'\n  ) {\n    data = data || {};\n    data.scopedSlots = { default: children[0] };\n    children.length = 0;\n  }\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n  var vnode, ns;\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = (context.$vnode && context.$vnode.ns) || config.getTagNamespace(tag);\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if (process.env.NODE_ENV !== 'production' && isDef(data) && isDef(data.nativeOn)) {\n        warn(\n          (\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\"),\n          context\n        );\n      }\n      vnode = new VNode(\n        config.parsePlatformTagName(tag), data, children,\n        undefined, undefined, context\n      );\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(\n        tag, data, children,\n        undefined, undefined, context\n      );\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n  if (Array.isArray(vnode)) {\n    return vnode\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) { applyNS(vnode, ns); }\n    if (isDef(data)) { registerDeepBindings(data); }\n    return vnode\n  } else {\n    return createEmptyVNode()\n  }\n}\n\nfunction applyNS (vnode, ns, force) {\n  vnode.ns = ns;\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n      if (isDef(child.tag) && (\n        isUndef(child.ns) || (isTrue(force) && child.tag !== 'svg'))) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n}\n\n// ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\nfunction registerDeepBindings (data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n\n/*  */\n\nfunction initRender (vm) {\n  vm._vnode = null; // the root of the child tree\n  vm._staticTrees = null; // v-once cached trees\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject;\n  // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n  vm._c = function (a, b, c, d) { return createElement(vm, a, b, c, d, false); };\n  // normalization is always applied for the public version, used in\n  // user-written render functions.\n  vm.$createElement = function (a, b, c, d) { return createElement(vm, a, b, c, d, true); };\n\n  // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n  var parentData = parentVnode && parentVnode.data;\n\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, null, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, null, true);\n  }\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin (Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this)\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(\n        _parentVnode.data.scopedSlots,\n        vm.$slots,\n        vm.$scopedSlots\n      );\n    }\n\n    // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n    vm.$vnode = _parentVnode;\n    // render self\n    var vnode;\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\");\n      // return error render result,\n      // or previous vnode to prevent render error causing blank component\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production' && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    }\n    // if the returned array contains only a single node, allow it\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    }\n    // return empty vnode in case the render function errored out\n    if (!(vnode instanceof VNode)) {\n      if (process.env.NODE_ENV !== 'production' && Array.isArray(vnode)) {\n        warn(\n          'Multiple root nodes returned from render function. Render function ' +\n          'should return a single root node.',\n          vm\n        );\n      }\n      vnode = createEmptyVNode();\n    }\n    // set parent\n    vnode.parent = _parentVnode;\n    return vnode\n  };\n}\n\n/*  */\n\nfunction ensureCtor (comp, base) {\n  if (\n    comp.__esModule ||\n    (hasSymbol && comp[Symbol.toStringTag] === 'Module')\n  ) {\n    comp = comp.default;\n  }\n  return isObject(comp)\n    ? base.extend(comp)\n    : comp\n}\n\nfunction createAsyncPlaceholder (\n  factory,\n  data,\n  context,\n  children,\n  tag\n) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = { data: data, context: context, children: children, tag: tag };\n  return node\n}\n\nfunction resolveAsyncComponent (\n  factory,\n  baseCtor\n) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved\n  }\n\n  var owner = currentRenderingInstance;\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null\n\n    ;(owner).$on('hook:destroyed', function () { return remove(owners, owner); });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        (owners[i]).$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor);\n      // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n\n    var reject = once(function (reason) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed to resolve async component: \" + (String(factory)) +\n        (reason ? (\"\\nReason: \" + reason) : '')\n      );\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n            if (isUndef(factory.resolved)) {\n              reject(\n                process.env.NODE_ENV !== 'production'\n                  ? (\"timeout (\" + (res.timeout) + \"ms)\")\n                  : null\n              );\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false;\n    // return in case resolved synchronously\n    return factory.loading\n      ? factory.loadingComp\n      : factory.resolved\n  }\n}\n\n/*  */\n\nfunction isAsyncPlaceholder (node) {\n  return node.isComment && node.asyncFactory\n}\n\n/*  */\n\nfunction getFirstComponentChild (children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c\n      }\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\nfunction initEvents (vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false;\n  // init parent attached events\n  var listeners = vm.$options._parentListeners;\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add (event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1 (event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler (event, fn) {\n  var _target = target;\n  return function onceHandler () {\n    var res = fn.apply(null, arguments);\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  }\n}\n\nfunction updateComponentListeners (\n  vm,\n  listeners,\n  oldListeners\n) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin (Vue) {\n  var hookRE = /^hook:/;\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn);\n      // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n    function on () {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this;\n    // all\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm\n    }\n    // array of events\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n      return vm\n    }\n    // specific event\n    var cbs = vm._events[event];\n    if (!cbs) {\n      return vm\n    }\n    if (!fn) {\n      vm._events[event] = null;\n      return vm\n    }\n    // specific handler\n    var cb;\n    var i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n    if (process.env.NODE_ENV !== 'production') {\n      var lowerCaseEvent = event.toLowerCase();\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\n          \"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" +\n          (formatComponentName(vm)) + \" but the handler is registered for \\\"\" + event + \"\\\". \" +\n          \"Note that HTML attributes are case-insensitive and you cannot use \" +\n          \"v-on to listen to camelCase events when using in-DOM templates. \" +\n          \"You should probably use \\\"\" + (hyphenate(event)) + \"\\\" instead of \\\"\" + event + \"\\\".\"\n        );\n      }\n    }\n    var cbs = vm._events[event];\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n    return vm\n  };\n}\n\n/*  */\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  }\n}\n\nfunction initLifecycle (vm) {\n  var options = vm.$options;\n\n  // locate first non-abstract parent\n  var parent = options.parent;\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n\n  vm.$children = [];\n  vm.$refs = {};\n\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin (Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode;\n    // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false /* removeOnly */);\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n    restoreActiveInstance();\n    // update __vue__ reference\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    }\n    // if parent is an HOC, update its $el as well\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    }\n    // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n    if (vm._isBeingDestroyed) {\n      return\n    }\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true;\n    // remove self from parent\n    var parent = vm.$parent;\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    }\n    // teardown watchers\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n    var i = vm._watchers.length;\n    while (i--) {\n      vm._watchers[i].teardown();\n    }\n    // remove reference from data ob\n    // frozen object may not have observer.\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    }\n    // call the last hook...\n    vm._isDestroyed = true;\n    // invoke destroy hooks on current rendered tree\n    vm.__patch__(vm._vnode, null);\n    // fire destroyed hook\n    callHook(vm, 'destroyed');\n    // turn off all instance listeners.\n    vm.$off();\n    // remove __vue__ reference\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    }\n    // release circular reference (#6759)\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction updateChildComponent (\n  vm,\n  propsData,\n  listeners,\n  parentVnode,\n  renderChildren\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = true;\n  }\n\n  // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(\n    (newScopedSlots && !newScopedSlots.$stable) ||\n    (oldScopedSlots !== emptyObject && !oldScopedSlots.$stable) ||\n    (newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key)\n  );\n\n  // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n  var needsForceUpdate = !!(\n    renderChildren ||               // has new static slots\n    vm.$options._renderChildren ||  // has old static slots\n    hasDynamicScopedSlot\n  );\n\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) { // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n  vm.$options._renderChildren = renderChildren;\n\n  // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject;\n\n  // update props\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n    toggleObserving(true);\n    // keep a copy of raw propsData\n    vm.$options.propsData = propsData;\n  }\n  \n  // fixed by xxxxxx update properties(mp runtime)\n  vm._$updateProperties && vm._$updateProperties(vm);\n  \n  // update listeners\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners);\n\n  // resolve slots + force update if has children\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree (vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) { return true }\n  }\n  return false\n}\n\nfunction activateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  } else if (vm._directInactive) {\n    return\n  }\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  }\n  if (!vm._inactive) {\n    vm._inactive = true;\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook (vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n  popTarget();\n}\n\n/*  */\n\nvar MAX_UPDATE_COUNT = 100;\n\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n\n/**\n * Reset the scheduler's state.\n */\nfunction resetSchedulerState () {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n  if (process.env.NODE_ENV !== 'production') {\n    circular = {};\n  }\n  waiting = flushing = false;\n}\n\n// Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\nvar currentFlushTimestamp = 0;\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar getNow = Date.now;\n\n// Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n  if (\n    performance &&\n    typeof performance.now === 'function' &&\n    getNow() > document.createEvent('Event').timeStamp\n  ) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () { return performance.now(); };\n  }\n}\n\n/**\n * Flush both queues and run the watchers.\n */\nfunction flushSchedulerQueue () {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id;\n\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n  queue.sort(function (a, b) { return a.id - b.id; });\n\n  // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n    if (watcher.before) {\n      watcher.before();\n    }\n    id = watcher.id;\n    has[id] = null;\n    watcher.run();\n    // in dev build, check and stop circular updates.\n    if (process.env.NODE_ENV !== 'production' && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn(\n          'You may have an infinite update loop ' + (\n            watcher.user\n              ? (\"in watcher with expression \\\"\" + (watcher.expression) + \"\\\"\")\n              : \"in a component render function.\"\n          ),\n          watcher.vm\n        );\n        break\n      }\n    }\n  }\n\n  // keep copies of post queues before resetting state\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n\n  resetSchedulerState();\n\n  // call component updated and activated hooks\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue);\n\n  // devtool hook\n  /* istanbul ignore if */\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks (queue) {\n  var i = queue.length;\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\nfunction queueActivatedComponent (vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks (queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true /* true */);\n  }\n}\n\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\nfunction queueWatcher (watcher) {\n  var id = watcher.id;\n  if (has[id] == null) {\n    has[id] = true;\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n      queue.splice(i + 1, 0, watcher);\n    }\n    // queue the flush\n    if (!waiting) {\n      waiting = true;\n\n      if (process.env.NODE_ENV !== 'production' && !config.async) {\n        flushSchedulerQueue();\n        return\n      }\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n\n/*  */\n\n\n\nvar uid$2 = 0;\n\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\nvar Watcher = function Watcher (\n  vm,\n  expOrFn,\n  cb,\n  options,\n  isRenderWatcher\n) {\n  this.vm = vm;\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n  vm._watchers.push(this);\n  // options\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression = process.env.NODE_ENV !== 'production'\n    ? expOrFn.toString()\n    : '';\n  // parse expression for getter\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n    if (!this.getter) {\n      this.getter = noop;\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed watching path: \\\"\" + expOrFn + \"\\\" \" +\n        'Watcher only accepts simple dot-delimited paths. ' +\n        'For full control, use a function instead.',\n        vm\n      );\n    }\n  }\n  this.value = this.lazy\n    ? undefined\n    : this.get();\n};\n\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\nWatcher.prototype.get = function get () {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, (\"getter for watcher \\\"\" + (this.expression) + \"\\\"\"));\n    } else {\n      throw e\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n    popTarget();\n    this.cleanupDeps();\n  }\n  return value\n};\n\n/**\n * Add a dependency to this directive.\n */\nWatcher.prototype.addDep = function addDep (dep) {\n  var id = dep.id;\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n\n/**\n * Clean up for dependency collection.\n */\nWatcher.prototype.cleanupDeps = function cleanupDeps () {\n  var i = this.deps.length;\n  while (i--) {\n    var dep = this.deps[i];\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\nWatcher.prototype.update = function update () {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\nWatcher.prototype.run = function run () {\n  if (this.active) {\n    var value = this.get();\n    if (\n      value !== this.value ||\n      // Deep watchers and watchers on Object/Arrays should fire even\n      // when the value is the same, because the value may\n      // have mutated.\n      isObject(value) ||\n      this.deep\n    ) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n      if (this.user) {\n        try {\n          this.cb.call(this.vm, value, oldValue);\n        } catch (e) {\n          handleError(e, this.vm, (\"callback for watcher \\\"\" + (this.expression) + \"\\\"\"));\n        }\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\nWatcher.prototype.evaluate = function evaluate () {\n  this.value = this.get();\n  this.dirty = false;\n};\n\n/**\n * Depend on all deps collected by this watcher.\n */\nWatcher.prototype.depend = function depend () {\n  var i = this.deps.length;\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n\n/**\n * Remove self from all dependencies' subscriber list.\n */\nWatcher.prototype.teardown = function teardown () {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n    var i = this.deps.length;\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n    this.active = false;\n  }\n};\n\n/*  */\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy (target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter () {\n    return this[sourceKey][key]\n  };\n  sharedPropertyDefinition.set = function proxySetter (val) {\n    this[sourceKey][key] = val;\n  };\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState (vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n  if (opts.props) { initProps(vm, opts.props); }\n  if (opts.methods) { initMethods(vm, opts.methods); }\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true /* asRootData */);\n  }\n  if (opts.computed) { initComputed(vm, opts.computed); }\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps (vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {};\n  // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent;\n  // root instance props should be converted\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n  var loop = function ( key ) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      var hyphenatedKey = hyphenate(key);\n      if (isReservedAttribute(hyphenatedKey) ||\n          config.isReservedAttr(hyphenatedKey)) {\n        warn(\n          (\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\"),\n          vm\n        );\n      }\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          {\n            if(vm.mpHost === 'mp-baidu' || vm.mpHost === 'mp-kuaishou' || vm.mpHost === 'mp-xhs'){//百度、快手、小红书 observer 在 setData callback 之后触发，直接忽略该 warn\n                return\n            }\n            //fixed by xxxxxx __next_tick_pending,uni://form-field 时不告警\n            if(\n                key === 'value' && \n                Array.isArray(vm.$options.behaviors) &&\n                vm.$options.behaviors.indexOf('uni://form-field') !== -1\n              ){\n              return\n            }\n            if(vm._getFormData){\n              return\n            }\n            var $parent = vm.$parent;\n            while($parent){\n              if($parent.__next_tick_pending){\n                return  \n              }\n              $parent = $parent.$parent;\n            }\n          }\n          warn(\n            \"Avoid mutating a prop directly since the value will be \" +\n            \"overwritten whenever the parent component re-renders. \" +\n            \"Instead, use a data or computed property based on the prop's \" +\n            \"value. Prop being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        }\n      });\n    } else {\n      defineReactive$$1(props, key, value);\n    }\n    // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop( key );\n  toggleObserving(true);\n}\n\nfunction initData (vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function'\n    ? getData(data, vm)\n    : data || {};\n  if (!isPlainObject(data)) {\n    data = {};\n    process.env.NODE_ENV !== 'production' && warn(\n      'data functions should return an object:\\n' +\n      'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function',\n      vm\n    );\n  }\n  // proxy data on instance\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n  while (i--) {\n    var key = keys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (methods && hasOwn(methods, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\"),\n          vm\n        );\n      }\n    }\n    if (props && hasOwn(props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" +\n        \"Use prop default value instead.\",\n        vm\n      );\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  }\n  // observe data\n  observe(data, true /* asRootData */);\n}\n\nfunction getData (data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n  try {\n    return data.call(vm, vm)\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {}\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = { lazy: true };\n\nfunction initComputed (vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null);\n  // computed properties are just getters during SSR\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n    if (process.env.NODE_ENV !== 'production' && getter == null) {\n      warn(\n        (\"Getter is missing for computed property \\\"\" + key + \"\\\".\"),\n        vm\n      );\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(\n        vm,\n        getter || noop,\n        noop,\n        computedWatcherOptions\n      );\n    }\n\n    // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (process.env.NODE_ENV !== 'production') {\n      if (key in vm.$data) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined in data.\"), vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\"), vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed (\n  target,\n  key,\n  userDef\n) {\n  var shouldCache = !isServerRendering();\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache\n      ? createComputedGetter(key)\n      : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get\n      ? shouldCache && userDef.cache !== false\n        ? createComputedGetter(key)\n        : createGetterInvoker(userDef.get)\n      : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n  if (process.env.NODE_ENV !== 'production' &&\n      sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\n        (\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\"),\n        this\n      );\n    };\n  }\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter (key) {\n  return function computedGetter () {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n      if (Dep.SharedObject.target) {// fixed by xxxxxx\n        watcher.depend();\n      }\n      return watcher.value\n    }\n  }\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter () {\n    return fn.call(this, this)\n  }\n}\n\nfunction initMethods (vm, methods) {\n  var props = vm.$options.props;\n  for (var key in methods) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof methods[key] !== 'function') {\n        warn(\n          \"Method \\\"\" + key + \"\\\" has type \\\"\" + (typeof methods[key]) + \"\\\" in the component definition. \" +\n          \"Did you reference the function correctly?\",\n          vm\n        );\n      }\n      if (props && hasOwn(props, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\"),\n          vm\n        );\n      }\n      if ((key in vm) && isReserved(key)) {\n        warn(\n          \"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" +\n          \"Avoid defining component methods that start with _ or $.\"\n        );\n      }\n    }\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch (vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher (\n  vm,\n  expOrFn,\n  handler,\n  options\n) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n  return vm.$watch(expOrFn, handler, options)\n}\n\nfunction stateMixin (Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n  dataDef.get = function () { return this._data };\n  var propsDef = {};\n  propsDef.get = function () { return this._props };\n  if (process.env.NODE_ENV !== 'production') {\n    dataDef.set = function () {\n      warn(\n        'Avoid replacing instance root $data. ' +\n        'Use nested data properties instead.',\n        this\n      );\n    };\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (\n    expOrFn,\n    cb,\n    options\n  ) {\n    var vm = this;\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options)\n    }\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n    if (options.immediate) {\n      try {\n        cb.call(vm, watcher.value);\n      } catch (error) {\n        handleError(error, vm, (\"callback for immediate watcher \\\"\" + (watcher.expression) + \"\\\"\"));\n      }\n    }\n    return function unwatchFn () {\n      watcher.teardown();\n    }\n  };\n}\n\n/*  */\n\nvar uid$3 = 0;\n\nfunction initMixin (Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this;\n    // a uid\n    vm._uid = uid$3++;\n\n    var startTag, endTag;\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + (vm._uid);\n      endTag = \"vue-perf-end:\" + (vm._uid);\n      mark(startTag);\n    }\n\n    // a flag to avoid this being observed\n    vm._isVue = true;\n    // merge options\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(\n        resolveConstructorOptions(vm.constructor),\n        options || {},\n        vm\n      );\n    }\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      initProxy(vm);\n    } else {\n      vm._renderProxy = vm;\n    }\n    // expose real self\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    !vm._$fallback && initInjections(vm); // resolve injections before data/props  \n    initState(vm);\n    !vm._$fallback && initProvide(vm); // resolve provide after data/props\n    !vm._$fallback && callHook(vm, 'created');      \n\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure((\"vue \" + (vm._name) + \" init\"), startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent (vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options);\n  // doing this because it's faster than dynamic enumeration.\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions (Ctor) {\n  var options = Ctor.options;\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions;\n      // check if there are any late-modified/attached options (#4976)\n      var modifiedOptions = resolveModifiedOptions(Ctor);\n      // update base extend options\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n  return options\n}\n\nfunction resolveModifiedOptions (Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) { modified = {}; }\n      modified[key] = latest[key];\n    }\n  }\n  return modified\n}\n\nfunction Vue (options) {\n  if (process.env.NODE_ENV !== 'production' &&\n    !(this instanceof Vue)\n  ) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n\n/*  */\n\nfunction initUse (Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = (this._installedPlugins || (this._installedPlugins = []));\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this\n    }\n\n    // additional parameters\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n    installedPlugins.push(plugin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initMixin$1 (Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initExtend (Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n\n  /**\n   * Class inheritance\n   */\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId]\n    }\n\n    var name = extendOptions.name || Super.options.name;\n    if (process.env.NODE_ENV !== 'production' && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent (options) {\n      this._init(options);\n    };\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(\n      Super.options,\n      extendOptions\n    );\n    Sub['super'] = Super;\n\n    // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    }\n\n    // allow further extension/mixin/plugin usage\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use;\n\n    // create asset registers, so extended classes\n    // can have their private assets too.\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    });\n    // enable recursive self-lookup\n    if (name) {\n      Sub.options.components[name] = Sub;\n    }\n\n    // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options);\n\n    // cache constructor\n    cachedCtors[SuperId] = Sub;\n    return Sub\n  };\n}\n\nfunction initProps$1 (Comp) {\n  var props = Comp.options.props;\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1 (Comp) {\n  var computed = Comp.options.computed;\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n\n/*  */\n\nfunction initAssetRegisters (Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (\n      id,\n      definition\n    ) {\n      if (!definition) {\n        return this.options[type + 's'][id]\n      } else {\n        /* istanbul ignore if */\n        if (process.env.NODE_ENV !== 'production' && type === 'component') {\n          validateComponentName(id);\n        }\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = { bind: definition, update: definition };\n        }\n        this.options[type + 's'][id] = definition;\n        return definition\n      }\n    };\n  });\n}\n\n/*  */\n\n\n\nfunction getComponentName (opts) {\n  return opts && (opts.Ctor.options.name || opts.tag)\n}\n\nfunction matches (pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name)\n  }\n  /* istanbul ignore next */\n  return false\n}\n\nfunction pruneCache (keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n  for (var key in cache) {\n    var cachedNode = cache[key];\n    if (cachedNode) {\n      var name = getComponentName(cachedNode.componentOptions);\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry (\n  cache,\n  key,\n  keys,\n  current\n) {\n  var cached$$1 = cache[key];\n  if (cached$$1 && (!current || cached$$1.tag !== current.tag)) {\n    cached$$1.componentInstance.$destroy();\n  }\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\n\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n\n  created: function created () {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n\n  destroyed: function destroyed () {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n\n  mounted: function mounted () {\n    var this$1 = this;\n\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) { return matches(val, name); });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) { return !matches(val, name); });\n    });\n  },\n\n  render: function render () {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n      if (\n        // not included\n        (include && (!name || !matches(include, name))) ||\n        // excluded\n        (exclude && name && matches(exclude, name))\n      ) {\n        return vnode\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null\n        // same constructor may get registered as different local components\n        // so cid alone is not enough (#3269)\n        ? componentOptions.Ctor.cid + (componentOptions.tag ? (\"::\" + (componentOptions.tag)) : '')\n        : vnode.key;\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance;\n        // make current key freshest\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        cache[key] = vnode;\n        keys.push(key);\n        // prune oldest entry\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n      }\n\n      vnode.data.keepAlive = true;\n    }\n    return vnode || (slot && slot[0])\n  }\n};\n\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n\n/*  */\n\nfunction initGlobalAPI (Vue) {\n  // config\n  var configDef = {};\n  configDef.get = function () { return config; };\n  if (process.env.NODE_ENV !== 'production') {\n    configDef.set = function () {\n      warn(\n        'Do not replace the Vue.config object, set individual fields instead.'\n      );\n    };\n  }\n  Object.defineProperty(Vue, 'config', configDef);\n\n  // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick;\n\n  // 2.6 explicit observable API\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  });\n\n  // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n  Vue.options._base = Vue;\n\n  extend(Vue.options.components, builtInComponents);\n\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\n\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\n\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get () {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext\n  }\n});\n\n// expose FunctionalRenderContext for ssr runtime helper installation\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\n\nVue.version = '2.6.11';\n\n/**\n * https://raw.githubusercontent.com/Tencent/westore/master/packages/westore/utils/diff.js\n */\nvar ARRAYTYPE = '[object Array]';\nvar OBJECTTYPE = '[object Object]';\n// const FUNCTIONTYPE = '[object Function]'\n\nfunction diff(current, pre) {\n    var result = {};\n    syncKeys(current, pre);\n    _diff(current, pre, '', result);\n    return result\n}\n\nfunction syncKeys(current, pre) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n        if(Object.keys(current).length >= Object.keys(pre).length){\n            for (var key in pre) {\n                var currentValue = current[key];\n                if (currentValue === undefined) {\n                    current[key] = null;\n                } else {\n                    syncKeys(currentValue, pre[key]);\n                }\n            }\n        }\n    } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n        if (current.length >= pre.length) {\n            pre.forEach(function (item, index) {\n                syncKeys(current[index], item);\n            });\n        }\n    }\n}\n\nfunction _diff(current, pre, path, result) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE) {\n        if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n            setResult(result, path, current);\n        } else {\n            var loop = function ( key ) {\n                var currentValue = current[key];\n                var preValue = pre[key];\n                var currentType = type(currentValue);\n                var preType = type(preValue);\n                if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n                    if (currentValue !== pre[key]) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    }\n                } else if (currentType == ARRAYTYPE) {\n                    if (preType != ARRAYTYPE) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        if (currentValue.length < preValue.length) {\n                            setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                        } else {\n                            currentValue.forEach(function (item, index) {\n                                _diff(item, preValue[index], (path == '' ? '' : path + \".\") + key + '[' + index + ']', result);\n                            });\n                        }\n                    }\n                } else if (currentType == OBJECTTYPE) {\n                    if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        for (var subKey in currentValue) {\n                            _diff(currentValue[subKey], preValue[subKey], (path == '' ? '' : path + \".\") + key + '.' + subKey, result);\n                        }\n                    }\n                }\n            };\n\n            for (var key in current) loop( key );\n        }\n    } else if (rootCurrentType == ARRAYTYPE) {\n        if (rootPreType != ARRAYTYPE) {\n            setResult(result, path, current);\n        } else {\n            if (current.length < pre.length) {\n                setResult(result, path, current);\n            } else {\n                current.forEach(function (item, index) {\n                    _diff(item, pre[index], path + '[' + index + ']', result);\n                });\n            }\n        }\n    } else {\n        setResult(result, path, current);\n    }\n}\n\nfunction setResult(result, k, v) {\n    // if (type(v) != FUNCTIONTYPE) {\n        result[k] = v;\n    // }\n}\n\nfunction type(obj) {\n    return Object.prototype.toString.call(obj)\n}\n\n/*  */\n\nfunction flushCallbacks$1(vm) {\n    if (vm.__next_tick_callbacks && vm.__next_tick_callbacks.length) {\n        if (process.env.VUE_APP_DEBUG) {\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:flushCallbacks[' + vm.__next_tick_callbacks.length + ']');\n        }\n        var copies = vm.__next_tick_callbacks.slice(0);\n        vm.__next_tick_callbacks.length = 0;\n        for (var i = 0; i < copies.length; i++) {\n            copies[i]();\n        }\n    }\n}\n\nfunction hasRenderWatcher(vm) {\n    return queue.find(function (watcher) { return vm._watcher === watcher; })\n}\n\nfunction nextTick$1(vm, cb) {\n    //1.nextTick 之前 已 setData 且 setData 还未回调完成\n    //2.nextTick 之前存在 render watcher\n    if (!vm.__next_tick_pending && !hasRenderWatcher(vm)) {\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:nextVueTick');\n        }\n        return nextTick(cb, vm)\n    }else{\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance$1 = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance$1.is || mpInstance$1.route) + '][' + vm._uid +\n                ']:nextMPTick');\n        }\n    }\n    var _resolve;\n    if (!vm.__next_tick_callbacks) {\n        vm.__next_tick_callbacks = [];\n    }\n    vm.__next_tick_callbacks.push(function () {\n        if (cb) {\n            try {\n                cb.call(vm);\n            } catch (e) {\n                handleError(e, vm, 'nextTick');\n            }\n        } else if (_resolve) {\n            _resolve(vm);\n        }\n    });\n    // $flow-disable-line\n    if (!cb && typeof Promise !== 'undefined') {\n        return new Promise(function (resolve) {\n            _resolve = resolve;\n        })\n    }\n}\n\n/*  */\n\nfunction cloneWithData(vm) {\n  // 确保当前 vm 所有数据被同步\n  var ret = Object.create(null);\n  var dataKeys = [].concat(\n    Object.keys(vm._data || {}),\n    Object.keys(vm._computedWatchers || {}));\n\n  dataKeys.reduce(function(ret, key) {\n    ret[key] = vm[key];\n    return ret\n  }, ret);\n\n  // vue-composition-api\n  var compositionApiState = vm.__composition_api_state__ || vm.__secret_vfa_state__;\n  var rawBindings = compositionApiState && compositionApiState.rawBindings;\n  if (rawBindings) {\n    Object.keys(rawBindings).forEach(function (key) {\n      ret[key] = vm[key];\n    });\n  }\n\n  //TODO 需要把无用数据处理掉，比如 list=>l0 则 list 需要移除，否则多传输一份数据\n  Object.assign(ret, vm.$mp.data || {});\n  if (\n    Array.isArray(vm.$options.behaviors) &&\n    vm.$options.behaviors.indexOf('uni://form-field') !== -1\n  ) { //form-field\n    ret['name'] = vm.name;\n    ret['value'] = vm.value;\n  }\n\n  return JSON.parse(JSON.stringify(ret))\n}\n\nvar patch = function(oldVnode, vnode) {\n  var this$1 = this;\n\n  if (vnode === null) { //destroy\n    return\n  }\n  if (this.mpType === 'page' || this.mpType === 'component') {\n    var mpInstance = this.$scope;\n    var data = Object.create(null);\n    try {\n      data = cloneWithData(this);\n    } catch (err) {\n      console.error(err);\n    }\n    data.__webviewId__ = mpInstance.data.__webviewId__;\n    var mpData = Object.create(null);\n    Object.keys(data).forEach(function (key) { //仅同步 data 中有的数据\n      mpData[key] = mpInstance.data[key];\n    });\n    var diffData = this.$shouldDiffData === false ? data : diff(data, mpData);\n    if (Object.keys(diffData).length) {\n      if (process.env.VUE_APP_DEBUG) {\n        console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + this._uid +\n          ']差量更新',\n          JSON.stringify(diffData));\n      }\n      this.__next_tick_pending = true;\n      mpInstance.setData(diffData, function () {\n        this$1.__next_tick_pending = false;\n        flushCallbacks$1(this$1);\n      });\n    } else {\n      flushCallbacks$1(this);\n    }\n  }\n};\n\n/*  */\n\nfunction createEmptyRender() {\n\n}\n\nfunction mountComponent$1(\n  vm,\n  el,\n  hydrating\n) {\n  if (!vm.mpType) {//main.js 中的 new Vue\n    return vm\n  }\n  if (vm.mpType === 'app') {\n    vm.$options.render = createEmptyRender;\n  }\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyRender;\n    if (process.env.NODE_ENV !== 'production') {\n      /* istanbul ignore if */\n      if ((vm.$options.template && vm.$options.template.charAt(0) !== '#') ||\n        vm.$options.el || el) {\n        warn(\n          'You are using the runtime-only build of Vue where the template ' +\n          'compiler is not available. Either pre-compile the templates into ' +\n          'render functions, or use the compiler-included build.',\n          vm\n        );\n      } else {\n        warn(\n          'Failed to mount component: template or render function not defined.',\n          vm\n        );\n      }\n    }\n  }\n  \n  !vm._$fallback && callHook(vm, 'beforeMount');\n\n  var updateComponent = function () {\n    vm._update(vm._render(), hydrating);\n  };\n\n  // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true /* isRenderWatcher */);\n  hydrating = false;\n  return vm\n}\n\n/*  */\n\nfunction renderClass (\n  staticClass,\n  dynamicClass\n) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass))\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction concat (a, b) {\n  return a ? b ? (a + ' ' + b) : a : (b || '')\n}\n\nfunction stringifyClass (value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value)\n  }\n  if (isObject(value)) {\n    return stringifyObject(value)\n  }\n  if (typeof value === 'string') {\n    return value\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction stringifyArray (value) {\n  var res = '';\n  var stringified;\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) { res += ' '; }\n      res += stringified;\n    }\n  }\n  return res\n}\n\nfunction stringifyObject (value) {\n  var res = '';\n  for (var key in value) {\n    if (value[key]) {\n      if (res) { res += ' '; }\n      res += key;\n    }\n  }\n  return res\n}\n\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res\n});\n\n// normalize possible array / string values into Object\nfunction normalizeStyleBinding (bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle)\n  }\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle)\n  }\n  return bindingStyle\n}\n\n/*  */\n\nvar MP_METHODS = ['createSelectorQuery', 'createIntersectionObserver', 'selectAllComponents', 'selectComponent'];\n\nfunction getTarget(obj, path) {\n  var parts = path.split('.');\n  var key = parts[0];\n  if (key.indexOf('__$n') === 0) { //number index\n    key = parseInt(key.replace('__$n', ''));\n  }\n  if (parts.length === 1) {\n    return obj[key]\n  }\n  return getTarget(obj[key], parts.slice(1).join('.'))\n}\n\nfunction internalMixin(Vue) {\n\n  Vue.config.errorHandler = function(err, vm, info) {\n    Vue.util.warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n    console.error(err);\n    /* eslint-disable no-undef */\n    var app = typeof getApp === 'function' && getApp();\n    if (app && app.onError) {\n      app.onError(err);\n    }\n  };\n\n  var oldEmit = Vue.prototype.$emit;\n\n  Vue.prototype.$emit = function(event) {\n    if (this.$scope && event) {\n      (this.$scope['_triggerEvent'] || this.$scope['triggerEvent']).call(this.$scope, event, {\n        __args__: toArray(arguments, 1)\n      });\n    }\n    return oldEmit.apply(this, arguments)\n  };\n\n  Vue.prototype.$nextTick = function(fn) {\n    return nextTick$1(this, fn)\n  };\n\n  MP_METHODS.forEach(function (method) {\n    Vue.prototype[method] = function(args) {\n      if (this.$scope && this.$scope[method]) {\n        return this.$scope[method](args)\n      }\n      // mp-alipay\n      if (typeof my === 'undefined') {\n        return\n      }\n      if (method === 'createSelectorQuery') {\n        /* eslint-disable no-undef */\n        return my.createSelectorQuery(args)\n      } else if (method === 'createIntersectionObserver') {\n        /* eslint-disable no-undef */\n        return my.createIntersectionObserver(args)\n      }\n      // TODO mp-alipay 暂不支持 selectAllComponents,selectComponent\n    };\n  });\n\n  Vue.prototype.__init_provide = initProvide;\n\n  Vue.prototype.__init_injections = initInjections;\n\n  Vue.prototype.__call_hook = function(hook, args) {\n    var vm = this;\n    // #7573 disable dep collection when invoking lifecycle hooks\n    pushTarget();\n    var handlers = vm.$options[hook];\n    var info = hook + \" hook\";\n    var ret;\n    if (handlers) {\n      for (var i = 0, j = handlers.length; i < j; i++) {\n        ret = invokeWithErrorHandling(handlers[i], vm, args ? [args] : null, vm, info);\n      }\n    }\n    if (vm._hasHookEvent) {\n      vm.$emit('hook:' + hook, args);\n    }\n    popTarget();\n    return ret\n  };\n\n  Vue.prototype.__set_model = function(target, key, value, modifiers) {\n    if (Array.isArray(modifiers)) {\n      if (modifiers.indexOf('trim') !== -1) {\n        value = value.trim();\n      }\n      if (modifiers.indexOf('number') !== -1) {\n        value = this._n(value);\n      }\n    }\n    if (!target) {\n      target = this;\n    }\n    // 解决动态属性添加\n    Vue.set(target, key, value);\n  };\n\n  Vue.prototype.__set_sync = function(target, key, value) {\n    if (!target) {\n      target = this;\n    }\n    // 解决动态属性添加\n    Vue.set(target, key, value);\n  };\n\n  Vue.prototype.__get_orig = function(item) {\n    if (isPlainObject(item)) {\n      return item['$orig'] || item\n    }\n    return item\n  };\n\n  Vue.prototype.__get_value = function(dataPath, target) {\n    return getTarget(target || this, dataPath)\n  };\n\n\n  Vue.prototype.__get_class = function(dynamicClass, staticClass) {\n    return renderClass(staticClass, dynamicClass)\n  };\n\n  Vue.prototype.__get_style = function(dynamicStyle, staticStyle) {\n    if (!dynamicStyle && !staticStyle) {\n      return ''\n    }\n    var dynamicStyleObj = normalizeStyleBinding(dynamicStyle);\n    var styleObj = staticStyle ? extend(staticStyle, dynamicStyleObj) : dynamicStyleObj;\n    return Object.keys(styleObj).map(function (name) { return ((hyphenate(name)) + \":\" + (styleObj[name])); }).join(';')\n  };\n\n  Vue.prototype.__map = function(val, iteratee) {\n    //TODO 暂不考虑 string\n    var ret, i, l, keys, key;\n    if (Array.isArray(val)) {\n      ret = new Array(val.length);\n      for (i = 0, l = val.length; i < l; i++) {\n        ret[i] = iteratee(val[i], i);\n      }\n      return ret\n    } else if (isObject(val)) {\n      keys = Object.keys(val);\n      ret = Object.create(null);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[key] = iteratee(val[key], key, i);\n      }\n      return ret\n    } else if (typeof val === 'number') {\n      ret = new Array(val);\n      for (i = 0, l = val; i < l; i++) {\n        // 第一个参数暂时仍和小程序一致\n        ret[i] = iteratee(i, i);\n      }\n      return ret\n    }\n    return []\n  };\n\n}\n\n/*  */\n\nvar LIFECYCLE_HOOKS$1 = [\n    //App\n    'onLaunch',\n    'onShow',\n    'onHide',\n    'onUniNViewMessage',\n    'onPageNotFound',\n    'onThemeChange',\n    'onError',\n    'onUnhandledRejection',\n    //Page\n    'onInit',\n    'onLoad',\n    // 'onShow',\n    'onReady',\n    // 'onHide',\n    'onUnload',\n    'onPullDownRefresh',\n    'onReachBottom',\n    'onTabItemTap',\n    'onAddToFavorites',\n    'onShareTimeline',\n    'onShareAppMessage',\n    'onResize',\n    'onPageScroll',\n    'onNavigationBarButtonTap',\n    'onBackPress',\n    'onNavigationBarSearchInputChanged',\n    'onNavigationBarSearchInputConfirmed',\n    'onNavigationBarSearchInputClicked',\n    //Component\n    // 'onReady', // 兼容旧版本，应该移除该事件\n    'onPageShow',\n    'onPageHide',\n    'onPageResize'\n];\nfunction lifecycleMixin$1(Vue) {\n\n    //fixed vue-class-component\n    var oldExtend = Vue.extend;\n    Vue.extend = function(extendOptions) {\n        extendOptions = extendOptions || {};\n\n        var methods = extendOptions.methods;\n        if (methods) {\n            Object.keys(methods).forEach(function (methodName) {\n                if (LIFECYCLE_HOOKS$1.indexOf(methodName)!==-1) {\n                    extendOptions[methodName] = methods[methodName];\n                    delete methods[methodName];\n                }\n            });\n        }\n\n        return oldExtend.call(this, extendOptions)\n    };\n\n    var strategies = Vue.config.optionMergeStrategies;\n    var mergeHook = strategies.created;\n    LIFECYCLE_HOOKS$1.forEach(function (hook) {\n        strategies[hook] = mergeHook;\n    });\n\n    Vue.prototype.__lifecycle_hooks__ = LIFECYCLE_HOOKS$1;\n}\n\n/*  */\n\n// install platform patch function\nVue.prototype.__patch__ = patch;\n\n// public mount method\nVue.prototype.$mount = function(\n    el ,\n    hydrating \n) {\n    return mountComponent$1(this, el, hydrating)\n};\n\nlifecycleMixin$1(Vue);\ninternalMixin(Vue);\n\n/*  */\n\nexport default Vue;\n", "module.exports = require(\"regenerator-runtime\");\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n// This method of obtaining a reference to the global object needs to be\n// kept identical to the way it is obtained in runtime.js\nvar g = (function() {\n  return this || (typeof self === \"object\" && self);\n})() || Function(\"return this\")();\n\n// Use `getOwnPropertyNames` because not all browsers support calling\n// `hasOwnProperty` on the global `self` object in a worker. See #183.\nvar hadRuntime = g.regeneratorRuntime &&\n  Object.getOwnPropertyNames(g).indexOf(\"regeneratorRuntime\") >= 0;\n\n// Save the old regeneratorRuntime in case it needs to be restored later.\nvar oldRuntime = hadRuntime && g.regeneratorRuntime;\n\n// Force reevalutation of runtime.js.\ng.regeneratorRuntime = undefined;\n\nmodule.exports = require(\"./runtime\");\n\nif (hadRuntime) {\n  // Restore the original runtime.\n  g.regeneratorRuntime = oldRuntime;\n} else {\n  // Remove the global property added by runtime.js.\n  try {\n    delete g.regeneratorRuntime;\n  } catch(e) {\n    g.regeneratorRuntime = undefined;\n  }\n}\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n!(function(global) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  var inModule = typeof module === \"object\";\n  var runtime = global.regeneratorRuntime;\n  if (runtime) {\n    if (inModule) {\n      // If regeneratorRuntime is defined globally and we're in a module,\n      // make the exports object identical to regeneratorRuntime.\n      module.exports = runtime;\n    }\n    // Don't bother evaluating the rest of this file if the runtime was\n    // already defined globally.\n    return;\n  }\n\n  // Define the runtime globally (as expected by generated code) as either\n  // module.exports (if we're in a module) or a new, empty object.\n  runtime = global.regeneratorRuntime = inModule ? module.exports : {};\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  runtime.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  runtime.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  runtime.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  runtime.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return Promise.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return Promise.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new Promise(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  runtime.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  runtime.async = function(innerFn, outerFn, self, tryLocsList) {\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList)\n    );\n\n    return runtime.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        if (delegate.iterator.return) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  runtime.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  runtime.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n})(\n  // In sloppy mode, unbound `this` refers to the global object, fallback to\n  // Function constructor if we're in global strict mode. That is sadly a form\n  // of indirect eval which violates Content Security Policy.\n  (function() {\n    return this || (typeof self === \"object\" && self);\n  })() || Function(\"return this\")()\n);\n"], "sourceRoot": ""}