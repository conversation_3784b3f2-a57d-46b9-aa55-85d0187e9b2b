@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.follow-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}
.page-header .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 32rpx;
  border-radius: 50%;
  transition: all 0.2s;
}
.page-header .back-btn:active {
  background-color: #f5f5f5;
}
.page-header .title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.page-header .right-placeholder {
  width: 60rpx;
}
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #fff;
}
.search-bar .search-input {
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 25rpx;
}
.search-bar .search-input .fa-search {
  font-size: 28rpx;
  color: #999;
  margin-right: 15rpx;
}
.search-bar .search-input input {
  flex: 1;
  font-size: 28rpx;
  color: #999;
  /* 禁用状态文字颜色 */
  height: 100%;
}
.search-bar .search-input input:disabled {
  background-color: transparent;
}
.follow-list {
  padding: 0 30rpx 40rpx;
}
.follow-list .follow-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
}
.follow-list .avatar-wrap {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.follow-list .avatar-wrap .avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.follow-list .follow-info {
  flex: 1;
  min-width: 0;
}
.follow-list .follow-info .name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.follow-list .follow-info .desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.follow-list .follow-btn {
  min-width: 140rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border-radius: 32rpx;
  font-size: 28rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.follow-list .follow-btn.active {
  background-color: #ff4d4f;
  color: #fff;
}
.follow-list .follow-btn.active .btn-text {
  margin-right: 8rpx;
}
.follow-list .follow-btn .btn-icon {
  font-size: 24rpx;
}

