{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?cbbd", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?ed6c", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?5b55", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?6b70", "uni-app:///pages/setting/setting.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?92e2", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/setting/setting.vue?0895"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;kDAAA;;;AAGA;AACA,kG,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,gBAAD,CAAV,C;;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAorB,CAAgB,mqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyJxsB;AACA,MADA,kBACA;AACA;;;AAGA,GALA;AAMA;AACA;AACA,UAFA,oBAEA;AACA;AACA,gBADA;;AAGA,KANA,EANA,E;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA+xC,CAAgB,0rCAAG,EAAC,C;;;;;;;;;;;ACAnzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/setting/setting.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/setting/setting.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setting.vue?vue&type=template&id=9543fcb0&\"\nvar renderjs\nimport script from \"./setting.vue?vue&type=script&lang=js&\"\nexport * from \"./setting.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setting.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/setting/setting.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=template&id=9543fcb0&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"setting-page\">\n\t\t<!-- 页面标题栏 -->\n\t\t<view class=\"page-header\">\n\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t<text class=\"fa fa-arrow-left\"></text>\n\t\t\t</view>\n\t\t\t<view class=\"title\">设置</view>\n\t\t</view>\n\t\t\n\t\t<!-- 设置列表区域 -->\n\t\t<view class=\"setting-list\">\n\t\t\t<!-- 账号相关 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-title\">账号管理</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-user\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">个人信息</view>\n\t\t\t\t\t\t<view class=\"item-desc\">编辑昵称、签名等信息</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-lock\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">账号安全</view>\n\t\t\t\t\t\t<view class=\"item-desc\">修改密码、绑定手机</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 播放设置 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-title\">播放设置</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-play-circle\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">播放清晰度</view>\n\t\t\t\t\t\t<view class=\"item-desc\">默认高清</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-volume-up\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">音效设置</view>\n\t\t\t\t\t\t<view class=\"item-desc\">标准音效</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-repeat\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">播放模式</view>\n\t\t\t\t\t\t<view class=\"item-desc\">列表循环</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 通用设置 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-title\">通用设置</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-moon-o\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">深色模式</view>\n\t\t\t\t\t\t<view class=\"item-desc\">跟随系统</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-bell-o\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">通知设置</view>\n\t\t\t\t\t\t<view class=\"item-desc\">新视频通知、关注动态</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 关于相关 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-title\">关于我们</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-info-circle\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">关于视频</view>\n\t\t\t\t\t\t<view class=\"item-desc\">版本号 v2.3.0</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"fa fa-question-circle\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-content\">\n\t\t\t\t\t\t<view class=\"item-title\">帮助与反馈</view>\n\t\t\t\t\t\t<view class=\"item-desc\">常见问题、意见反馈</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-arrow\">\n\t\t\t\t\t\t<text class=\"fa fa-angle-right\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t};\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.setting-page {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tfont-family: -apple-system, BlinkMacSystemFont, sans-serif;\n\t}\n\t\n\t// 页面标题栏\n\t.page-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 25rpx 30rpx;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1rpx solid #eee;\n\t\t\n\t\t.back-btn {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tcolor: #333;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\t\t\n\t\t.title {\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 34rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\t\n\t// 设置列表区域\n\t.setting-list {\n\t\tpadding: 20rpx 0;\n\t}\n\t\n\t// 分组标题\n\t.section-title {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground-color: #f5f5f5;\n\t}\n\t\n\t// 设置项样式\n\t.setting-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 25rpx 30rpx;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\n\t\t// 图标区域\n\t\t.item-icon {\n\t\t\twidth: 50rpx;\n\t\t\theight: 50rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tcolor: #666;\n\t\t\tfont-size: 30rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\t\t\n\t\t// 内容区域\n\t\t.item-content {\n\t\t\tflex: 1;\n\t\t\t\n\t\t\t.item-title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tmargin-bottom: 5rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.item-desc {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 箭头区域\n\t\t.item-arrow {\n\t\t\tcolor: #ccc;\n\t\t\tfont-size: 30rpx;\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753861137251\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}