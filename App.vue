<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url("./static/iconfont.css");

	/* 全局样式重置 */
	page {
		background-color: #f8f9fa;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
		line-height: 1.6;
	}

	/* 通用类 */
	.container {
		padding: 20rpx;
	}

	.text-center {
		text-align: center;
	}

	.flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.flex-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	/* 文字省略 */
	.text-ellipsis {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.text-ellipsis-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* 动画类 */
	.fade-in {
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.slide-up {
		animation: slideUp 0.3s ease-out;
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	/* 按钮样式 */
	.btn {
		padding: 16rpx 32rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 500;
		text-align: center;
		transition: all 0.2s ease;
		border: none;
		outline: none;
	}

	.btn-primary {
		background: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);
		color: white;
	}

	.btn-secondary {
		background: #f8f9fa;
		color: #6c757d;
		border: 1rpx solid #dee2e6;
	}

	.btn:active {
		transform: scale(0.98);
	}

	/* 卡片样式 */
	.card {
		background: white;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
		overflow: hidden;
	}

	/* 分割线 */
	.divider {
		height: 1rpx;
		background: #e9ecef;
		margin: 20rpx 0;
	}

	/* 安全区域 */
	.safe-area-top {
		height: constant(safe-area-inset-top);
		height: env(safe-area-inset-top);
	}

	.safe-area-bottom {
		height: constant(safe-area-inset-bottom);
		height: env(safe-area-inset-bottom);
	}
</style>
