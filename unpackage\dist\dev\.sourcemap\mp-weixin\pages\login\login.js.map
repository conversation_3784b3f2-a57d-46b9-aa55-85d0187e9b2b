{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?29a9", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?fb26", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?5ff2", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?515b", "uni-app:///pages/login/login.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?2842", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/login/login.vue?70bf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;;;kDAAA;;;AAGA;AACA,4F,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,cAAD,CAAV,C;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkrB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgEtsB;AACA,MADA,kBACA;AACA;AACA,qBADA;AAEA,qBAFA;AAGA,sBAHA;AAIA,qBAJA;AAKA,qBALA;;AAOA,GATA;AAUA;AACA,eADA,yBACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,yBADA;AAEA,oBAFA;;;AAKA;AACA;AACA;AACA;;AAEA;AACA,yBADA;AAEA,2BAFA;;;AAKA;AACA;;AAEA;AACA;AACA,qCADA;;AAGA,SAhBA,EAgBA,IAhBA;AAiBA,OA1BA,MA0BA;AACA;AACA;AACA;AACA,KAhDA,EAVA,E;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA6xC,CAAgB,wrCAAG,EAAC,C;;;;;;;;;;ACAjzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 顶部Logo -->\n    <view class=\"logo-container\">\n      <image src=\"../../static/ddy.jpeg\" class=\"logo\"></image>\n      <text class=\"app-name\">登录助手</text>\n      <text class=\"app-desc\">安全便捷的登录体验</text>\n    </view>\n\n    <!-- 登录表单 -->\n    <view class=\"form-container\">\n      <view class=\"input-group\">\n        <view class=\"input-icon\">\n          <text class=\"fa fa-user\"></text>\n        </view>\n        <input \n          type=\"text\" \n          class=\"input-field\" \n          placeholder=\"请输入账号\" \n          v-model=\"username\"\n          placeholder-class=\"placeholder-style\"\n        />\n      </view>\n      \n      <view class=\"input-group\">\n        <view class=\"input-icon\">\n          <text class=\"fa fa-lock\"></text>\n        </view>\n        <input \n          type=\"password\" \n          class=\"input-field\" \n          placeholder=\"请输入密码\" \n          v-model=\"password\"\n          placeholder-class=\"placeholder-style\"\n        />\n      </view>\n      \n      <view class=\"form-actions\">\n        <checkbox-group class=\"remember-me\">\n          <label>\n            <checkbox value=\"remember\" checked color=\"#6B7280\" />\n            <text>记住密码</text>\n          </label>\n        </checkbox-group>\n        <text class=\"forgot-password\">忘记密码?</text>\n      </view>\n      \n      <button class=\"login-btn\" @click=\"handleLogin\" :disabled=\"isLoading\">\n        {{ isLoading ? '登录中...' : '登录' }}\n      </button>\n      \n      <view class=\"register-container\">\n        <text>还没有账号?</text>\n        <navigator url=\"/pages/register/register\" class=\"register-link\">立即注册</navigator>\n      </view>\n      \n      <view class=\"tips\" v-if=\"showTips\">\n        <text class=\"tips-text\">{{ tipsMessage }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      username: '123',\n      password: '123',\n      isLoading: false,\n      showTips: false,\n      tipsMessage: ''\n    };\n  },\n  methods: {\n    handleLogin() {\n      this.showTips = false;\n      \n      // 表单验证\n      if (!this.username) {\n        this.showTips = true;\n        this.tipsMessage = '请输入账号';\n        return;\n      }\n      \n      if (!this.password) {\n        this.showTips = true;\n        this.tipsMessage = '请输入密码';\n        return;\n      }\n      \n      // 模拟登录验证\n      if (this.username === '123' && this.password === '123') {\n        this.isLoading = true;\n        \n        uni.showLoading({\n          title: '登录中...',\n          mask: true\n        });\n        \n        // 模拟登录请求\n        setTimeout(() => {\n          this.isLoading = false;\n          uni.hideLoading();\n          \n          uni.showToast({\n            title: '登录成功',\n            icon: 'success'\n          });\n          \n          // 存储登录状态\n          uni.setStorageSync('isLoggedIn', true);\n          \n          // 跳转到首页\n          uni.redirectTo({\n            url: '/pages/index/index'\n          });\n        }, 1500);\n      } else {\n        this.showTips = true;\n        this.tipsMessage = '账号或密码错误';\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.container {\n  padding: 120rpx 60rpx 0;\n  background: linear-gradient(135deg, #E6F7FF 0%, #F3F3FF 100%);\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  backdrop-filter: blur(5px); /* 背景模糊效果 */\n}\n\n.logo-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 120rpx;\n  \n  .logo {\n    width: 220rpx;\n    height: 220rpx;\n    border-radius: 24rpx;\n    margin-bottom: 40rpx;\n    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease; /* 过渡效果 */\n    \n    &:hover {\n      transform: scale(1.1); /* 悬停放大效果 */\n    }\n  }\n  \n  .app-name {\n    font-size: 52rpx;\n    font-weight: bold;\n    color: #4B5563;\n    text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1); /* 文字阴影 */\n  }\n  \n  .app-desc {\n    margin-top: 20rpx;\n    font-size: 28rpx;\n    color: #6B7280;\n  }\n}\n\n.form-container {\n  background-color: rgba(255, 255, 255, 0.8); /* 半透明背景 */\n  border-radius: 30rpx;\n  padding: 60rpx 50rpx;\n  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.08);\n  margin-bottom: 80rpx;\n  transition: transform 0.3s ease; /* 过渡效果 */\n  \n  &:hover {\n    transform: translateY(-10rpx); /* 悬停上移效果 */\n  }\n  \n  .input-group {\n    display: flex;\n    align-items: center;\n    padding: 25rpx 0;\n    border-bottom: 1rpx solid #F3F4F6;\n    \n    .input-icon {\n      width: 60rpx;\n      text-align: center;\n      color: #9CA3AF;\n      font-size: 36rpx;\n      margin-right: 20rpx;\n    }\n    \n    .input-field {\n      flex: 1;\n      font-size: 32rpx;\n      height: 60rpx;\n      color: #374151;\n      border: none;\n      background-color: transparent;\n      outline: none;\n      \n      &::placeholder {\n        color: #D1D5DB;\n      }\n    }\n  }\n  \n  .form-actions {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 40rpx;\n    \n    .remember-me {\n      display: flex;\n      align-items: center;\n      \n      checkbox {\n        transform: scale(0.8);\n      }\n      \n      text {\n        font-size: 28rpx;\n        color: #6B7280;\n        margin-left: 10rpx;\n      }\n    }\n    \n    .forgot-password {\n      font-size: 28rpx;\n      color: #4F46E5;\n      text-decoration: underline; /* 下划线效果 */\n      \n      &:hover {\n        color: #3730a3; /* 悬停颜色变化 */\n      }\n    }\n  }\n  \n  .login-btn {\n    margin-top: 60rpx;\n    height: 96rpx;\n    line-height: 96rpx;\n    background: linear-gradient(90deg, #4F46E5 0%, #6366F1 100%);\n    color: #FFFFFF;\n    font-size: 36rpx;\n    border-radius: 48rpx;\n    font-weight: 500;\n    box-shadow: 0 10rpx 20rpx rgba(79, 70, 229, 0.2);\n    transition: all 0.3s;\n    \n    &:hover {\n      background: linear-gradient(90deg, #3730a3 0%, #4338ca 100%); /* 悬停背景渐变 */\n      transform: scale(1.05); /* 悬停放大效果 */\n    }\n    \n    &[disabled] {\n      opacity: 0.7;\n      cursor: not-allowed;\n    }\n  }\n  \n  .register-container {\n    display: flex;\n    justify-content: center;\n    margin-top: 40rpx;\n    font-size: 28rpx;\n    \n    text {\n      color: #6B7280;\n    }\n    \n    .register-link {\n      color: #4F46E5;\n      margin-left: 10rpx;\n      text-decoration: underline; /* 下划线效果 */\n      \n      &:hover {\n        color: #3730a3; /* 悬停颜色变化 */\n      }\n    }\n  }\n  \n  .tips {\n    margin-top: 30rpx;\n    text-align: center;\n  }\n  \n  .tips-text {\n    color: #EF4444;\n    font-size: 28rpx;\n  }\n}\n\n.placeholder-style {\n  color: #D1D5DB;\n  font-size: 32rpx;\n}\n</style>    ", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753861137198\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}