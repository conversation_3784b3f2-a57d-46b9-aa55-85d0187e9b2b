<template>
	<view class="user-detail-page">
		<!-- 顶部背景区域 -->
		<view class="user-header-bg">
			<!-- 返回按钮 -->
			<view class="back-btn" @click="goBack">
				<text class="fa fa-arrow-left"></text>
			</view>
		</view>
		
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<!-- 头像区域 -->
			<view class="avatar-wrap">
				<image 
					src="../../static/tx.jpeg"
					mode="widthFix" 
					class="avatar-img"
				/>
				<!-- 编辑头像按钮 -->
				<view class="edit-avatar-btn">
					<text class="fa fa-pencil"></text>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="user-basic">
				<view class="user-name">小杰</view>
				<view class="user-sign">热爱生活的视频创作者 | 已加入2年</view>
			</view>
			
			<!-- 数据统计 -->
			<view class="user-stats">
				<view class="stat-item" @click="goToMyVideos">
					<view class="stat-num">24</view>
					<view class="stat-label">我的视频</view>
				</view>
				<view class="stat-item" @click="goToFavorites">
					<view class="stat-num">156</view>
					<view class="stat-label">收藏</view>
				</view>
				<view class="stat-item" @click="goToFans">
					<view class="stat-num">3.2k</view>
					<view class="stat-label">粉丝</view>
				</view>
				<view class="stat-item" @click="goToFollowing">
					<view class="stat-num">89</view>
					<view class="stat-label">关注</view>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="user-menu">
			<view class="menu-item" @click="goToSetting">
				<text class="menu-icon fa fa-cog"></text>
				<text class="menu-text">设置</text>
				<text class="menu-arrow fa fa-angle-right"></text>
			</view>
			<view class="menu-item" @click="goToHelp">
				<text class="menu-icon fa fa-question-circle"></text>
				<text class="menu-text">帮助中心</text>
				<text class="menu-arrow fa fa-angle-right"></text>
			</view>
			<view class="menu-item" @click="logout">
				<text class="menu-icon fa fa-sign-out"></text>
				<text class="menu-text" style="color: #ff4d4f;">退出登录</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可在此处存储用户数据，实际项目中可从接口获取
				userInfo: {
					name: '小杰',
					avatar: '/static/images/user-avatar.png',
					signature: '热爱生活的视频创作者',
					joinTime: '2年',
					stats: {
						videos: 24,
						favorites: 156,
						fans: 3200,
						following: 89
					}
				}
			};
		},
		onLoad() {
			// 实际项目中可在此处请求用户详情接口
			// this.getUserDetail()
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			// 跳转我的视频
			goToMyVideos() {
				uni.navigateTo({
					url: '/pages/my-videos/my-videos'
				})
			},
			// 跳转收藏
			goToFavorites() {
				uni.navigateTo({
					url: '/pages/favorites/favorites'
				})
			},
			// 跳转粉丝列表
			goToFans() {
				uni.navigateTo({
					url: '/pages/fans/fans'
				})
			},
			// 跳转关注列表
			goToFollowing() {
				uni.navigateTo({
					url: '/pages/following/following'
				})
			},
			// 跳转设置页面
			goToSetting() {
				uni.navigateTo({
					url: '/pages/setting/setting'
				})
			},
			// 跳转帮助中心
			goToHelp() {
				uni.navigateTo({
					url: '/pages/help/help'
				})
			},
			// 退出登录
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: res => {
						if (res.confirm) {
							// 执行退出登录逻辑
							uni.reLaunch({
								url: '/pages/login/login'
							})
						}
					}
				})
			},
			// 获取用户详情（示例接口方法）
			getUserDetail() {
				uni.request({
					url: config.url + '/user/detail',
					success: res => {
						this.userInfo = res.data.data
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.user-detail-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, sans-serif;
	}
	
	// 顶部背景
	.user-header-bg {
		height: 220rpx;
		background: linear-gradient(135deg, #4a90e2, #5c6bc0);
		position: relative;
		
		.back-btn {
			position: absolute;
			left: 30rpx;
			top: 60rpx;
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
			background-color: rgba(0,0,0,0.2);
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
		}
	}
	
	// 用户卡片
	.user-card {
		width: 90%;
		margin: -80rpx auto 0;
		background-color: #fff;
		border-radius: 20rpx;
		box-shadow: 0 5rpx 20rpx rgba(0,0,0,0.1);
		padding: 40rpx 0 30rpx;
		text-align: center;
		
		// 头像区域
		.avatar-wrap {
			position: relative;
			width: 160rpx;
			height: 160rpx;
			margin: 0 auto 30rpx;
			
			.avatar-img {
				width: 100%;
				height: 100%;
				border-radius: 50%;
				border: 5rpx solid #fff;
			}
			
			.edit-avatar-btn {
				position: absolute;
				right: 0;
				bottom: 0;
				width: 40rpx;
				height: 40rpx;
				background-color: #4a90e2;
				border-radius: 50%;
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 20rpx;
				border: 2rpx solid #fff;
			}
		}
		
		// 基本信息
		.user-basic {
			margin-bottom: 30rpx;
			
			.user-name {
				font-size: 36rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.user-sign {
				font-size: 26rpx;
				color: #666;
				line-height: 1.5;
			}
		}
		
		// 数据统计
		.user-stats {
			display: flex;
			justify-content: space-around;
			padding: 0 20rpx;
			
			.stat-item {
				flex: 1;
				padding: 10rpx 0;
				
				.stat-num {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 5rpx;
				}
				
				.stat-label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	// 功能菜单
	.user-menu {
		width: 90%;
		margin: 30rpx auto;
		background-color: #fff;
		border-radius: 20rpx;
		box-shadow: 0 5rpx 20rpx rgba(0,0,0,0.1);
		
		.menu-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.menu-icon {
				font-size: 32rpx;
				color: #4a90e2;
				margin-right: 20rpx;
				width: 32rpx; // 固定宽度避免文字偏移
				text-align: center;
			}
			
			.menu-text {
				flex: 1;
				font-size: 30rpx;
				color: #333;
			}
			
			.menu-arrow {
				font-size: 30rpx;
				color: #ccc;
			}
		}
	}
</style>