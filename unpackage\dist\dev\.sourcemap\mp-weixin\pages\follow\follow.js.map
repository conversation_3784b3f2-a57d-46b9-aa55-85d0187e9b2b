{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?a3d4", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?c730", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?975a", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?a961", "uni-app:///pages/follow/follow.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?6a7e", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/follow/follow.vue?25e8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;kDAAA;;;AAGA;AACA,+F,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,eAAD,CAAV,C;;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmrB,CAAgB,kqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiDvsB;AACA,MADA,kBACA;AACA;AACA;AACA;AACA;AACA,aADA;AAEA,mBAFA;AAGA,8BAHA;AAIA,iCAJA;AAKA,yBALA,EADA;;AAQA;AACA,aADA;AAEA,mBAFA;AAGA,6BAHA;AAIA,iCAJA;AAKA,yBALA,EARA;;AAeA;AACA,aADA;AAEA,mBAFA;AAGA,6BAHA;AAIA,kCAJA;AAKA,yBALA,EAfA;;AAsBA;AACA,aADA;AAEA,mBAFA;AAGA,8BAHA;AAIA,kCAJA;AAKA,yBALA,EAtBA;;AA6BA;AACA,aADA;AAEA,oBAFA;AAGA,mCAHA;AAIA,mCAJA;AAKA,yBALA,EA7BA,CAFA;;;;AAwCA,GA1CA;;AA4CA;AACA;AACA,UAFA,oBAEA;AACA;AACA,gBADA;;AAGA,KANA;;AAQA;AACA,kBATA,0BASA,EATA,EASA;AACA;AACA,4DADA;;AAGA,KAbA;;AAeA;AACA,gBAhBA,wBAgBA,KAhBA,EAgBA;AACA;AACA;;AAEA;AACA;AACA,oDADA;AAEA,oBAFA;AAGA,sBAHA;;AAKA,KA1BA,EA5CA,E;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,yrCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/follow/follow.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/follow/follow.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./follow.vue?vue&type=template&id=2f62b6b4&\"\nvar renderjs\nimport script from \"./follow.vue?vue&type=script&lang=js&\"\nexport * from \"./follow.vue?vue&type=script&lang=js&\"\nimport style0 from \"./follow.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/follow/follow.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./follow.vue?vue&type=template&id=2f62b6b4&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./follow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./follow.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"follow-page\">\n    <!-- 顶部导航栏 -->\n    <view class=\"page-header\">\n      <view class=\"back-btn\" @click=\"goBack\">\n        <text class=\"fa fa-arrow-left\"></text>\n      </view>\n      <view class=\"title\">我的关注</view>\n      <view class=\"right-placeholder\"></view>\n    </view>\n    \n    <!-- 搜索栏（仅展示） -->\n    <view class=\"search-bar\">\n      <view class=\"search-input\">\n        <text class=\"fa fa-search\"></text>\n        <input placeholder=\"搜索关注\" disabled />\n      </view>\n    </view>\n    \n    <!-- 关注列表区域 -->\n    <view class=\"follow-list\">\n      <!-- 关注人员列表项 -->\n      <view class=\"follow-item\" v-for=\"(person, index) in followList\" :key=\"person.id || index\">\n        <!-- 头像区域 -->\n        <view class=\"avatar-wrap\" @click=\"goToStarDetail(person.id || index)\">\n          <image \n            :src=\"person.avatar\" \n            mode=\"widthFix\" \n            class=\"avatar-img\"\n          />\n        </view>\n        \n        <!-- 名称及操作区域 -->\n        <view class=\"follow-info\">\n          <view class=\"name\">{{ person.name }}</view>\n          <view class=\"desc\">{{ person.desc }}</view>\n        </view>\n        \n        <!-- 已关注/取消关注按钮 -->\n        <view class=\"follow-btn\" :class=\"{ active: person.isFollowing }\" @click=\"toggleFollow(index)\">\n          <text class=\"btn-text\">{{ person.isFollowing ? '已关注' : '关注' }}</text>\n          <text class=\"btn-icon fa\" :class=\"person.isFollowing ? 'fa-check' : 'fa-plus'\"></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      // 关注列表数据\n      followList: [\n        {\n          id: 1,\n          name: '田曦薇',\n          desc: '演员 | 代表作《卿卿日常》',\n          avatar: '/static/txw.jpg',\n          isFollowing: true\n        },\n        {\n          id: 2,\n          name: '张凌赫',\n          desc: '演员 | 代表作《苍兰诀》',\n          avatar: '/static/zlh.jpg',\n          isFollowing: true\n        },\n        {\n          id: 3,\n          name: '卢昱晓',\n          desc: '演员 | 代表作《玉骨遥》',\n          avatar: '/static/lyx.jpeg',\n          isFollowing: true\n        },\n        {\n          id: 4,\n          name: '王鹤棣',\n          desc: '演员 | 代表作《流星花园》',\n          avatar: '/static/whd.jpeg',\n          isFollowing: true\n        },\n        {\n          id: 5,\n          name: '欧阳娜娜',\n          desc: '演员/歌手 | 代表作《北京爱情故事》',\n          avatar: '/static/oynn.jpeg',\n          isFollowing: true\n        }\n      ]\n    };\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack({\n        delta: 1\n      });\n    },\n    \n    // 跳转到明星详情页\n    goToStarDetail(id) {\n      uni.navigateTo({\n        url: `/pages/star-detail/star-detail?id=${id}`\n      });\n    },\n    \n    // 切换关注状态\n    toggleFollow(index) {\n      const person = this.followList[index];\n      person.isFollowing = !person.isFollowing;\n      \n      // 提示信息\n      uni.showToast({\n        title: person.isFollowing ? '关注成功' : '已取消关注',\n        icon: 'none',\n        duration: 1500\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.follow-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  font-family: -apple-system, BlinkMacSystemFont, sans-serif;\n}\n\n// 页面标题栏\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 30rpx 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);\n  \n  .back-btn {\n    width: 60rpx;\n    height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #333;\n    font-size: 32rpx;\n    border-radius: 50%;\n    transition: all 0.2s;\n    \n    &:active {\n      background-color: #f5f5f5;\n    }\n  }\n  \n  .title {\n    flex: 1;\n    text-align: center;\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #333;\n  }\n  \n  .right-placeholder {\n    width: 60rpx;\n  }\n}\n\n// 搜索栏（仅展示）\n.search-bar {\n  padding: 20rpx 30rpx;\n  background-color: #fff;\n  \n  .search-input {\n    display: flex;\n    align-items: center;\n    height: 70rpx;\n    background-color: #f5f5f5;\n    border-radius: 35rpx;\n    padding: 0 25rpx;\n    \n    .fa-search {\n      font-size: 28rpx;\n      color: #999;\n      margin-right: 15rpx;\n    }\n    \n    input {\n      flex: 1;\n      font-size: 28rpx;\n      color: #999; /* 禁用状态文字颜色 */\n      height: 100%;\n    }\n    \n    input:disabled {\n      background-color: transparent;\n    }\n  }\n}\n\n// 关注列表\n.follow-list {\n  padding: 0 30rpx 40rpx;\n  \n  .follow-item {\n    display: flex;\n    align-items: center;\n    background-color: #fff;\n    border-radius: 20rpx;\n    padding: 30rpx 25rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.05);\n  }\n  \n  // 头像样式\n  .avatar-wrap {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 50%;\n    overflow: hidden;\n    margin-right: 30rpx;\n    flex-shrink: 0;\n    \n    .avatar-img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n  }\n  \n  // 名称及描述\n  .follow-info {\n    flex: 1;\n    min-width: 0;\n    \n    .name {\n      font-size: 32rpx;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 8rpx;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n    \n    .desc {\n      font-size: 26rpx;\n      color: #666;\n      line-height: 1.4;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n  }\n  \n  // 关注按钮\n  .follow-btn {\n    min-width: 140rpx;\n    height: 64rpx;\n    line-height: 64rpx;\n    text-align: center;\n    border-radius: 32rpx;\n    font-size: 28rpx;\n    border: 2rpx solid #ff4d4f;\n    color: #ff4d4f;\n    margin-left: 20rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    &.active {\n      background-color: #ff4d4f;\n      color: #fff;\n      \n      .btn-text {\n        margin-right: 8rpx;\n      }\n    }\n    \n    .btn-icon {\n      font-size: 24rpx;\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./follow.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./follow.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753861137236\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}