<template>
	<view class="setting-page">
		<!-- 页面标题栏 -->
		<view class="page-header">
			<view class="back-btn" @click="goBack">
				<text class="fa fa-arrow-left"></text>
			</view>
			<view class="title">设置</view>
		</view>
		
		<!-- 设置列表区域 -->
		<view class="setting-list">
			<!-- 账号相关 -->
			<view class="setting-section">
				<view class="section-title">账号管理</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-user"></text>
					</view>
					<view class="item-content">
						<view class="item-title">个人信息</view>
						<view class="item-desc">编辑昵称、签名等信息</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-lock"></text>
					</view>
					<view class="item-content">
						<view class="item-title">账号安全</view>
						<view class="item-desc">修改密码、绑定手机</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
			</view>
			
			<!-- 播放设置 -->
			<view class="setting-section">
				<view class="section-title">播放设置</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-play-circle"></text>
					</view>
					<view class="item-content">
						<view class="item-title">播放清晰度</view>
						<view class="item-desc">默认高清</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-volume-up"></text>
					</view>
					<view class="item-content">
						<view class="item-title">音效设置</view>
						<view class="item-desc">标准音效</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-repeat"></text>
					</view>
					<view class="item-content">
						<view class="item-title">播放模式</view>
						<view class="item-desc">列表循环</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
			</view>
			
			<!-- 通用设置 -->
			<view class="setting-section">
				<view class="section-title">通用设置</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-moon-o"></text>
					</view>
					<view class="item-content">
						<view class="item-title">深色模式</view>
						<view class="item-desc">跟随系统</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-bell-o"></text>
					</view>
					<view class="item-content">
						<view class="item-title">通知设置</view>
						<view class="item-desc">新视频通知、关注动态</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
			</view>
			
			<!-- 关于相关 -->
			<view class="setting-section">
				<view class="section-title">关于我们</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-info-circle"></text>
					</view>
					<view class="item-content">
						<view class="item-title">关于视频</view>
						<view class="item-desc">版本号 v2.3.0</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-icon">
						<text class="fa fa-question-circle"></text>
					</view>
					<view class="item-content">
						<view class="item-title">帮助与反馈</view>
						<view class="item-desc">常见问题、意见反馈</view>
					</view>
					<view class="item-arrow">
						<text class="fa fa-angle-right"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style lang="scss">
	.setting-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, sans-serif;
	}
	
	// 页面标题栏
	.page-header {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #333;
			font-size: 32rpx;
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 34rpx;
			font-weight: 600;
			color: #333;
		}
	}
	
	// 设置列表区域
	.setting-list {
		padding: 20rpx 0;
	}
	
	// 分组标题
	.section-title {
		font-size: 24rpx;
		color: #999;
		padding: 20rpx 30rpx;
		background-color: #f5f5f5;
	}
	
	// 设置项样式
	.setting-item {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #f5f5f5;
		
		// 图标区域
		.item-icon {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #666;
			font-size: 30rpx;
			margin-right: 20rpx;
		}
		
		// 内容区域
		.item-content {
			flex: 1;
			
			.item-title {
				font-size: 30rpx;
				color: #333;
				margin-bottom: 5rpx;
			}
			
			.item-desc {
				font-size: 24rpx;
				color: #999;
			}
		}
		
		// 箭头区域
		.item-arrow {
			color: #ccc;
			font-size: 30rpx;
		}
	}
</style>