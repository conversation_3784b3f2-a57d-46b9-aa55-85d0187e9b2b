<template>
  <view class="follow-page">
    <!-- 顶部导航栏 -->
    <view class="page-header">
      <view class="back-btn" @click="goBack">
        <text class="fa fa-arrow-left"></text>
      </view>
      <view class="title">我的关注</view>
      <view class="right-placeholder"></view>
    </view>
    
    <!-- 搜索栏（仅展示） -->
    <view class="search-bar">
      <view class="search-input">
        <text class="fa fa-search"></text>
        <input placeholder="搜索关注" disabled />
      </view>
    </view>
    
    <!-- 关注列表区域 -->
    <view class="follow-list">
      <!-- 关注人员列表项 -->
      <view class="follow-item" v-for="(person, index) in followList" :key="person.id || index">
        <!-- 头像区域 -->
        <view class="avatar-wrap" @click="goToStarDetail(person.id || index)">
          <image 
            :src="person.avatar" 
            mode="widthFix" 
            class="avatar-img"
          />
        </view>
        
        <!-- 名称及操作区域 -->
        <view class="follow-info">
          <view class="name">{{ person.name }}</view>
          <view class="desc">{{ person.desc }}</view>
        </view>
        
        <!-- 已关注/取消关注按钮 -->
        <view class="follow-btn" :class="{ active: person.isFollowing }" @click="toggleFollow(index)">
          <text class="btn-text">{{ person.isFollowing ? '已关注' : '关注' }}</text>
          <text class="btn-icon fa" :class="person.isFollowing ? 'fa-check' : 'fa-plus'"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 关注列表数据
      followList: [
        {
          id: 1,
          name: '田曦薇',
          desc: '演员 | 代表作《卿卿日常》',
          avatar: '/static/txw.jpg',
          isFollowing: true
        },
        {
          id: 2,
          name: '张凌赫',
          desc: '演员 | 代表作《苍兰诀》',
          avatar: '/static/zlh.jpg',
          isFollowing: true
        },
        {
          id: 3,
          name: '卢昱晓',
          desc: '演员 | 代表作《玉骨遥》',
          avatar: '/static/lyx.jpeg',
          isFollowing: true
        },
        {
          id: 4,
          name: '王鹤棣',
          desc: '演员 | 代表作《流星花园》',
          avatar: '/static/whd.jpeg',
          isFollowing: true
        },
        {
          id: 5,
          name: '欧阳娜娜',
          desc: '演员/歌手 | 代表作《北京爱情故事》',
          avatar: '/static/oynn.jpeg',
          isFollowing: true
        }
      ]
    };
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    
    // 跳转到明星详情页
    goToStarDetail(id) {
      uni.navigateTo({
        url: `/pages/star-detail/star-detail?id=${id}`
      });
    },
    
    // 切换关注状态
    toggleFollow(index) {
      const person = this.followList[index];
      person.isFollowing = !person.isFollowing;
      
      // 提示信息
      uni.showToast({
        title: person.isFollowing ? '关注成功' : '已取消关注',
        icon: 'none',
        duration: 1500
      });
    }
  }
}
</script>

<style lang="scss">
.follow-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

// 页面标题栏
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
  
  .back-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 32rpx;
    border-radius: 50%;
    transition: all 0.2s;
    
    &:active {
      background-color: #f5f5f5;
    }
  }
  
  .title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
  
  .right-placeholder {
    width: 60rpx;
  }
}

// 搜索栏（仅展示）
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #fff;
  
  .search-input {
    display: flex;
    align-items: center;
    height: 70rpx;
    background-color: #f5f5f5;
    border-radius: 35rpx;
    padding: 0 25rpx;
    
    .fa-search {
      font-size: 28rpx;
      color: #999;
      margin-right: 15rpx;
    }
    
    input {
      flex: 1;
      font-size: 28rpx;
      color: #999; /* 禁用状态文字颜色 */
      height: 100%;
    }
    
    input:disabled {
      background-color: transparent;
    }
  }
}

// 关注列表
.follow-list {
  padding: 0 30rpx 40rpx;
  
  .follow-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx 25rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.05);
  }
  
  // 头像样式
  .avatar-wrap {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 30rpx;
    flex-shrink: 0;
    
    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  // 名称及描述
  .follow-info {
    flex: 1;
    min-width: 0;
    
    .name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  
  // 关注按钮
  .follow-btn {
    min-width: 140rpx;
    height: 64rpx;
    line-height: 64rpx;
    text-align: center;
    border-radius: 32rpx;
    font-size: 28rpx;
    border: 2rpx solid #ff4d4f;
    color: #ff4d4f;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.active {
      background-color: #ff4d4f;
      color: #fff;
      
      .btn-text {
        margin-right: 8rpx;
      }
    }
    
    .btn-icon {
      font-size: 24rpx;
    }
  }
}
</style>