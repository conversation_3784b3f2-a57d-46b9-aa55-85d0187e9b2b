<view><my-title vue-id="3db16520-1" bind:__l="__l"></my-title><block wx:if="{{videoInfo}}"><view class="video-info"><video class="video" src="{{videoInfo.videoSrc}}" controls="{{true}}"></video><view class="video-title"><text>{{videoInfo.videoTitle}}</text><text class="fa fa-angle-down"></text></view><view class="video-detail"><text class="author">{{videoInfo.author}}</text><text class="play-count">{{videoInfo.playCount}}</text><text class="comment-count">{{videoInfo.commentCount+"弹幕"}}</text><text class="date">{{"时间："+videoInfo.date}}</text></view></view></block><view class="other-list"><block wx:for="{{othersList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="item_other"><view class="other-img-wrap"><image src="{{item.imgSrc}}" mode="widthFix"></image></view><view class="other-info"><view class="other-title">{{item.title}}</view><view class="other-detail"><text class="play-count">{{item.playMsg+"次观看"}}</text><text class="comment-count">{{item.commentCount}}</text></view></view></view></block></view><view class="comment-wrap"><view class="comment-title">{{'评论 ('+commentData.commentTotalCount+')'}}</view><view class="comment-list"><block wx:for="{{commentData.commentList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="comment-item"><view class="comment_user"><image class="comment_user_image" src="{{item.userIconSrc}}" mode="widthFix"></image></view><view class="comment-info"><view class="comment-detail"><text class="author">{{item.username}}</text><text class="data">{{item.commentData}}</text></view><view class="comment-content">{{''+item.commentInfo+''}}</view></view></view></block></view></view></view>