@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.video-info {
  margin-top: 10rpx;
}
.video-info .video {
  width: 100%;
}
.video-info .video-title {
  display: flex;
  justify-content: space-between;
  font-size: 35rpx;
}
.video-info .video-detail {
  margin-top: 5rpx;
  font-size: 28rpx;
}
.video-info .video-detail text {
  margin-left: 15rpx;
}
.video-info .video-detail .author {
  color: #000;
}
.other-list {
  margin-top: 10rpx;
}
.other-list .item_other {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.other-list .item_other .other-img-wrap {
  width: 38%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.other-list .item_other .other-img-wrap .other-img-wrap image {
  width: 90%;
  border-radius: 15rpx;
}
.other-list .item_other .other-info {
  width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.other-list .item_other .other-info .other-title {
  font-size: 30rpx;
  color: #e06f93;
}
.other-list .item_other .other-info .other-detail {
  font-size: 26rpx;
  color: #666;
}
.comment-wrap {
  margin-top: 10rpx;
}
.comment-wrap .comment-title {
  padding: 10rpx;
  font-size: 28rpx;
}
.comment-wrap .comment-list .comment-item {
  margin-bottom: 10rpx;
  padding: 10rpx;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #666;
}
.comment-wrap .comment-list .comment-item .comment_user {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.comment-wrap .comment-list .comment-item .comment_user .comment_user_image {
  width: 60%;
  border-radius: 50%;
}
.comment-wrap .comment-list .comment-item .comment-info {
  flex: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.comment-wrap .comment-list .comment-item .comment-info .comment-detail {
  display: flex;
  justify-content: space-between;
}
.comment-wrap .comment-list .comment-item .comment-info .comment-detail .comment-detail .author {
  font-size: 28rpx;
  color: #000;
}
.comment-wrap .comment-list .comment-item .comment-info .comment-detail .comment-detail .date {
  color: #666;
  font-size: 24rpx;
}
.comment-wrap .comment-content {
  font-size: 28rpx;
  color: #000;
}

