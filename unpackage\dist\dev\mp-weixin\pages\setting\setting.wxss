@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.setting-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}
.page-header {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.page-header .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 32rpx;
}
.page-header .title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}
.setting-list {
  padding: 20rpx 0;
}
.section-title {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}
.setting-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}
.setting-item .item-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 30rpx;
  margin-right: 20rpx;
}
.setting-item .item-content {
  flex: 1;
}
.setting-item .item-content .item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 5rpx;
}
.setting-item .item-content .item-desc {
  font-size: 24rpx;
  color: #999;
}
.setting-item .item-arrow {
  color: #ccc;
  font-size: 30rpx;
}

