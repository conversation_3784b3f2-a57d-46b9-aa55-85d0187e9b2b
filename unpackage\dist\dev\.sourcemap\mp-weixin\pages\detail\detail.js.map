{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?803f", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?42f0", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?bb95", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?87e4", "uni-app:///pages/detail/detail.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?5364", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/detail/detail.vue?57a9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;kDAAA;;;AAGA;AACA,+F,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,eAAD,CAAV,C;;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iLAEN;AACP;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmrB,CAAgB,kqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkEvsB,4F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA,EACA,IADA,kBACA,CACA,SACA,eADA,EAEA,cAFA,EAGA,eAHA,GAKA,CAPA,EAQA,MARA,kBAQA,OARA,EAQA,CACA;AACA;AACA,6BACA,8BACA,qBACA,6BACA,CAfA,EAgBA,WACA;AACA,mBAFA,2BAEA,OAFA,EAEA,mBACA;AACA,oBACA,uDADA,EAEA,gCACA;AACA;AACA,oCACA,0CACA,CACA,CARA,IAUA,CAdA,EAeA,aAfA,2BAeA,mBACA,cACA,wCADA,EAEA,gCACA,0BACA,6CACA,CACA,CANA,IAQA,CAxBA,EAyBA,cAzBA,0BAyBA,OAzBA,EAyBA,mBACA,cACA,uDADA,EAEA,gCACA,0BACA,+CACA,CACA,CANA,IAQA,CAlCA,EAhBA,E;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,yrCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/detail/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/detail/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=3e159eb4&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/detail/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=3e159eb4&\"", "var components\ntry {\n  components = {\n    MyTitle: function() {\n      return import(\n        /* webpackChunkName: \"components/MyTitle/MyTitle\" */ \"@/components/MyTitle/MyTitle.vue\"\n      )\n    }\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n<view>\n<MyTitle></MyTitle>\n<!-- 视频详情 -->\n<view class=\"video-info\" v-if=\"videoInfo\">\n  <!-- 视频 -->\n  <video :src=\"videoInfo.videoSrc\" controls class=\"video\"></video>\n  <!-- 视频标题 -->\n  <view class=\"video-title\">\n    <text>{{ videoInfo.videoTitle }}</text>\n    <text class=\"fa fa-angle-down\"></text>\n  </view>\n  <!-- 视频详细信息 -->\n  <view class=\"video-detail\">\n    <!-- 作者 -->\n    <text class=\"author\">{{ videoInfo.author }}</text>\n    <!-- 播放量 -->\n    <text class=\"play-count\">{{ videoInfo.playCount }}</text>\n    <!-- 评论量 -->\n    <text class=\"comment-count\">{{ videoInfo.commentCount }}弹幕</text>\n    <!-- 时间 -->\n    <text class=\"date\">时间：{{ videoInfo.date }}</text>\n  </view>\n</view>\n<!-- 推荐视频 -->\n<view class=\"other-list\">\n\t<view v-for=\"item in othersList\" :key=\"item.id\" class=\"item_other\">\n\t\t<view class=\"other-img-wrap\">\n\t\t\t<image :src=\"item.imgSrc\" mode=\"widthFix\"/>\n\t\t</view>\n\t\t<view class=\"other-info\">\n\t\t\t<view class=\"other-title\">{{ item.title }}</view>\n\t\t\t<view class=\"other-detail\">\n\t\t\t\t<text class=\"play-count\">{{ item.playMsg }}次观看</text>\n\t\t\t\t<text class=\"comment-count\">{{ item.commentCount }}</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</view>\n<!-- 评论列表 -->\n<view class=\"comment-wrap\">\n\t<view class=\"comment-title\">\n\t\t评论 ({{ commentData.commentTotalCount }})\n\t</view>\n\t<view class=\"comment-list\">\n\t\t<view class=\"comment-item\" v-for=\"(item, index) in commentData.commentList\" :key=\"index\">\n\t\t\t<view class=\"comment_user\">\n\t\t\t\t<image :src=\"item.userIconSrc\" mode=\"widthFix\" class=\"comment_user_image\"/>\n\t\t\t</view>\n\t\t\t<view class=\"comment-info\">\n\t\t\t\t<view class=\"comment-detail\">\n\t\t\t\t\t<text class=\"author\">{{ item.username }}</text>\n\t\t\t\t\t<text class=\"data\">{{ item.commentData }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"comment-content\">\n\t\t\t\t\t{{ item.commentInfo }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</view>\n\n</view>\n</template>\n\n<script>\n\timport config from '../../common/config.js'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tvideoInfo: null,\n\t\t\t\tothersList: [],\n\t\t\t\tcommentData: [],\n\t\t\t};\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// console.log(options);\n\t\t\t// console.log(\"视频详情id===\"+options.id);\n\t\t\tlet videoId = options.id\n\t\t\tthis.getCurrentVideo(videoId)\n\t\t\tthis.getOthersList()\n\t\t\tthis.getCommentList(videoId)\n\t\t},\n\t\tmethods: {\n\t\t  // 页面加载时获取视频信息\n\t\t  getCurrentVideo(videoId) {\n\t\t\t  // console.log(\"视频Id===\"+videoId);\n\t\t    uni.request({\n\t\t      url: config.url + '/videoDetail?id=' + videoId,\n\t\t      success: res => {\n\t\t\t\t//   console.log(\"请求视频详情成功\");\n\t\t\t\t// console.log(res);\n\t\t        if (res.data.code === 0) {\n\t\t          this.videoInfo = res.data.data.videoInfo\n\t\t        }\n\t\t      }\n\t\t    })\n\t\t  },\n\t\t  getOthersList() {\n\t\t\t  uni.request({\n\t\t\t  \turl:config.url + '/othersList',\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.data.code === 0) {\n\t\t\t\t\t\tthis.othersList = res.data.data.othersList\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t  })\n\t\t  },\n\t\t  getCommentList(videoId) {\n\t\t\t  uni.request({\n\t\t\t  \turl:config.url + '/commentsList?id' + videoId,\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.data.code === 0) {\n\t\t\t\t\t\tthis.commentData = res.data.data.commentData;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t  })\n\t\t  },\n\t\t  \n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.video-info {\n\t  margin-top: 10rpx;\n\t .video {\n\t    width: 100%;\n\t  }\n\t .video-title {\n\t    display: flex;\n\t    justify-content: space-between;\n\t    font-size: 35rpx;\n\t  }\n\t .video-detail {\n\t    margin-top: 5rpx;\n\t    font-size: 28rpx;\n\t    text {\n\t      margin-left: 15rpx;\n\t    }\n\t  \n\t .author {\n\t    color: #000;\n\t  }\n\t  }\n\t}\n\t.other-list {\n\t  margin-top: 10rpx;\n\t .item_other {\n\t    display: flex;\n\t    justify-content: space-between;\n\t    margin-bottom: 20rpx;\n\t   .other-img-wrap {\n\t      width: 38%;\n\t      display: flex;\n\t      justify-content: center;\n\t      align-items: center;\n\t     .other-img-wrap image {\n\t        width: 90%;\n\t        border-radius: 15rpx;\n\t      }\n\t    }\n\t   .other-info {\n\t      width: 60%;\n\t      display: flex;\n\t      flex-direction: column;\n\t      justify-content: space-around;\n\t     .other-title {\n\t        font-size: 30rpx;\n\t        color: #e06f93;\n\t      }\n\t     .other-detail {\n\t        font-size: 26rpx;\n\t        color: #666;\n\t      }\n\t    }\n\t  }\n\t}\n\t.comment-wrap {\n\t  margin-top: 10rpx;\n\t .comment-title {\n\t    padding: 10rpx;\n\t    font-size: 28rpx;\n\t  }\n\t .comment-list {\n\t   .comment-item {\n\t      margin-bottom: 10rpx;\n\t      padding: 10rpx;\n\t      display: flex;\n\t      justify-content: space-between;\n\t      border-bottom: 1px solid #666;\n\t     .comment_user {\n\t        flex: 1;\n\t        display: flex;\n\t        justify-content: center;\n\t        align-items: center;\n\t       .comment_user_image {\n\t          width: 60%;\n\t          border-radius: 50%;\n\t        }\n\t      }\n\t     .comment-info {\n\t        flex: 5;\n\t        display: flex;\n\t        flex-direction: column;\n\t        justify-content: space-around;\n\t       .comment-detail {\n\t          display: flex;\n\t          justify-content: space-between;\n\t         .comment-detail .author {\n\t            font-size: 28rpx;\n\t            color: #000;\n\t          }\n\t         .comment-detail .date {\n\t            color: #666;\n\t            font-size: 24rpx;\n\t          }\n\t        }\n\t      }\n\t    }\n\t  }\n\t .comment-content {\n\t    font-size: 28rpx;\n\t    color: #000;\n\t  }\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753861137215\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}