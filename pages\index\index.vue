<template>
  <view class="container">
    <!-- 标题、用户头像、设置按钮与关注按钮区域 -->
    <view class="header-wrap">
      <MyTitle />
      <!-- 右侧：用户头像 + 设置按钮 + 关注按钮 -->
      <view class="user-actions">
        <view class="user-avatar-wrap" @click="handleUserClick">
          <image 
            :src="userAvatar"
            mode="aspectFill" 
            class="user-avatar"
            @error="handleAvatarError"
            :lazy-load="true"
          />
        </view>
        
        <!-- 设置按钮 -->
        <view class="setting-btn" @click="handleSettingClick">
          <text class="fa fa-cog">设置</text>
        </view>
        
        <!-- 关注按钮 -->
        <view class="follow-btn" @click="handleFollowClick">
          <text>关注</text>
        </view>
      </view>
    </view>
    
    <!-- 导航栏区域 -->
    <view class="nav-wrap">
      <scroll-view class="nav" scroll-x show-scrollbar="false">
        <view
          v-for="(item, index) in navList" 
          :key="item.id"
          @click="handleNavClick(index)" 
          :class="['nav-item', { active: index === currentIndexNav }]"
        >
          {{ item.text }}
        </view>
      </scroll-view>
    </view>
    
    <!-- 轮播图区域 -->
    <view class="slides">
      <swiper 
        v-if="swiperList.length > 0"
        :autoplay="true" 
        :indicator-dots="true" 
        :circular="true"
        :interval="3000"
        :duration="500"
      >
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <image 
            :src="item.imgSrc" 
            mode="aspectFill"
            :lazy-load="true"
            @error="handleImageError"
          />
        </swiper-item>
      </swiper>
      <view v-else class="loading-placeholder">
        <text>加载中...</text>
      </view>
    </view>
    
    <!-- 视频列表区域 -->
    <view class="video-wrap">
      <view 
        v-for="(item, index) in videosList" 
        :key="item.id"
        class="video-item" 
        @click="handleVideoClick(item.id)"
      >
        <view class="video-img">
          <image 
            :src="item.imgSrc" 
            mode="aspectFill"
            :lazy-load="true"
            @error="handleImageError"
          />
          <view class="video-info">
            <view class="play-count-wrap">
              <text class="fa fa-play-circle-o"></text>
              <text class="play-count">{{ formatPlayCount(item.playCount) }}</text>
            </view>
          </view>
        </view>
        <view class="video-title">
          {{ item.desc }}
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="videosList.length === 0 && !isLoading" class="empty-state">
        <text>暂无视频内容</text>
      </view>
    </view>
    
    <!-- 加载提示 -->
    <uni-load-more v-if="isLoading" status="loading"></uni-load-more>
  </view>
</template>

<script>
import config from '../../common/config.js'
import { throttle, debounce } from '../../utils/common.js' // 假设有工具函数

export default {
  name: 'HomePage',
  data() {
    return {
      navList: [],
      currentIndexNav: 0,
      swiperList: [],
      videosList: [],
      isLoading: false,
      userAvatar: '../../static/mmexport1749721684379.jpg',
      defaultImage: '../../static/default-image.png' // 默认占位图
    }
  },
  
  onLoad(options) {
    this.initPageData()
  },
  
  // 页面显示时刷新数据
  onShow() {
    // 可以在这里刷新某些数据
  },
  
  methods: {
    // 初始化页面数据
    async initPageData() {
      this.isLoading = true
      try {
        await Promise.all([
          this.getNavList(),
          this.getSwiperList(),
          this.getVideosList()
        ])
      } catch (error) {
        console.error('页面数据加载失败:', error)
        this.showToast('数据加载失败，请重试')
      } finally {
        this.isLoading = false
      }
    },
    
    // 封装网络请求方法
    async request(url, options = {}) {
      return new Promise((resolve, reject) => {
        uni.request({
          url: config.url + url,
          timeout: 10000,
          ...options,
          success: (res) => {
            if (res.statusCode === 200 && res.data.code === 200) {
              resolve(res.data.data)
            } else {
              reject(new Error(res.data.message || '请求失败'))
            }
          },
          fail: (error) => {
            reject(error)
          }
        })
      })
    },
    
    // 获取导航列表
    async getNavList() {
      try {
        const data = await this.request('/navList')
        this.navList = data.navList || []
      } catch (error) {
        console.error('获取导航列表失败:', error)
        this.showToast('导航数据加载失败')
      }
    },
    
    // 获取轮播图列表
    async getSwiperList() {
      try {
        const data = await this.request('/swiperList')
        this.swiperList = data.swiperList || []
      } catch (error) {
        console.error('获取轮播图失败:', error)
        this.showToast('轮播图加载失败')
      }
    },
    
    // 获取视频列表
    async getVideosList() {
      try {
        const data = await this.request('/videosList')
        this.videosList = data.videosList || []
      } catch (error) {
        console.error('获取视频列表失败:', error)
        this.showToast('视频列表加载失败')
      }
    },
    
    // 导航点击事件（添加防抖）
    handleNavClick: debounce(function(index) {
      if (this.currentIndexNav !== index) {
        this.currentIndexNav = index
        // 可以在这里根据导航切换加载不同内容
        this.onNavChange(index)
      }
    }, 300),
    
    // 导航切换处理
    onNavChange(index) {
      // 根据导航索引加载对应内容
      console.log('导航切换到:', index)
    },
    
    // 视频点击事件（添加节流）
    handleVideoClick: throttle(function(id) {
      if (!id) return
      uni.navigateTo({
        url: `/pages/detail/detail?id=${id}`
      })
    }, 1000),
    
    // 用户头像点击
    handleUserClick: throttle(function() {
      uni.navigateTo({
        url: '/pages/user/user'
      })
    }, 1000),
    
    // 设置按钮点击
    handleSettingClick: throttle(function() {
      uni.navigateTo({
        url: '/pages/setting/setting'
      })
    }, 1000),
    
    // 关注按钮点击
    handleFollowClick: throttle(function() {
      uni.navigateTo({
        url: '/pages/follow/follow'
      })
    }, 1000),
    
    // 图片加载错误处理
    handleImageError(e) {
      // 设置默认图片
      e.target.src = this.defaultImage
    },
    
    // 头像加载错误处理
    handleAvatarError(e) {
      this.userAvatar = this.defaultImage
    },
    
    // 格式化播放次数
    formatPlayCount(count) {
      if (!count) return '0'
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      if (count < 100000000) return (count / 10000).toFixed(1) + 'w'
      return (count / 100000000).toFixed(1) + '亿'
    },
    
    // 显示提示信息
    showToast(title, icon = 'none') {
      uni.showToast({
        title,
        icon,
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

// 头部区域美化
.header-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #87CEEB 0%, #6bb8e0 100%);
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(135,206,235,0.2);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 6rpx;
    background: rgba(135,206,235,0.3);
    border-radius: 3rpx;
  }
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-avatar-wrap {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 6rpx 20rpx rgba(135,206,235,0.3);
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.setting-btn, .follow-btn {
  padding: 16rpx 24rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(135,206,235,0.2);
  transition: all 0.2s ease;
  
  text {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
  }
  
  &:active {
    transform: scale(0.95);
    background: rgba(255,255,255,0.8);
  }
}

// 导航栏美化
.nav-wrap {
  padding: 20rpx 0;
  background: #fff;
  margin-bottom: 20rpx;
}

.nav {
  white-space: nowrap;
  padding: 0 30rpx;
  
  .nav-item {
    display: inline-block;
    padding: 20rpx 32rpx;
    margin-right: 20rpx;
    font-size: 30rpx;
    color: #666;
    background: #f8f9fa;
    border-radius: 30rpx;
    transition: all 0.3s ease;
    position: relative;
    
    &.active {
      background: linear-gradient(135deg, #87CEEB 0%, #6bb8e0 100%);
      color: #fff;
      font-weight: 600;
      box-shadow: 0 4rpx 16rpx rgba(135,206,235,0.3);
      transform: translateY(-2rpx);
    }
  }
}

// 轮播图美化
.slides {
  margin: 0 30rpx 40rpx;
  
  swiper {
    height: 300rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(135,206,235,0.15);
  }
  
  image {
    width: 100%;
    height: 100%;
  }
}

.loading-placeholder {
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 20rpx;
  
  text {
    color: #999;
    font-size: 28rpx;
  }
}

// 视频列表美化
.video-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 30rpx;
  gap: 20rpx;
}

.video-item {
  width: calc(50% - 10rpx);
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(135,206,235,0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(135,206,235,0.2);
  }
}

.video-img {
  position: relative;
  height: 240rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
  
  .video-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.6));
    padding: 20rpx 16rpx 12rpx;
    display: flex;
    justify-content: flex-end;
  }
  
  .play-count-wrap {
    display: flex;
    align-items: center;
    background: rgba(0,0,0,0.4);
    border-radius: 20rpx;
    padding: 6rpx 12rpx;
    
    .fa-play-circle-o {
      color: #fff;
      font-size: 24rpx;
      margin-right: 6rpx;
    }
    
    .play-count {
      color: #fff;
      font-size: 22rpx;
      font-weight: 500;
    }
  }
}

.video-title {
  padding: 20rpx 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 空状态
.empty-state {
  width: 100%;
  padding: 100rpx 0;
  text-align: center;
  
  text {
    color: #999;
    font-size: 28rpx;
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .video-item {
    width: 100%;
  }
}
</style>
