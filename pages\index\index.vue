<template>
  <view class="container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 顶部导航栏 -->
    <view class="header-wrap">
      <MyTitle />
      <!-- 右侧操作区域 -->
      <view class="user-actions">
        <!-- 搜索按钮 -->
        <view class="action-btn search-btn" @click="handleSearchClick">
          <text class="iconfont icon-search"></text>
        </view>

        <!-- 消息按钮 -->
        <view class="action-btn message-btn" @click="handleMessageClick">
          <text class="iconfont icon-message"></text>
          <view v-if="unreadCount > 0" class="badge">{{ unreadCount > 99 ? '99+' : unreadCount }}</view>
        </view>

        <!-- 用户头像 -->
        <view class="user-avatar-wrap" @click="handleUserClick">
          <image
            :src="userAvatar"
            mode="aspectFill"
            class="user-avatar"
            @error="handleAvatarError"
            :lazy-load="true"
          />
          <view class="avatar-border"></view>
        </view>
      </view>
    </view>

    <!-- 分类导航栏 -->
    <view class="nav-wrap">
      <scroll-view class="nav" scroll-x show-scrollbar="false" :scroll-left="navScrollLeft">
        <view
          v-for="(item, index) in navList"
          :key="item.id"
          @click="handleNavClick(index)"
          :class="['nav-item', { active: index === currentIndexNav }]"
        >
          <text class="nav-text">{{ item.text }}</text>
          <view v-if="index === currentIndexNav" class="nav-indicator"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 轮播图区域 -->
    <view class="slides" v-if="showSwiper">
      <swiper
        v-if="swiperList.length > 0"
        :autoplay="true"
        :indicator-dots="false"
        :circular="true"
        :interval="4000"
        :duration="800"
        @change="onSwiperChange"
        class="main-swiper"
      >
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <view class="swiper-item-wrap" @click="handleSwiperClick(item)">
            <image
              :src="item.imgSrc"
              mode="aspectFill"
              :lazy-load="true"
              @error="handleImageError"
              class="swiper-image"
            />
            <view class="swiper-overlay">
              <view class="swiper-content">
                <text class="swiper-title">{{ item.title }}</text>
                <text class="swiper-desc">{{ item.desc }}</text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <!-- 自定义指示器 -->
      <view class="custom-indicators">
        <view
          v-for="(item, index) in swiperList"
          :key="index"
          :class="['indicator-dot', { active: index === currentSwiperIndex }]"
        ></view>
      </view>

      <view v-else class="loading-placeholder">
        <view class="loading-animation">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text class="loading-text">精彩内容加载中...</text>
      </view>
    </view>

    <!-- 快捷功能区 -->
    <view class="quick-actions">
      <view class="quick-item" @click="handleQuickAction('trending')">
        <view class="quick-icon trending">
          <text class="iconfont icon-fire"></text>
        </view>
        <text class="quick-text">热门</text>
      </view>
      <view class="quick-item" @click="handleQuickAction('live')">
        <view class="quick-icon live">
          <text class="iconfont icon-live"></text>
        </view>
        <text class="quick-text">直播</text>
      </view>
      <view class="quick-item" @click="handleQuickAction('nearby')">
        <view class="quick-icon nearby">
          <text class="iconfont icon-location"></text>
        </view>
        <text class="quick-text">附近</text>
      </view>
      <view class="quick-item" @click="handleQuickAction('upload')">
        <view class="quick-icon upload">
          <text class="iconfont icon-plus"></text>
        </view>
        <text class="quick-text">发布</text>
      </view>
    </view>

    <!-- 视频列表区域 -->
    <view class="video-section">
      <view class="section-header">
        <text class="section-title">推荐视频</text>
        <view class="view-mode-switch">
          <view
            :class="['mode-btn', { active: viewMode === 'grid' }]"
            @click="switchViewMode('grid')"
          >
            <text class="iconfont icon-grid"></text>
          </view>
          <view
            :class="['mode-btn', { active: viewMode === 'list' }]"
            @click="switchViewMode('list')"
          >
            <text class="iconfont icon-list"></text>
          </view>
        </view>
      </view>

      <view :class="['video-wrap', viewMode]">
        <view
          v-for="(item, index) in videosList"
          :key="item.id"
          :class="['video-item', { 'list-mode': viewMode === 'list' }]"
          @click="handleVideoClick(item.id)"
          @longpress="handleVideoLongPress(item, index)"
        >
          <view class="video-img">
            <image
              :src="item.imgSrc"
              mode="aspectFill"
              :lazy-load="true"
              @error="handleImageError"
              class="video-thumbnail"
            />

            <!-- 播放按钮 -->
            <view class="play-overlay">
              <view class="play-btn">
                <text class="iconfont icon-play"></text>
              </view>
            </view>

            <!-- 视频时长 -->
            <view class="video-duration">
              <text>{{ formatDuration(item.duration) }}</text>
            </view>

            <!-- 播放量信息 -->
            <view class="video-stats">
              <view class="stat-item">
                <text class="iconfont icon-play-circle"></text>
                <text class="stat-text">{{ formatPlayCount(item.playCount) }}</text>
              </view>
            </view>
          </view>

          <view class="video-content">
            <view class="video-title">{{ item.desc }}</view>
            <view class="video-meta">
              <view class="author-info">
                <image :src="item.authorAvatar" class="author-avatar" />
                <text class="author-name">{{ item.author }}</text>
              </view>
              <view class="video-actions">
                <view class="action-item" @click.stop="handleLike(item, index)">
                  <text :class="['iconfont', item.isLiked ? 'icon-heart-fill' : 'icon-heart']"></text>
                  <text class="action-count">{{ formatCount(item.likeCount) }}</text>
                </view>
                <view class="action-item" @click.stop="handleShare(item)">
                  <text class="iconfont icon-share"></text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="videosList.length === 0 && !isLoading" class="empty-state">
        <view class="empty-icon">
          <text class="iconfont icon-video-off"></text>
        </view>
        <text class="empty-text">暂无视频内容</text>
        <view class="empty-btn" @click="refreshData">
          <text>刷新试试</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="isLoading" class="loading-more">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 回到顶部按钮 -->
    <view
      v-if="showBackTop"
      class="back-top-btn"
      @click="scrollToTop"
    >
      <text class="iconfont icon-arrow-up"></text>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script>
import config from '../../common/config.js'

export default {
  name: 'HomePage',
  data() {
    return {
      // 系统信息
      statusBarHeight: 0,

      // 导航相关
      navList: [
        { id: 1, text: '推荐' },
        { id: 2, text: '热门' },
        { id: 3, text: '关注' },
        { id: 4, text: '音乐' },
        { id: 5, text: '舞蹈' },
        { id: 6, text: '美食' },
        { id: 7, text: '游戏' },
        { id: 8, text: '搞笑' }
      ],
      currentIndexNav: 0,
      navScrollLeft: 0,

      // 轮播图相关
      swiperList: [
        {
          id: 1,
          imgSrc: '../../static/ddy.jpeg',
          title: '精彩视频推荐',
          desc: '发现更多有趣内容'
        },
        {
          id: 2,
          imgSrc: '../../static/tx.jpeg',
          title: '热门创作者',
          desc: '关注你喜欢的UP主'
        },
        {
          id: 3,
          imgSrc: '../../static/lyx.jpeg',
          title: '今日热点',
          desc: '不容错过的精彩瞬间'
        }
      ],
      currentSwiperIndex: 0,
      showSwiper: true,

      // 视频列表相关
      videosList: [
        {
          id: 1,
          imgSrc: '../../static/ddy.jpeg',
          desc: '超好看的舞蹈视频，学会了你就是全场最靓的仔！',
          playCount: 12500,
          likeCount: 1200,
          duration: 180,
          author: '舞蹈达人小美',
          authorAvatar: '../../static/tx.jpeg',
          isLiked: false
        },
        {
          id: 2,
          imgSrc: '../../static/lyx.jpeg',
          desc: '美食制作教程，简单易学的家常菜做法',
          playCount: 8900,
          likeCount: 890,
          duration: 240,
          author: '美食小当家',
          authorAvatar: '../../static/oynn.jpeg',
          isLiked: true
        },
        {
          id: 3,
          imgSrc: '../../static/whd.jpeg',
          desc: '搞笑日常，笑到肚子疼的沙雕视频合集',
          playCount: 25600,
          likeCount: 3200,
          duration: 120,
          author: '搞笑王',
          authorAvatar: '../../static/zlh.jpg',
          isLiked: false
        },
        {
          id: 4,
          imgSrc: '../../static/txw.jpg',
          desc: '旅行vlog，带你看遍世界美景',
          playCount: 15800,
          likeCount: 1580,
          duration: 300,
          author: '旅行者小王',
          authorAvatar: '../../static/user.jpg',
          isLiked: false
        }
      ],

      // UI状态
      isLoading: false,
      viewMode: 'grid', // grid 或 list
      showBackTop: false,
      unreadCount: 3,

      // 用户信息
      userAvatar: '../../static/mmexport1749721684379.jpg',
      defaultImage: '../../static/logo.png'
    }
  },

  onLoad() {
    this.getSystemInfo()
    this.initPageData()
  },

  onShow() {
    // 页面显示时可以刷新某些数据
    this.checkUnreadMessages()
  },

  onPageScroll(e) {
    // 控制回到顶部按钮显示
    this.showBackTop = e.scrollTop > 500
  },
  
  methods: {
    // 获取系统信息
    getSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 0
    },

    // 初始化页面数据
    async initPageData() {
      this.isLoading = true
      try {
        // 模拟API请求延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        // 实际项目中这里会调用真实的API
        // await Promise.all([
        //   this.getNavList(),
        //   this.getSwiperList(),
        //   this.getVideosList()
        // ])
      } catch (error) {
        console.error('页面数据加载失败:', error)
        this.showToast('数据加载失败，请重试')
      } finally {
        this.isLoading = false
      }
    },

    // 检查未读消息
    checkUnreadMessages() {
      // 模拟检查未读消息
      // 实际项目中这里会调用API
    },

    // 刷新数据
    refreshData() {
      this.initPageData()
    },

    // 轮播图切换
    onSwiperChange(e) {
      this.currentSwiperIndex = e.detail.current
    },

    // 轮播图点击
    handleSwiperClick(item) {
      console.log('轮播图点击:', item)
      // 可以跳转到相应页面
    },

    // 搜索按钮点击
    handleSearchClick() {
      uni.navigateTo({
        url: '/pages/search/search'
      })
    },

    // 消息按钮点击
    handleMessageClick() {
      uni.navigateTo({
        url: '/pages/message/message'
      })
      // 清除未读数量
      this.unreadCount = 0
    },

    // 快捷功能点击
    handleQuickAction(type) {
      const actions = {
        trending: () => {
          this.currentIndexNav = 1
          this.handleNavClick(1)
        },
        live: () => {
          uni.navigateTo({
            url: '/pages/live/live'
          })
        },
        nearby: () => {
          uni.navigateTo({
            url: '/pages/nearby/nearby'
          })
        },
        upload: () => {
          uni.navigateTo({
            url: '/pages/upload/upload'
          })
        }
      }

      if (actions[type]) {
        actions[type]()
      }
    },

    // 切换视图模式
    switchViewMode(mode) {
      this.viewMode = mode
      uni.setStorageSync('viewMode', mode)
    },

    // 导航点击事件
    handleNavClick(index) {
      if (this.currentIndexNav !== index) {
        this.currentIndexNav = index
        this.onNavChange(index)

        // 计算导航滚动位置
        this.calculateNavScroll(index)
      }
    },

    // 计算导航滚动位置
    calculateNavScroll(index) {
      const itemWidth = 120 // 估算每个导航项宽度
      const containerWidth = 750 // 容器宽度
      const scrollLeft = Math.max(0, (index * itemWidth) - (containerWidth / 2))
      this.navScrollLeft = scrollLeft
    },

    // 导航切换处理
    onNavChange(index) {
      console.log('导航切换到:', index)
      // 根据导航索引加载对应内容
      this.loadCategoryVideos(index)
    },

    // 加载分类视频
    loadCategoryVideos(categoryIndex) {
      // 模拟根据分类加载不同视频
      console.log('加载分类视频:', categoryIndex)
    },

    // 视频点击事件
    handleVideoClick(id) {
      if (!id) return
      uni.navigateTo({
        url: `/pages/detail/detail?id=${id}`
      })
    },

    // 视频长按事件
    handleVideoLongPress(item, index) {
      uni.showActionSheet({
        itemList: ['收藏', '分享', '举报', '不感兴趣'],
        success: (res) => {
          const actions = ['collect', 'share', 'report', 'notInterested']
          this.handleVideoAction(actions[res.tapIndex], item, index)
        }
      })
    },

    // 视频操作处理
    handleVideoAction(action, item, index) {
      const actionMap = {
        collect: () => this.collectVideo(item),
        share: () => this.handleShare(item),
        report: () => this.reportVideo(item),
        notInterested: () => this.markNotInterested(item, index)
      }

      if (actionMap[action]) {
        actionMap[action]()
      }
    },

    // 用户头像点击
    handleUserClick() {
      uni.navigateTo({
        url: '/pages/user/user'
      })
    },

    // 点赞操作
    handleLike(item, index) {
      item.isLiked = !item.isLiked
      if (item.isLiked) {
        item.likeCount++
        this.showToast('点赞成功', 'success')
      } else {
        item.likeCount--
        this.showToast('取消点赞', 'none')
      }

      // 更新视频列表
      this.$set(this.videosList, index, item)
    },

    // 分享操作
    handleShare(item) {
      uni.showActionSheet({
        itemList: ['微信好友', '朋友圈', 'QQ好友', '微博', '复制链接'],
        success: (res) => {
          const platforms = ['wechat', 'moments', 'qq', 'weibo', 'copy']
          this.shareToplatform(platforms[res.tapIndex], item)
        }
      })
    },

    // 分享到平台
    shareToplatform(platform, item) {
      console.log(`分享到${platform}:`, item)
      this.showToast('分享成功', 'success')
    },

    // 收藏视频
    collectVideo(item) {
      this.showToast('收藏成功', 'success')
    },

    // 举报视频
    reportVideo(item) {
      this.showToast('举报成功', 'success')
    },

    // 标记不感兴趣
    markNotInterested(item, index) {
      this.videosList.splice(index, 1)
      this.showToast('已标记为不感兴趣', 'none')
    },

    // 回到顶部
    scrollToTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      })
    },

    // 图片加载错误处理
    handleImageError(e) {
      e.target.src = this.defaultImage
    },

    // 头像加载错误处理
    handleAvatarError() {
      this.userAvatar = this.defaultImage
    },

    // 格式化播放次数
    formatPlayCount(count) {
      return this.formatCount(count)
    },

    // 格式化数量
    formatCount(count) {
      if (!count) return '0'
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      if (count < 100000000) return (count / 10000).toFixed(1) + 'w'
      return (count / 100000000).toFixed(1) + '亿'
    },

    // 格式化时长
    formatDuration(seconds) {
      if (!seconds) return '00:00'
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 显示提示信息
    showToast(title, icon = 'none') {
      uni.showToast({
        title,
        icon,
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss">
// 全局变量
$primary-color: #ff6b6b;
$secondary-color: #4ecdc4;
$accent-color: #45b7d1;
$text-primary: #2c3e50;
$text-secondary: #7f8c8d;
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$border-color: #e9ecef;
$shadow-light: 0 2rpx 12rpx rgba(0,0,0,0.04);
$shadow-medium: 0 4rpx 20rpx rgba(0,0,0,0.08);
$shadow-heavy: 0 8rpx 32rpx rgba(0,0,0,0.12);

.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

// 状态栏占位
.status-bar {
  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);
}

// 顶部导航栏
.header-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  }
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

// 操作按钮样式
.action-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .iconfont {
    font-size: 36rpx;
    color: rgba(255,255,255,0.9);
  }

  &:active {
    transform: scale(0.9);
    background: rgba(255,255,255,0.25);
  }

  // 消息徽章
  .badge {
    position: absolute;
    top: -6rpx;
    right: -6rpx;
    min-width: 32rpx;
    height: 32rpx;
    background: #ff4757;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    color: white;
    font-weight: 600;
    border: 3rpx solid rgba(255,255,255,0.8);
    line-height: 1;
    padding: 0 8rpx;
  }
}

// 用户头像
.user-avatar-wrap {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .user-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3rpx solid rgba(255,255,255,0.8);
  }

  .avatar-border {
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255,255,255,0.6), rgba(255,255,255,0.2));
    z-index: -1;
  }

  &:active {
    transform: scale(0.95);
  }
}

// 分类导航栏
.nav-wrap {
  background: $bg-primary;
  padding: 24rpx 0 16rpx;
  box-shadow: $shadow-light;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav {
  white-space: nowrap;
  padding: 0 30rpx;

  .nav-item {
    display: inline-block;
    padding: 16rpx 0;
    margin-right: 48rpx;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .nav-text {
      font-size: 32rpx;
      color: $text-secondary;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .nav-indicator {
      position: absolute;
      bottom: -8rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 6rpx;
      background: linear-gradient(90deg, $primary-color 0%, $accent-color 100%);
      border-radius: 3rpx;
      animation: slideIn 0.3s ease;
    }

    &.active .nav-text {
      color: $primary-color;
      font-weight: 600;
      transform: scale(1.05);
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 40rpx;
    opacity: 1;
  }
}

// 轮播图区域
.slides {
  margin: 24rpx 30rpx 32rpx;
  position: relative;

  .main-swiper {
    height: 360rpx;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: $shadow-medium;
  }

  .swiper-item-wrap {
    position: relative;
    height: 100%;
    overflow: hidden;
  }

  .swiper-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .swiper-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    padding: 40rpx 32rpx 32rpx;
  }

  .swiper-content {
    .swiper-title {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: white;
      margin-bottom: 8rpx;
      text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);
    }

    .swiper-desc {
      display: block;
      font-size: 28rpx;
      color: rgba(255,255,255,0.8);
      text-shadow: 0 1rpx 4rpx rgba(0,0,0,0.3);
    }
  }

  // 自定义指示器
  .custom-indicators {
    position: absolute;
    bottom: 20rpx;
    right: 32rpx;
    display: flex;
    gap: 12rpx;

    .indicator-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background: rgba(255,255,255,0.4);
      transition: all 0.3s ease;

      &.active {
        background: white;
        transform: scale(1.2);
      }
    }
  }
}

// 加载占位符
.loading-placeholder {
  height: 360rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: $bg-secondary;
  border-radius: 24rpx;

  .loading-animation {
    display: flex;
    gap: 8rpx;
    margin-bottom: 24rpx;

    .loading-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background: $primary-color;
      animation: bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: $text-secondary;
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 快捷功能区
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 30rpx;
  background: $bg-primary;
  margin: 0 30rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: $shadow-light;

  .quick-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    .quick-icon {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .iconfont {
        font-size: 40rpx;
        color: white;
      }

      &.trending {
        background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
      }

      &.live {
        background: linear-gradient(135deg, #4ecdc4 0%, #6ee5dd 100%);
      }

      &.nearby {
        background: linear-gradient(135deg, #45b7d1 0%, #6cc5e0 100%);
      }

      &.upload {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
    }

    .quick-text {
      font-size: 24rpx;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

// 视频区域
.video-section {
  padding: 0 30rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .view-mode-switch {
      display: flex;
      background: $bg-secondary;
      border-radius: 12rpx;
      padding: 4rpx;

      .mode-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        .iconfont {
          font-size: 28rpx;
          color: $text-secondary;
        }

        &.active {
          background: $bg-primary;
          box-shadow: $shadow-light;

          .iconfont {
            color: $primary-color;
          }
        }
      }
    }
  }
}

// 视频列表
.video-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;

  &.grid {
    .video-item {
      width: calc(50% - 12rpx);
    }
  }

  &.list {
    flex-direction: column;

    .video-item {
      width: 100%;

      &.list-mode {
        flex-direction: row;

        .video-img {
          width: 240rpx;
          height: 180rpx;
          flex-shrink: 0;
        }

        .video-content {
          flex: 1;
          padding: 16rpx 0 16rpx 20rpx;
        }
      }
    }
  }
}

.video-item {
  background: $bg-primary;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: $shadow-light;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  &:active {
    transform: translateY(-4rpx);
    box-shadow: $shadow-medium;
  }
}

// 视频图片区域
.video-img {
  position: relative;
  height: 240rpx;
  overflow: hidden;

  .video-thumbnail {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  // 播放按钮覆盖层
  .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.1);
    opacity: 0;
    transition: all 0.3s ease;

    .play-btn {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: rgba(255,255,255,0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scale(0.8);
      transition: all 0.3s ease;

      .iconfont {
        font-size: 36rpx;
        color: $primary-color;
        margin-left: 4rpx;
      }
    }
  }

  // 视频时长
  .video-duration {
    position: absolute;
    bottom: 12rpx;
    right: 12rpx;
    background: rgba(0,0,0,0.7);
    color: white;
    font-size: 22rpx;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    font-weight: 500;
  }

  // 播放统计
  .video-stats {
    position: absolute;
    top: 12rpx;
    right: 12rpx;

    .stat-item {
      display: flex;
      align-items: center;
      background: rgba(0,0,0,0.6);
      color: white;
      font-size: 22rpx;
      padding: 6rpx 10rpx;
      border-radius: 12rpx;
      gap: 4rpx;

      .iconfont {
        font-size: 20rpx;
      }

      .stat-text {
        font-weight: 500;
      }
    }
  }
}

// 视频内容区域
.video-content {
  padding: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;

  .video-title {
    font-size: 28rpx;
    font-weight: 500;
    color: $text-primary;
    line-height: 1.4;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .video-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;

    .author-info {
      display: flex;
      align-items: center;
      gap: 12rpx;
      flex: 1;
      min-width: 0;

      .author-avatar {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        border: 2rpx solid $border-color;
      }

      .author-name {
        font-size: 24rpx;
        color: $text-secondary;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .video-actions {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .action-item {
        display: flex;
        align-items: center;
        gap: 6rpx;
        padding: 8rpx;
        border-radius: 8rpx;
        transition: all 0.2s ease;

        .iconfont {
          font-size: 28rpx;
          color: $text-secondary;

          &.icon-heart-fill {
            color: $primary-color;
          }
        }

        .action-count {
          font-size: 22rpx;
          color: $text-secondary;
          font-weight: 500;
        }

        &:active {
          background: $bg-secondary;
          transform: scale(0.95);
        }
      }
    }
  }
}

// 悬停效果
.video-item:hover {
  .video-img .play-overlay {
    opacity: 1;

    .play-btn {
      transform: scale(1);
    }
  }

  .video-thumbnail {
    transform: scale(1.05);
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: $bg-secondary;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32rpx;

    .iconfont {
      font-size: 60rpx;
      color: $text-secondary;
    }
  }

  .empty-text {
    font-size: 32rpx;
    color: $text-secondary;
    margin-bottom: 32rpx;
  }

  .empty-btn {
    padding: 16rpx 32rpx;
    background: $primary-color;
    color: white;
    border-radius: 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
      background: darken($primary-color, 10%);
    }
  }
}

// 加载更多
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;

  .loading-spinner {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid $bg-secondary;
    border-top: 3rpx solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 28rpx;
    color: $text-secondary;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 回到顶部按钮
.back-top-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);
  box-shadow: $shadow-medium;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .iconfont {
    font-size: 32rpx;
    color: white;
  }

  &:active {
    transform: scale(0.9);
  }

  // 添加呼吸动画
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 底部安全区域
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background: $bg-secondary;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .video-wrap.grid .video-item {
    width: 100%;
  }

  .quick-actions {
    .quick-item .quick-icon {
      width: 72rpx;
      height: 72rpx;

      .iconfont {
        font-size: 32rpx;
      }
    }

    .quick-text {
      font-size: 22rpx;
    }
  }

  .slides .main-swiper {
    height: 280rpx;
  }

  .nav .nav-item {
    margin-right: 32rpx;

    .nav-text {
      font-size: 28rpx;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .video-item {
    background: #2d2d2d;

    .video-title {
      color: #ffffff;
    }

    .author-name {
      color: #cccccc;
    }
  }

  .nav-wrap {
    background: #2d2d2d;
  }

  .quick-actions {
    background: #2d2d2d;
  }
}

// 动画优化
* {
  -webkit-tap-highlight-color: transparent;
}

// 滚动优化
.container {
  scroll-behavior: smooth;
}

// 字体图标样式（如果使用iconfont）
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
