@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}
.user-header-bg {
  height: 220rpx;
  background: linear-gradient(135deg, #4a90e2, #5c6bc0);
  position: relative;
}
.user-header-bg .back-btn {
  position: absolute;
  left: 30rpx;
  top: 60rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}
.user-card {
  width: 90%;
  margin: -80rpx auto 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 5rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx 0 30rpx;
  text-align: center;
}
.user-card .avatar-wrap {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto 30rpx;
}
.user-card .avatar-wrap .avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 5rpx solid #fff;
}
.user-card .avatar-wrap .edit-avatar-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #4a90e2;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  border: 2rpx solid #fff;
}
.user-card .user-basic {
  margin-bottom: 30rpx;
}
.user-card .user-basic .user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.user-card .user-basic .user-sign {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.user-card .user-stats {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
}
.user-card .user-stats .stat-item {
  flex: 1;
  padding: 10rpx 0;
}
.user-card .user-stats .stat-item .stat-num {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}
.user-card .user-stats .stat-item .stat-label {
  font-size: 24rpx;
  color: #666;
}
.user-menu {
  width: 90%;
  margin: 30rpx auto;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 5rpx 20rpx rgba(0, 0, 0, 0.1);
}
.user-menu .menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.user-menu .menu-item:last-child {
  border-bottom: none;
}
.user-menu .menu-item .menu-icon {
  font-size: 32rpx;
  color: #4a90e2;
  margin-right: 20rpx;
  width: 32rpx;
  text-align: center;
}
.user-menu .menu-item .menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}
.user-menu .menu-item .menu-arrow {
  font-size: 30rpx;
  color: #ccc;
}

