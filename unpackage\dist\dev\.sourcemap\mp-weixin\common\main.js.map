{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/短视频平台/App.vue?7ad5", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/App.vue?eb91", "webpack:///C:/Users/<USER>/Desktop/短视频平台/App.vue?04fb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "$mount"], "mappings": ";;;;;;;;;iDAAA;;AAE2D;;;AAG3D,qE,ynCAJA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAIAC,aAAIC,MAAJ,CAAWC,aAAX,GAA2B,KAA3B;AACAC,aAAIC,MAAJ,GAAa,KAAb;AACA,IAAMC,GAAG,GAAG,IAAIL,YAAJ;AACLG,YADK,EAAZ;;AAGA,UAAAE,GAAG,EAACC,MAAJ,G;;;;;;;;;;;;;;;;;ACXA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC6K;AAC7K,gBAAgB,uLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAkpB,CAAgB,+pBAAG,EAAC,C;;;;;;;;;;;;ACCtqB;AACA;AACA;AACA,GAHA;AAIA;AACA;AACA,GANA;AAOA;AACA;AACA,GATA,E;;;;;;;;;;;ACDA;AAAA;AAAA;AAAA;AAAy7B,CAAgB,w6BAAG,EAAC,C;;;;;;;;;;ACA78B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n\r\nimport Vue from 'vue'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n    ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t@import url(\"./static/iconfont.css\");\r\n\r\n\t/* 全局样式重置 */\r\n\tpage {\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t/* 通用类 */\r\n\t.container {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.text-center {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.flex-center {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.flex-between {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.flex-column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 文字省略 */\r\n\t.text-ellipsis {\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t}\r\n\r\n\t.text-ellipsis-2 {\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* 动画类 */\r\n\t.fade-in {\r\n\t\tanimation: fadeIn 0.3s ease-in-out;\r\n\t}\r\n\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(20rpx);\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.slide-up {\r\n\t\tanimation: slideUp 0.3s ease-out;\r\n\t}\r\n\r\n\t@keyframes slideUp {\r\n\t\tfrom {\r\n\t\t\ttransform: translateY(100%);\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.2s ease;\r\n\t\tborder: none;\r\n\t\toutline: none;\r\n\t}\r\n\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.btn-secondary {\r\n\t\tbackground: #f8f9fa;\r\n\t\tcolor: #6c757d;\r\n\t\tborder: 1rpx solid #dee2e6;\r\n\t}\r\n\r\n\t.btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* 分割线 */\r\n\t.divider {\r\n\t\theight: 1rpx;\r\n\t\tbackground: #e9ecef;\r\n\t\tmargin: 20rpx 0;\r\n\t}\r\n\r\n\t/* 安全区域 */\r\n\t.safe-area-top {\r\n\t\theight: constant(safe-area-inset-top);\r\n\t\theight: env(safe-area-inset-top);\r\n\t}\r\n\r\n\t.safe-area-bottom {\r\n\t\theight: constant(safe-area-inset-bottom);\r\n\t\theight: env(safe-area-inset-bottom);\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753864396149\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}