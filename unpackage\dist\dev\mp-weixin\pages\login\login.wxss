@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  padding: 120rpx 60rpx 0;
  background: linear-gradient(135deg, #E6F7FF 0%, #F3F3FF 100%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  /* 背景模糊效果 */
}
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}
.logo-container .logo {
  width: 220rpx;
  height: 220rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  /* 过渡效果 */
}
.logo-container .logo:hover {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  /* 悬停放大效果 */
}
.logo-container .app-name {
  font-size: 52rpx;
  font-weight: bold;
  color: #4B5563;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1);
  /* 文字阴影 */
}
.logo-container .app-desc {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6B7280;
}
.form-container {
  background-color: rgba(255, 255, 255, 0.8);
  /* 半透明背景 */
  border-radius: 30rpx;
  padding: 60rpx 50rpx;
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 80rpx;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  /* 过渡效果 */
}
.form-container:hover {
  -webkit-transform: translateY(-10rpx);
          transform: translateY(-10rpx);
  /* 悬停上移效果 */
}
.form-container .input-group {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #F3F4F6;
}
.form-container .input-group .input-icon {
  width: 60rpx;
  text-align: center;
  color: #9CA3AF;
  font-size: 36rpx;
  margin-right: 20rpx;
}
.form-container .input-group .input-field {
  flex: 1;
  font-size: 32rpx;
  height: 60rpx;
  color: #374151;
  border: none;
  background-color: transparent;
  outline: none;
}
.form-container .input-group .input-field::-webkit-input-placeholder {
  color: #D1D5DB;
}
.form-container .input-group .input-field::placeholder {
  color: #D1D5DB;
}
.form-container .form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40rpx;
}
.form-container .form-actions .remember-me {
  display: flex;
  align-items: center;
}
.form-container .form-actions .remember-me checkbox {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
.form-container .form-actions .remember-me text {
  font-size: 28rpx;
  color: #6B7280;
  margin-left: 10rpx;
}
.form-container .form-actions .forgot-password {
  font-size: 28rpx;
  color: #4F46E5;
  text-decoration: underline;
  /* 下划线效果 */
}
.form-container .form-actions .forgot-password:hover {
  color: #3730a3;
  /* 悬停颜色变化 */
}
.form-container .login-btn {
  margin-top: 60rpx;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient(90deg, #4F46E5 0%, #6366F1 100%);
  color: #FFFFFF;
  font-size: 36rpx;
  border-radius: 48rpx;
  font-weight: 500;
  box-shadow: 0 10rpx 20rpx rgba(79, 70, 229, 0.2);
  transition: all 0.3s;
}
.form-container .login-btn:hover {
  background: linear-gradient(90deg, #3730a3 0%, #4338ca 100%);
  /* 悬停背景渐变 */
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
  /* 悬停放大效果 */
}
.form-container .login-btn[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
}
.form-container .register-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  font-size: 28rpx;
}
.form-container .register-container text {
  color: #6B7280;
}
.form-container .register-container .register-link {
  color: #4F46E5;
  margin-left: 10rpx;
  text-decoration: underline;
  /* 下划线效果 */
}
.form-container .register-container .register-link:hover {
  color: #3730a3;
  /* 悬停颜色变化 */
}
.form-container .tips {
  margin-top: 30rpx;
  text-align: center;
}
.form-container .tips-text {
  color: #EF4444;
  font-size: 28rpx;
}
.placeholder-style {
  color: #D1D5DB;
  font-size: 32rpx;
}

