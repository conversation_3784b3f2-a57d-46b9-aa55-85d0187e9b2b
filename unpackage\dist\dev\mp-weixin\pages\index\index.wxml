<view class="container"><view class="status-bar" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="header-wrap"><my-title vue-id="8dd740cc-1" bind:__l="__l"></my-title><view class="user-actions"><view data-event-opts="{{[['tap',[['handleSearchClick',['$event']]]]]}}" class="action-btn search-btn" bindtap="__e"><text class="iconfont icon-search"></text></view><view data-event-opts="{{[['tap',[['handleMessageClick',['$event']]]]]}}" class="action-btn message-btn" bindtap="__e"><text class="iconfont icon-message"></text><block wx:if="{{unreadCount>0}}"><view class="badge">{{unreadCount>99?'99+':unreadCount}}</view></block></view><view data-event-opts="{{[['tap',[['handleUserClick',['$event']]]]]}}" class="user-avatar-wrap" bindtap="__e"><image class="user-avatar" src="{{userAvatar}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleAvatarError',['$event']]]]]}}" binderror="__e"></image><view class="avatar-border"></view></view></view></view><view class="nav-wrap"><scroll-view class="nav" scroll-x="{{true}}" show-scrollbar="false" scroll-left="{{navScrollLeft}}"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['handleNavClick',[index]]]]]}}" class="{{['nav-item',[(index===currentIndexNav)?'active':'']]}}" bindtap="__e"><text class="nav-text">{{item.text}}</text><block wx:if="{{index===currentIndexNav}}"><view class="nav-indicator"></view></block></view></block></scroll-view></view><block wx:if="{{showSwiper}}"><view class="slides"><block wx:if="{{swiperList.length>0}}"><swiper class="main-swiper" autoplay="{{true}}" indicator-dots="{{false}}" circular="{{true}}" interval="{{4000}}" duration="{{800}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{swiperList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view data-event-opts="{{[['tap',[['handleSwiperClick',['$0'],[[['swiperList','',index]]]]]]]}}" class="swiper-item-wrap" bindtap="__e"><image class="swiper-image" src="{{item.imgSrc}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageError',['$event']]]]]}}" binderror="__e"></image><view class="swiper-overlay"><view class="swiper-content"><text class="swiper-title">{{item.title}}</text><text class="swiper-desc">{{item.desc}}</text></view></view></view></swiper-item></block></swiper></block><view class="custom-indicators"><block wx:for="{{swiperList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['indicator-dot',[(index===currentSwiperIndex)?'active':'']]}}"></view></block></view></view></block><view class="quick-actions"><view data-event-opts="{{[['tap',[['handleQuickAction',['trending']]]]]}}" class="quick-item" bindtap="__e"><view class="quick-icon trending"><text class="iconfont icon-fire"></text></view><text class="quick-text">热门</text></view><view data-event-opts="{{[['tap',[['handleQuickAction',['live']]]]]}}" class="quick-item" bindtap="__e"><view class="quick-icon live"><text class="iconfont icon-live"></text></view><text class="quick-text">直播</text></view><view data-event-opts="{{[['tap',[['handleQuickAction',['nearby']]]]]}}" class="quick-item" bindtap="__e"><view class="quick-icon nearby"><text class="iconfont icon-location"></text></view><text class="quick-text">附近</text></view><view data-event-opts="{{[['tap',[['handleQuickAction',['upload']]]]]}}" class="quick-item" bindtap="__e"><view class="quick-icon upload"><text class="iconfont icon-plus"></text></view><text class="quick-text">发布</text></view></view><view class="video-section"><view class="section-header"><text class="section-title">推荐视频</text><view class="view-mode-switch"><view data-event-opts="{{[['tap',[['switchViewMode',['grid']]]]]}}" class="{{['mode-btn',[(viewMode==='grid')?'active':'']]}}" bindtap="__e"><text class="iconfont icon-grid"></text></view><view data-event-opts="{{[['tap',[['switchViewMode',['list']]]]]}}" class="{{['mode-btn',[(viewMode==='list')?'active':'']]}}" bindtap="__e"><text class="iconfont icon-list"></text></view></view></view><view class="{{['video-wrap',viewMode]}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['handleVideoClick',['$0'],[[['videosList','id',item.$orig.id,'id']]]]]],['longpress',[['handleVideoLongPress',['$0',index],[[['videosList','id',item.$orig.id]]]]]]]}}" class="{{['video-item',[(viewMode==='list')?'list-mode':'']]}}" bindtap="__e" bindlongpress="__e"><view class="video-img"><image class="video-thumbnail" src="{{item.$orig.imgSrc}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageError',['$event']]]]]}}" binderror="__e"></image><view class="play-overlay"><view class="play-btn"><text class="iconfont icon-play"></text></view></view><view class="video-duration"><text>{{item.m0}}</text></view><view class="video-stats"><view class="stat-item"><text class="iconfont icon-play-circle"></text><text class="stat-text">{{item.m1}}</text></view></view></view><view class="video-content"><view class="video-title">{{item.$orig.desc}}</view><view class="video-meta"><view class="author-info"><image class="author-avatar" src="{{item.$orig.authorAvatar}}"></image><text class="author-name">{{item.$orig.author}}</text></view><view class="video-actions"><view data-event-opts="{{[['tap',[['handleLike',['$0',index],[[['videosList','id',item.$orig.id]]]]]]]}}" class="action-item" catchtap="__e"><text class="{{['iconfont',item.$orig.isLiked?'icon-heart-fill':'icon-heart']}}"></text><text class="action-count">{{item.m2}}</text></view><view data-event-opts="{{[['tap',[['handleShare',['$0'],[[['videosList','id',item.$orig.id]]]]]]]}}" class="action-item" catchtap="__e"><text class="iconfont icon-share"></text></view></view></view></view></view></block></view><block wx:if="{{videosList.length===0&&!isLoading}}"><view class="empty-state"><view class="empty-icon"><text class="iconfont icon-video-off"></text></view><text class="empty-text">暂无视频内容</text><view data-event-opts="{{[['tap',[['refreshData',['$event']]]]]}}" class="empty-btn" bindtap="__e"><text>刷新试试</text></view></view></block></view><block wx:if="{{isLoading}}"><view class="loading-more"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view></block><block wx:if="{{showBackTop}}"><view data-event-opts="{{[['tap',[['scrollToTop',['$event']]]]]}}" class="back-top-btn" bindtap="__e"><text class="iconfont icon-arrow-up"></text></view></block><view class="safe-area-bottom"></view></view>