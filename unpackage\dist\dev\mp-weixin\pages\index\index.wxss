@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}
.status-bar {
  background: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);
}
.header-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);
  position: relative;
}
.header-wrap::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}
.user-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.action-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.action-btn .iconfont {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.9);
}
.action-btn:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  background: rgba(255, 255, 255, 0.25);
}
.action-btn .badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: 600;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  line-height: 1;
  padding: 0 8rpx;
}
.user-avatar-wrap {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.user-avatar-wrap .user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}
.user-avatar-wrap .avatar-border {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  z-index: -1;
}
.user-avatar-wrap:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.nav-wrap {
  background: #ffffff;
  padding: 24rpx 0 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav {
  white-space: nowrap;
  padding: 0 30rpx;
}
.nav .nav-item {
  display: inline-block;
  padding: 16rpx 0;
  margin-right: 48rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.nav .nav-item .nav-text {
  font-size: 32rpx;
  color: #7f8c8d;
  font-weight: 500;
  transition: all 0.3s ease;
}
.nav .nav-item .nav-indicator {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b 0%, #45b7d1 100%);
  border-radius: 3rpx;
  -webkit-animation: slideIn 0.3s ease;
          animation: slideIn 0.3s ease;
}
.nav .nav-item.active .nav-text {
  color: #ff6b6b;
  font-weight: 600;
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
@-webkit-keyframes slideIn {
from {
    width: 0;
    opacity: 0;
}
to {
    width: 40rpx;
    opacity: 1;
}
}
@keyframes slideIn {
from {
    width: 0;
    opacity: 0;
}
to {
    width: 40rpx;
    opacity: 1;
}
}
.slides {
  margin: 24rpx 30rpx 32rpx;
  position: relative;
}
.slides .main-swiper {
  height: 360rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.slides .swiper-item-wrap {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.slides .swiper-image {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.slides .swiper-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 32rpx 32rpx;
}
.slides .swiper-content .swiper-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
.slides .swiper-content .swiper-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}
.slides .custom-indicators {
  position: absolute;
  bottom: 20rpx;
  right: 32rpx;
  display: flex;
  gap: 12rpx;
}
.slides .custom-indicators .indicator-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}
.slides .custom-indicators .indicator-dot.active {
  background: white;
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
}
.loading-placeholder {
  height: 360rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 24rpx;
}
.loading-placeholder .loading-animation {
  display: flex;
  gap: 8rpx;
  margin-bottom: 24rpx;
}
.loading-placeholder .loading-animation .loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff6b6b;
  -webkit-animation: bounce 1.4s ease-in-out infinite both;
          animation: bounce 1.4s ease-in-out infinite both;
}
.loading-placeholder .loading-animation .loading-dot:nth-child(1) {
  -webkit-animation-delay: -0.32s;
          animation-delay: -0.32s;
}
.loading-placeholder .loading-animation .loading-dot:nth-child(2) {
  -webkit-animation-delay: -0.16s;
          animation-delay: -0.16s;
}
.loading-placeholder .loading-animation .loading-dot:nth-child(3) {
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.loading-placeholder .loading-text {
  font-size: 28rpx;
  color: #7f8c8d;
}
@-webkit-keyframes bounce {
0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes bounce {
0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 30rpx;
  background: #ffffff;
  margin: 0 30rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.quick-actions .quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.quick-actions .quick-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.quick-actions .quick-item .quick-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.quick-actions .quick-item .quick-icon .iconfont {
  font-size: 40rpx;
  color: white;
}
.quick-actions .quick-item .quick-icon.trending {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
}
.quick-actions .quick-item .quick-icon.live {
  background: linear-gradient(135deg, #4ecdc4 0%, #6ee5dd 100%);
}
.quick-actions .quick-item .quick-icon.nearby {
  background: linear-gradient(135deg, #45b7d1 0%, #6cc5e0 100%);
}
.quick-actions .quick-item .quick-icon.upload {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.quick-actions .quick-item .quick-text {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
}
.video-section {
  padding: 0 30rpx;
}
.video-section .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.video-section .section-header .section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
}
.video-section .section-header .view-mode-switch {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 4rpx;
}
.video-section .section-header .view-mode-switch .mode-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.video-section .section-header .view-mode-switch .mode-btn .iconfont {
  font-size: 28rpx;
  color: #7f8c8d;
}
.video-section .section-header .view-mode-switch .mode-btn.active {
  background: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.video-section .section-header .view-mode-switch .mode-btn.active .iconfont {
  color: #ff6b6b;
}
.video-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.video-wrap.grid .video-item {
  width: calc(50% - 12rpx);
}
.video-wrap.list {
  flex-direction: column;
}
.video-wrap.list .video-item {
  width: 100%;
}
.video-wrap.list .video-item.list-mode {
  flex-direction: row;
}
.video-wrap.list .video-item.list-mode .video-img {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.video-wrap.list .video-item.list-mode .video-content {
  flex: 1;
  padding: 16rpx 0 16rpx 20rpx;
}
.video-item {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}
.video-item:active {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.video-img {
  position: relative;
  height: 240rpx;
  overflow: hidden;
}
.video-img .video-thumbnail {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.video-img .play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
}
.video-img .play-overlay .play-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  transition: all 0.3s ease;
}
.video-img .play-overlay .play-btn .iconfont {
  font-size: 36rpx;
  color: #ff6b6b;
  margin-left: 4rpx;
}
.video-img .video-duration {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 500;
}
.video-img .video-stats {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
}
.video-img .video-stats .stat-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  border-radius: 12rpx;
  gap: 4rpx;
}
.video-img .video-stats .stat-item .iconfont {
  font-size: 20rpx;
}
.video-img .video-stats .stat-item .stat-text {
  font-weight: 500;
}
.video-content {
  padding: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.video-content .video-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.video-content .video-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}
.video-content .video-meta .author-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  min-width: 0;
}
.video-content .video-meta .author-info .author-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #e9ecef;
}
.video-content .video-meta .author-info .author-name {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video-content .video-meta .video-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.video-content .video-meta .video-actions .action-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}
.video-content .video-meta .video-actions .action-item .iconfont {
  font-size: 28rpx;
  color: #7f8c8d;
}
.video-content .video-meta .video-actions .action-item .iconfont.icon-heart-fill {
  color: #ff6b6b;
}
.video-content .video-meta .video-actions .action-item .action-count {
  font-size: 22rpx;
  color: #7f8c8d;
  font-weight: 500;
}
.video-content .video-meta .video-actions .action-item:active {
  background: #f8f9fa;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.video-item:hover .video-img .play-overlay {
  opacity: 1;
}
.video-item:hover .video-img .play-overlay .play-btn {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.video-item:hover .video-thumbnail {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-state .empty-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}
.empty-state .empty-icon .iconfont {
  font-size: 60rpx;
  color: #7f8c8d;
}
.empty-state .empty-text {
  font-size: 32rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
}
.empty-state .empty-btn {
  padding: 16rpx 32rpx;
  background: #ff6b6b;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.empty-state .empty-btn:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: #ff3838;
}
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;
}
.loading-more .loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f8f9fa;
  border-top: 3rpx solid #ff6b6b;
  border-radius: 50%;
  -webkit-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
}
.loading-more .loading-text {
  font-size: 28rpx;
  color: #7f8c8d;
}
@-webkit-keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.back-top-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-animation: breathe 2s ease-in-out infinite;
          animation: breathe 2s ease-in-out infinite;
}
.back-top-btn .iconfont {
  font-size: 32rpx;
  color: white;
}
.back-top-btn:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
@-webkit-keyframes breathe {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
@keyframes breathe {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background: #f8f9fa;
}
@media screen and (max-width: 750rpx) {
.video-wrap.grid .video-item {
    width: 100%;
}
.quick-actions .quick-item .quick-icon {
    width: 72rpx;
    height: 72rpx;
}
.quick-actions .quick-item .quick-icon .iconfont {
    font-size: 32rpx;
}
.quick-actions .quick-text {
    font-size: 22rpx;
}
.slides .main-swiper {
    height: 280rpx;
}
.nav .nav-item {
    margin-right: 32rpx;
}
.nav .nav-item .nav-text {
    font-size: 28rpx;
}
}
@media (prefers-color-scheme: dark) {
.container {
    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
}
.video-item {
    background: #2d2d2d;
}
.video-item .video-title {
    color: #ffffff;
}
.video-item .author-name {
    color: #cccccc;
}
.nav-wrap {
    background: #2d2d2d;
}
.quick-actions {
    background: #2d2d2d;
}
}
.container {
  scroll-behavior: smooth;
}
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

