














/*每个页面公共css */
/* 字体图标样式 - 使用Unicode字符模拟图标效果 */
.iconfont {
  font-family: "iconfont", Arial, sans-serif !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 搜索图标 */
.icon-search:before {
  content: "🔍";
}
/* 消息图标 */
.icon-message:before {
  content: "💬";
}
/* 播放图标 */
.icon-play:before {
  content: "▶";
}
/* 播放圆圈图标 */
.icon-play-circle:before {
  content: "⏯";
}
/* 火焰图标 */
.icon-fire:before {
  content: "🔥";
}
/* 直播图标 */
.icon-live:before {
  content: "📺";
}
/* 位置图标 */
.icon-location:before {
  content: "📍";
}
/* 加号图标 */
.icon-plus:before {
  content: "➕";
}
/* 网格图标 */
.icon-grid:before {
  content: "⊞";
}
/* 列表图标 */
.icon-list:before {
  content: "☰";
}
/* 心形图标 */
.icon-heart:before {
  content: "♡";
}
/* 实心心形图标 */
.icon-heart-fill:before {
  content: "♥";
}
/* 分享图标 */
.icon-share:before {
  content: "📤";
}
/* 视频关闭图标 */
.icon-video-off:before {
  content: "📹";
}
/* 向上箭头图标 */
.icon-arrow-up:before {
  content: "↑";
}
/* 向左箭头图标 */
.icon-arrow-left:before {
  content: "←";
}
/* 向右箭头图标 */
.icon-arrow-right:before {
  content: "→";
}
/* 设置图标 */
.icon-cog:before {
  content: "⚙";
}
/* 用户图标 */
.icon-user:before {
  content: "👤";
}
/* 更多图标 */
.icon-more:before {
  content: "⋯";
}
/* 收藏图标 */
.icon-star:before {
  content: "☆";
}
/* 实心收藏图标 */
.icon-star-fill:before {
  content: "★";
}
/* 评论图标 */
.icon-comment:before {
  content: "💭";
}
/* 下载图标 */
.icon-download:before {
  content: "⬇";
}
/* 上传图标 */
.icon-upload:before {
  content: "⬆";
}
/* 刷新图标 */
.icon-refresh:before {
  content: "🔄";
}
/* 关闭图标 */
.icon-close:before {
  content: "✕";
}
/* 检查图标 */
.icon-check:before {
  content: "✓";
}
/* 警告图标 */
.icon-warning:before {
  content: "⚠";
}
/* 信息图标 */
.icon-info:before {
  content: "ℹ";
}
/* 错误图标 */
.icon-error:before {
  content: "✗";
}
/* 成功图标 */
.icon-success:before {
  content: "✓";
}
/* 眼睛图标 */
.icon-eye:before {
  content: "👁";
}
/* 隐藏眼睛图标 */
.icon-eye-off:before {
  content: "🙈";
}
/* 锁定图标 */
.icon-lock:before {
  content: "🔒";
}
/* 解锁图标 */
.icon-unlock:before {
  content: "🔓";
}
/* 时间图标 */
.icon-time:before {
  content: "⏰";
}
/* 日历图标 */
.icon-calendar:before {
  content: "📅";
}
/* 邮件图标 */
.icon-mail:before {
  content: "✉";
}
/* 电话图标 */
.icon-phone:before {
  content: "📞";
}
/* 相机图标 */
.icon-camera:before {
  content: "📷";
}
/* 图片图标 */
.icon-image:before {
  content: "🖼";
}
/* 文件图标 */
.icon-file:before {
  content: "📄";
}
/* 文件夹图标 */
.icon-folder:before {
  content: "📁";
}
/* 链接图标 */
.icon-link:before {
  content: "🔗";
}
/* 标签图标 */
.icon-tag:before {
  content: "🏷";
}
/* 礼物图标 */
.icon-gift:before {
  content: "🎁";
}
/* 音乐图标 */
.icon-music:before {
  content: "🎵";
}
/* 音量图标 */
.icon-volume:before {
  content: "🔊";
}
/* 静音图标 */
.icon-mute:before {
  content: "🔇";
}
/* WiFi图标 */
.icon-wifi:before {
  content: "📶";
}
/* 电池图标 */
.icon-battery:before {
  content: "🔋";
}
/* 蓝牙图标 */
.icon-bluetooth:before {
  content: "📶";
}
/* GPS图标 */
.icon-gps:before {
  content: "🧭";
}
/* 全局样式重置 */
page {
	background-color: #f8f9fa;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
	line-height: 1.6;
}
/* 通用类 */
.container {
	padding: 20rpx;
}
.text-center {
	text-align: center;
}
.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.flex-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.flex-column {
	display: flex;
	flex-direction: column;
}
/* 文字省略 */
.text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.text-ellipsis-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
/* 动画类 */
.fade-in {
	-webkit-animation: fadeIn 0.3s ease-in-out;
	        animation: fadeIn 0.3s ease-in-out;
}
@-webkit-keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.slide-up {
	-webkit-animation: slideUp 0.3s ease-out;
	        animation: slideUp 0.3s ease-out;
}
@-webkit-keyframes slideUp {
from {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes slideUp {
from {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
/* 按钮样式 */
.btn {
	padding: 16rpx 32rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
	text-align: center;
	transition: all 0.2s ease;
	border: none;
	outline: none;
}
.btn-primary {
	background: linear-gradient(135deg, #ff6b6b 0%, #45b7d1 100%);
	color: white;
}
.btn-secondary {
	background: #f8f9fa;
	color: #6c757d;
	border: 1rpx solid #dee2e6;
}
.btn:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
/* 卡片样式 */
.card {
	background: white;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	overflow: hidden;
}
/* 分割线 */
.divider {
	height: 1rpx;
	background: #e9ecef;
	margin: 20rpx 0;
}
/* 安全区域 */
.safe-area-top {
	height: constant(safe-area-inset-top);
	height: env(safe-area-inset-top);
}
.safe-area-bottom {
	height: constant(safe-area-inset-bottom);
	height: env(safe-area-inset-bottom);
}

