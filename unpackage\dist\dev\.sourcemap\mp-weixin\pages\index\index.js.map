{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/index/index.vue?509d", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/index/index.vue?10f0", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/index/index.vue?6efc", "uni-app:///pages/index/index.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/index/index.vue?e29b", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/index/index.vue?1598"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;kDAAA;;;AAGA;AACA,4F,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,cAAD,CAAV,C;;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAkrB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgPtsB,4F;;AAEA;AACA,kBADA;AAEA,MAFA,kBAEA;AACA;AACA;AACA,wBAFA;;AAIA;AACA;AACA,2BADA;AAEA,2BAFA;AAGA,2BAHA;AAIA,2BAJA;AAKA,2BALA;AAMA,2BANA;AAOA,2BAPA;AAQA,2BARA,CALA;;AAeA,wBAfA;AAgBA,sBAhBA;;AAkBA;AACA;AACA;AACA,aADA;AAEA,uCAFA;AAGA,uBAHA;AAIA,wBAJA,EADA;;AAOA;AACA,aADA;AAEA,sCAFA;AAGA,sBAHA;AAIA,yBAJA,EAPA;;AAaA;AACA,aADA;AAEA,uCAFA;AAGA,qBAHA;AAIA,yBAJA,EAbA,CAnBA;;;AAuCA,2BAvCA;AAwCA,sBAxCA;;AA0CA;AACA;AACA;AACA,aADA;AAEA,uCAFA;AAGA,sCAHA;AAIA,wBAJA;AAKA,uBALA;AAMA,qBANA;AAOA,wBAPA;AAQA,4CARA;AASA,sBATA,EADA;;AAYA;AACA,aADA;AAEA,uCAFA;AAGA,iCAHA;AAIA,uBAJA;AAKA,sBALA;AAMA,qBANA;AAOA,uBAPA;AAQA,8CARA;AASA,qBATA,EAZA;;AAuBA;AACA,aADA;AAEA,uCAFA;AAGA,iCAHA;AAIA,wBAJA;AAKA,uBALA;AAMA,qBANA;AAOA,qBAPA;AAQA,4CARA;AASA,sBATA,EAvBA;;AAkCA;AACA,aADA;AAEA,sCAFA;AAGA,+BAHA;AAIA,wBAJA;AAKA,uBALA;AAMA,qBANA;AAOA,uBAPA;AAQA,6CARA;AASA,sBATA,EAlCA,CA3CA;;;;AA0FA;AACA,sBA3FA;AA4FA,sBA5FA,EA4FA;AACA,wBA7FA;AA8FA,oBA9FA;;AAgGA;AACA,0DAjGA;AAkGA,2CAlGA;;AAoGA,GAvGA;;AAyGA,QAzGA,oBAyGA;AACA;AACA;AACA,GA5GA;;AA8GA,QA9GA,oBA8GA;AACA;AACA;AACA,GAjHA;;AAmHA,cAnHA,wBAmHA,CAnHA,EAmHA;AACA;AACA;AACA,GAtHA;;AAwHA;AACA;AACA,iBAFA,2BAEA;AACA;AACA;AACA,KALA;;AAOA;AACA,gBARA,0BAQA;AACA,uCADA;;;AAIA,qFAJA;;;;;;;;AAYA;AACA,8CAbA;;AAeA,wCAfA;;AAiBA,KAzBA;;AA2BA;AACA,uBA5BA,iCA4BA;AACA;AACA;AACA,KA/BA;;AAiCA;AACA,eAlCA,yBAkCA;AACA;AACA,KApCA;;AAsCA;AACA,kBAvCA,0BAuCA,CAvCA,EAuCA;AACA;AACA,KAzCA;;AA2CA;AACA,qBA5CA,6BA4CA,IA5CA,EA4CA;AACA;AACA;AACA,KA/CA;;AAiDA;AACA,qBAlDA,+BAkDA;AACA;AACA,mCADA;;AAGA,KAtDA;;AAwDA;AACA,sBAzDA,gCAyDA;AACA;AACA,qCADA;;AAGA;AACA;AACA,KA/DA;;AAiEA;AACA,qBAlEA,6BAkEA,IAlEA,EAkEA;AACA;AACA;AACA;AACA;AACA,SAJA;AAKA;AACA;AACA,mCADA;;AAGA,SATA;AAUA;AACA;AACA,uCADA;;AAGA,SAdA;AAeA;AACA;AACA,uCADA;;AAGA,SAnBA;;;AAsBA;AACA;AACA;AACA,KA5FA;;AA8FA;AACA,kBA/FA,0BA+FA,IA/FA,EA+FA;AACA;AACA;AACA,KAlGA;;AAoGA;AACA,kBArGA,0BAqGA,KArGA,EAqGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KA7GA;;AA+GA;AACA,sBAhHA,8BAgHA,KAhHA,EAgHA;AACA,0BADA,CACA;AACA,+BAFA,CAEA;AACA;AACA;AACA,KArHA;;AAuHA;AACA,eAxHA,uBAwHA,KAxHA,EAwHA;AACA;AACA;AACA;AACA,KA5HA;;AA8HA;AACA,sBA/HA,8BA+HA,aA/HA,EA+HA;AACA;AACA;AACA,KAlIA;;AAoIA;AACA,oBArIA,4BAqIA,EArIA,EAqIA;AACA;AACA;AACA,kDADA;;AAGA,KA1IA;;AA4IA;AACA,wBA7IA,gCA6IA,IA7IA,EA6IA,KA7IA,EA6IA;AACA;AACA,4CADA;AAEA;AACA;AACA;AACA,SALA;;AAOA,KArJA;;AAuJA;AACA,qBAxJA,6BAwJA,MAxJA,EAwJA,IAxJA,EAwJA,KAxJA,EAwJA;AACA;AACA,uEADA;AAEA,kEAFA;AAGA,oEAHA;AAIA,+FAJA;;;AAOA;AACA;AACA;AACA,KAnKA;;AAqKA;AACA,mBAtKA,6BAsKA;AACA;AACA,+BADA;;AAGA,KA1KA;;AA4KA;AACA,cA7KA,sBA6KA,IA7KA,EA6KA,KA7KA,EA6KA;AACA;AACA;AACA;AACA;AACA,OAHA,MAGA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAzLA;;AA2LA;AACA,eA5LA,uBA4LA,IA5LA,EA4LA;AACA;AACA,uDADA;AAEA;AACA;AACA;AACA,SALA;;AAOA,KApMA;;AAsMA;AACA,mBAvMA,2BAuMA,QAvMA,EAuMA,IAvMA,EAuMA;AACA;AACA;AACA,KA1MA;;AA4MA;AACA,gBA7MA,wBA6MA,IA7MA,EA6MA;AACA;AACA,KA/MA;;AAiNA;AACA,eAlNA,uBAkNA,IAlNA,EAkNA;AACA;AACA,KApNA;;AAsNA;AACA,qBAvNA,6BAuNA,IAvNA,EAuNA,KAvNA,EAuNA;AACA;AACA;AACA,KA1NA;;AA4NA;AACA,eA7NA,yBA6NA;AACA;AACA,oBADA;AAEA,qBAFA;;AAIA,KAlOA;;AAoOA;AACA,oBArOA,4BAqOA,CArOA,EAqOA;AACA;AACA,KAvOA;;AAyOA;AACA,qBA1OA,+BA0OA;AACA;AACA,KA5OA;;AA8OA;AACA,mBA/OA,2BA+OA,KA/OA,EA+OA;AACA;AACA,KAjPA;;AAmPA;AACA,eApPA,uBAoPA,KApPA,EAoPA;AACA;AACA;AACA;AACA;AACA;AACA,KA1PA;;AA4PA;AACA,kBA7PA,0BA6PA,OA7PA,EA6PA;AACA;AACA;AACA;AACA;AACA,KAlQA;;AAoQA;AACA,aArQA,qBAqQA,KArQA,EAqQA;AACA;AACA,oBADA;AAEA,kBAFA;AAGA,sBAHA;;AAKA,KA3QA,EAxHA,E;;;;;;;;;;;;;AClPA;AAAA;AAAA;AAAA;AAA6xC,CAAgB,wrCAAG,EAAC,C;;;;;;;;;;;ACAjzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 状态栏占位 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\n    <!-- 顶部导航栏 -->\n    <view class=\"header-wrap\">\n      <MyTitle />\n      <!-- 右侧操作区域 -->\n      <view class=\"user-actions\">\n        <!-- 搜索按钮 -->\n        <view class=\"action-btn search-btn\" @click=\"handleSearchClick\">\n          <text class=\"iconfont icon-search\"></text>\n        </view>\n\n        <!-- 消息按钮 -->\n        <view class=\"action-btn message-btn\" @click=\"handleMessageClick\">\n          <text class=\"iconfont icon-message\"></text>\n          <view v-if=\"unreadCount > 0\" class=\"badge\">{{ unreadCount > 99 ? '99+' : unreadCount }}</view>\n        </view>\n\n        <!-- 用户头像 -->\n        <view class=\"user-avatar-wrap\" @click=\"handleUserClick\">\n          <image\n            :src=\"userAvatar\"\n            mode=\"aspectFill\"\n            class=\"user-avatar\"\n            @error=\"handleAvatarError\"\n            :lazy-load=\"true\"\n          />\n          <view class=\"avatar-border\"></view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分类导航栏 -->\n    <view class=\"nav-wrap\">\n      <scroll-view class=\"nav\" scroll-x show-scrollbar=\"false\" :scroll-left=\"navScrollLeft\">\n        <view\n          v-for=\"(item, index) in navList\"\n          :key=\"item.id\"\n          @click=\"handleNavClick(index)\"\n          :class=\"['nav-item', { active: index === currentIndexNav }]\"\n        >\n          <text class=\"nav-text\">{{ item.text }}</text>\n          <view v-if=\"index === currentIndexNav\" class=\"nav-indicator\"></view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 轮播图区域 -->\n    <view class=\"slides\" v-if=\"showSwiper\">\n      <swiper\n        v-if=\"swiperList.length > 0\"\n        :autoplay=\"true\"\n        :indicator-dots=\"false\"\n        :circular=\"true\"\n        :interval=\"4000\"\n        :duration=\"800\"\n        @change=\"onSwiperChange\"\n        class=\"main-swiper\"\n      >\n        <swiper-item v-for=\"(item, index) in swiperList\" :key=\"index\">\n          <view class=\"swiper-item-wrap\" @click=\"handleSwiperClick(item)\">\n            <image\n              :src=\"item.imgSrc\"\n              mode=\"aspectFill\"\n              :lazy-load=\"true\"\n              @error=\"handleImageError\"\n              class=\"swiper-image\"\n            />\n            <view class=\"swiper-overlay\">\n              <view class=\"swiper-content\">\n                <text class=\"swiper-title\">{{ item.title }}</text>\n                <text class=\"swiper-desc\">{{ item.desc }}</text>\n              </view>\n            </view>\n          </view>\n        </swiper-item>\n      </swiper>\n\n      <!-- 自定义指示器 -->\n      <view class=\"custom-indicators\">\n        <view\n          v-for=\"(item, index) in swiperList\"\n          :key=\"index\"\n          :class=\"['indicator-dot', { active: index === currentSwiperIndex }]\"\n        ></view>\n      </view>\n\n      <view v-else class=\"loading-placeholder\">\n        <view class=\"loading-animation\">\n          <view class=\"loading-dot\"></view>\n          <view class=\"loading-dot\"></view>\n          <view class=\"loading-dot\"></view>\n        </view>\n        <text class=\"loading-text\">精彩内容加载中...</text>\n      </view>\n    </view>\n\n    <!-- 快捷功能区 -->\n    <view class=\"quick-actions\">\n      <view class=\"quick-item\" @click=\"handleQuickAction('trending')\">\n        <view class=\"quick-icon trending\">\n          <text class=\"iconfont icon-fire\"></text>\n        </view>\n        <text class=\"quick-text\">热门</text>\n      </view>\n      <view class=\"quick-item\" @click=\"handleQuickAction('live')\">\n        <view class=\"quick-icon live\">\n          <text class=\"iconfont icon-live\"></text>\n        </view>\n        <text class=\"quick-text\">直播</text>\n      </view>\n      <view class=\"quick-item\" @click=\"handleQuickAction('nearby')\">\n        <view class=\"quick-icon nearby\">\n          <text class=\"iconfont icon-location\"></text>\n        </view>\n        <text class=\"quick-text\">附近</text>\n      </view>\n      <view class=\"quick-item\" @click=\"handleQuickAction('upload')\">\n        <view class=\"quick-icon upload\">\n          <text class=\"iconfont icon-plus\"></text>\n        </view>\n        <text class=\"quick-text\">发布</text>\n      </view>\n    </view>\n\n    <!-- 视频列表区域 -->\n    <view class=\"video-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">推荐视频</text>\n        <view class=\"view-mode-switch\">\n          <view\n            :class=\"['mode-btn', { active: viewMode === 'grid' }]\"\n            @click=\"switchViewMode('grid')\"\n          >\n            <text class=\"iconfont icon-grid\"></text>\n          </view>\n          <view\n            :class=\"['mode-btn', { active: viewMode === 'list' }]\"\n            @click=\"switchViewMode('list')\"\n          >\n            <text class=\"iconfont icon-list\"></text>\n          </view>\n        </view>\n      </view>\n\n      <view :class=\"['video-wrap', viewMode]\">\n        <view\n          v-for=\"(item, index) in videosList\"\n          :key=\"item.id\"\n          :class=\"['video-item', { 'list-mode': viewMode === 'list' }]\"\n          @click=\"handleVideoClick(item.id)\"\n          @longpress=\"handleVideoLongPress(item, index)\"\n        >\n          <view class=\"video-img\">\n            <image\n              :src=\"item.imgSrc\"\n              mode=\"aspectFill\"\n              :lazy-load=\"true\"\n              @error=\"handleImageError\"\n              class=\"video-thumbnail\"\n            />\n\n            <!-- 播放按钮 -->\n            <view class=\"play-overlay\">\n              <view class=\"play-btn\">\n                <text class=\"iconfont icon-play\"></text>\n              </view>\n            </view>\n\n            <!-- 视频时长 -->\n            <view class=\"video-duration\">\n              <text>{{ formatDuration(item.duration) }}</text>\n            </view>\n\n            <!-- 播放量信息 -->\n            <view class=\"video-stats\">\n              <view class=\"stat-item\">\n                <text class=\"iconfont icon-play-circle\"></text>\n                <text class=\"stat-text\">{{ formatPlayCount(item.playCount) }}</text>\n              </view>\n            </view>\n          </view>\n\n          <view class=\"video-content\">\n            <view class=\"video-title\">{{ item.desc }}</view>\n            <view class=\"video-meta\">\n              <view class=\"author-info\">\n                <image :src=\"item.authorAvatar\" class=\"author-avatar\" />\n                <text class=\"author-name\">{{ item.author }}</text>\n              </view>\n              <view class=\"video-actions\">\n                <view class=\"action-item\" @click.stop=\"handleLike(item, index)\">\n                  <text :class=\"['iconfont', item.isLiked ? 'icon-heart-fill' : 'icon-heart']\"></text>\n                  <text class=\"action-count\">{{ formatCount(item.likeCount) }}</text>\n                </view>\n                <view class=\"action-item\" @click.stop=\"handleShare(item)\">\n                  <text class=\"iconfont icon-share\"></text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"videosList.length === 0 && !isLoading\" class=\"empty-state\">\n        <view class=\"empty-icon\">\n          <text class=\"iconfont icon-video-off\"></text>\n        </view>\n        <text class=\"empty-text\">暂无视频内容</text>\n        <view class=\"empty-btn\" @click=\"refreshData\">\n          <text>刷新试试</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 加载更多 -->\n    <view v-if=\"isLoading\" class=\"loading-more\">\n      <view class=\"loading-spinner\"></view>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 回到顶部按钮 -->\n    <view\n      v-if=\"showBackTop\"\n      class=\"back-top-btn\"\n      @click=\"scrollToTop\"\n    >\n      <text class=\"iconfont icon-arrow-up\"></text>\n    </view>\n\n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script>\nimport config from '../../common/config.js'\n\nexport default {\n  name: 'HomePage',\n  data() {\n    return {\n      // 系统信息\n      statusBarHeight: 0,\n\n      // 导航相关\n      navList: [\n        { id: 1, text: '推荐' },\n        { id: 2, text: '热门' },\n        { id: 3, text: '关注' },\n        { id: 4, text: '音乐' },\n        { id: 5, text: '舞蹈' },\n        { id: 6, text: '美食' },\n        { id: 7, text: '游戏' },\n        { id: 8, text: '搞笑' }\n      ],\n      currentIndexNav: 0,\n      navScrollLeft: 0,\n\n      // 轮播图相关\n      swiperList: [\n        {\n          id: 1,\n          imgSrc: '../../static/ddy.jpeg',\n          title: '精彩视频推荐',\n          desc: '发现更多有趣内容'\n        },\n        {\n          id: 2,\n          imgSrc: '../../static/tx.jpeg',\n          title: '热门创作者',\n          desc: '关注你喜欢的UP主'\n        },\n        {\n          id: 3,\n          imgSrc: '../../static/lyx.jpeg',\n          title: '今日热点',\n          desc: '不容错过的精彩瞬间'\n        }\n      ],\n      currentSwiperIndex: 0,\n      showSwiper: true,\n\n      // 视频列表相关\n      videosList: [\n        {\n          id: 1,\n          imgSrc: '../../static/ddy.jpeg',\n          desc: '超好看的舞蹈视频，学会了你就是全场最靓的仔！',\n          playCount: 12500,\n          likeCount: 1200,\n          duration: 180,\n          author: '舞蹈达人小美',\n          authorAvatar: '../../static/tx.jpeg',\n          isLiked: false\n        },\n        {\n          id: 2,\n          imgSrc: '../../static/lyx.jpeg',\n          desc: '美食制作教程，简单易学的家常菜做法',\n          playCount: 8900,\n          likeCount: 890,\n          duration: 240,\n          author: '美食小当家',\n          authorAvatar: '../../static/oynn.jpeg',\n          isLiked: true\n        },\n        {\n          id: 3,\n          imgSrc: '../../static/whd.jpeg',\n          desc: '搞笑日常，笑到肚子疼的沙雕视频合集',\n          playCount: 25600,\n          likeCount: 3200,\n          duration: 120,\n          author: '搞笑王',\n          authorAvatar: '../../static/zlh.jpg',\n          isLiked: false\n        },\n        {\n          id: 4,\n          imgSrc: '../../static/txw.jpg',\n          desc: '旅行vlog，带你看遍世界美景',\n          playCount: 15800,\n          likeCount: 1580,\n          duration: 300,\n          author: '旅行者小王',\n          authorAvatar: '../../static/user.jpg',\n          isLiked: false\n        }\n      ],\n\n      // UI状态\n      isLoading: false,\n      viewMode: 'grid', // grid 或 list\n      showBackTop: false,\n      unreadCount: 3,\n\n      // 用户信息\n      userAvatar: '../../static/mmexport1749721684379.jpg',\n      defaultImage: '../../static/logo.png'\n    }\n  },\n\n  onLoad() {\n    this.getSystemInfo()\n    this.initPageData()\n  },\n\n  onShow() {\n    // 页面显示时可以刷新某些数据\n    this.checkUnreadMessages()\n  },\n\n  onPageScroll(e) {\n    // 控制回到顶部按钮显示\n    this.showBackTop = e.scrollTop > 500\n  },\n  \n  methods: {\n    // 获取系统信息\n    getSystemInfo() {\n      const systemInfo = uni.getSystemInfoSync()\n      this.statusBarHeight = systemInfo.statusBarHeight || 0\n    },\n\n    // 初始化页面数据\n    async initPageData() {\n      this.isLoading = true\n      try {\n        // 模拟API请求延迟\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        // 实际项目中这里会调用真实的API\n        // await Promise.all([\n        //   this.getNavList(),\n        //   this.getSwiperList(),\n        //   this.getVideosList()\n        // ])\n      } catch (error) {\n        console.error('页面数据加载失败:', error)\n        this.showToast('数据加载失败，请重试')\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    // 检查未读消息\n    checkUnreadMessages() {\n      // 模拟检查未读消息\n      // 实际项目中这里会调用API\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.initPageData()\n    },\n\n    // 轮播图切换\n    onSwiperChange(e) {\n      this.currentSwiperIndex = e.detail.current\n    },\n\n    // 轮播图点击\n    handleSwiperClick(item) {\n      console.log('轮播图点击:', item)\n      // 可以跳转到相应页面\n    },\n\n    // 搜索按钮点击\n    handleSearchClick() {\n      uni.navigateTo({\n        url: '/pages/search/search'\n      })\n    },\n\n    // 消息按钮点击\n    handleMessageClick() {\n      uni.navigateTo({\n        url: '/pages/message/message'\n      })\n      // 清除未读数量\n      this.unreadCount = 0\n    },\n\n    // 快捷功能点击\n    handleQuickAction(type) {\n      const actions = {\n        trending: () => {\n          this.currentIndexNav = 1\n          this.handleNavClick(1)\n        },\n        live: () => {\n          uni.navigateTo({\n            url: '/pages/live/live'\n          })\n        },\n        nearby: () => {\n          uni.navigateTo({\n            url: '/pages/nearby/nearby'\n          })\n        },\n        upload: () => {\n          uni.navigateTo({\n            url: '/pages/upload/upload'\n          })\n        }\n      }\n\n      if (actions[type]) {\n        actions[type]()\n      }\n    },\n\n    // 切换视图模式\n    switchViewMode(mode) {\n      this.viewMode = mode\n      uni.setStorageSync('viewMode', mode)\n    },\n\n    // 导航点击事件\n    handleNavClick(index) {\n      if (this.currentIndexNav !== index) {\n        this.currentIndexNav = index\n        this.onNavChange(index)\n\n        // 计算导航滚动位置\n        this.calculateNavScroll(index)\n      }\n    },\n\n    // 计算导航滚动位置\n    calculateNavScroll(index) {\n      const itemWidth = 120 // 估算每个导航项宽度\n      const containerWidth = 750 // 容器宽度\n      const scrollLeft = Math.max(0, (index * itemWidth) - (containerWidth / 2))\n      this.navScrollLeft = scrollLeft\n    },\n\n    // 导航切换处理\n    onNavChange(index) {\n      console.log('导航切换到:', index)\n      // 根据导航索引加载对应内容\n      this.loadCategoryVideos(index)\n    },\n\n    // 加载分类视频\n    loadCategoryVideos(categoryIndex) {\n      // 模拟根据分类加载不同视频\n      console.log('加载分类视频:', categoryIndex)\n    },\n\n    // 视频点击事件\n    handleVideoClick(id) {\n      if (!id) return\n      uni.navigateTo({\n        url: `/pages/detail/detail?id=${id}`\n      })\n    },\n\n    // 视频长按事件\n    handleVideoLongPress(item, index) {\n      uni.showActionSheet({\n        itemList: ['收藏', '分享', '举报', '不感兴趣'],\n        success: (res) => {\n          const actions = ['collect', 'share', 'report', 'notInterested']\n          this.handleVideoAction(actions[res.tapIndex], item, index)\n        }\n      })\n    },\n\n    // 视频操作处理\n    handleVideoAction(action, item, index) {\n      const actionMap = {\n        collect: () => this.collectVideo(item),\n        share: () => this.handleShare(item),\n        report: () => this.reportVideo(item),\n        notInterested: () => this.markNotInterested(item, index)\n      }\n\n      if (actionMap[action]) {\n        actionMap[action]()\n      }\n    },\n\n    // 用户头像点击\n    handleUserClick() {\n      uni.navigateTo({\n        url: '/pages/user/user'\n      })\n    },\n\n    // 点赞操作\n    handleLike(item, index) {\n      item.isLiked = !item.isLiked\n      if (item.isLiked) {\n        item.likeCount++\n        this.showToast('点赞成功', 'success')\n      } else {\n        item.likeCount--\n        this.showToast('取消点赞', 'none')\n      }\n\n      // 更新视频列表\n      this.$set(this.videosList, index, item)\n    },\n\n    // 分享操作\n    handleShare(item) {\n      uni.showActionSheet({\n        itemList: ['微信好友', '朋友圈', 'QQ好友', '微博', '复制链接'],\n        success: (res) => {\n          const platforms = ['wechat', 'moments', 'qq', 'weibo', 'copy']\n          this.shareToplatform(platforms[res.tapIndex], item)\n        }\n      })\n    },\n\n    // 分享到平台\n    shareToplatform(platform, item) {\n      console.log(`分享到${platform}:`, item)\n      this.showToast('分享成功', 'success')\n    },\n\n    // 收藏视频\n    collectVideo(item) {\n      this.showToast('收藏成功', 'success')\n    },\n\n    // 举报视频\n    reportVideo(item) {\n      this.showToast('举报成功', 'success')\n    },\n\n    // 标记不感兴趣\n    markNotInterested(item, index) {\n      this.videosList.splice(index, 1)\n      this.showToast('已标记为不感兴趣', 'none')\n    },\n\n    // 回到顶部\n    scrollToTop() {\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 300\n      })\n    },\n\n    // 图片加载错误处理\n    handleImageError(e) {\n      e.target.src = this.defaultImage\n    },\n\n    // 头像加载错误处理\n    handleAvatarError() {\n      this.userAvatar = this.defaultImage\n    },\n\n    // 格式化播放次数\n    formatPlayCount(count) {\n      return this.formatCount(count)\n    },\n\n    // 格式化数量\n    formatCount(count) {\n      if (!count) return '0'\n      if (count < 1000) return count.toString()\n      if (count < 10000) return (count / 1000).toFixed(1) + 'k'\n      if (count < 100000000) return (count / 10000).toFixed(1) + 'w'\n      return (count / 100000000).toFixed(1) + '亿'\n    },\n\n    // 格式化时长\n    formatDuration(seconds) {\n      if (!seconds) return '00:00'\n      const minutes = Math.floor(seconds / 60)\n      const remainingSeconds = seconds % 60\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`\n    },\n\n    // 显示提示信息\n    showToast(title, icon = 'none') {\n      uni.showToast({\n        title,\n        icon,\n        duration: 2000\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n// 全局变量\n$primary-color: #ff6b6b;\n$secondary-color: #4ecdc4;\n$accent-color: #45b7d1;\n$text-primary: #2c3e50;\n$text-secondary: #7f8c8d;\n$bg-primary: #ffffff;\n$bg-secondary: #f8f9fa;\n$border-color: #e9ecef;\n$shadow-light: 0 2rpx 12rpx rgba(0,0,0,0.04);\n$shadow-medium: 0 4rpx 20rpx rgba(0,0,0,0.08);\n$shadow-heavy: 0 8rpx 32rpx rgba(0,0,0,0.12);\n\n.container {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n// 状态栏占位\n.status-bar {\n  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);\n}\n\n// 顶部导航栏\n.header-wrap {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 1rpx;\n    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);\n  }\n}\n\n.user-actions {\n  display: flex;\n  align-items: center;\n  gap: 24rpx;\n}\n\n// 操作按钮样式\n.action-btn {\n  width: 72rpx;\n  height: 72rpx;\n  border-radius: 50%;\n  background: rgba(255,255,255,0.15);\n  backdrop-filter: blur(10rpx);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n  .iconfont {\n    font-size: 36rpx;\n    color: rgba(255,255,255,0.9);\n  }\n\n  &:active {\n    transform: scale(0.9);\n    background: rgba(255,255,255,0.25);\n  }\n\n  // 消息徽章\n  .badge {\n    position: absolute;\n    top: -6rpx;\n    right: -6rpx;\n    min-width: 32rpx;\n    height: 32rpx;\n    background: #ff4757;\n    border-radius: 16rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 20rpx;\n    color: white;\n    font-weight: 600;\n    border: 3rpx solid rgba(255,255,255,0.8);\n    line-height: 1;\n    padding: 0 8rpx;\n  }\n}\n\n// 用户头像\n.user-avatar-wrap {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n  .user-avatar {\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    border: 3rpx solid rgba(255,255,255,0.8);\n  }\n\n  .avatar-border {\n    position: absolute;\n    top: -4rpx;\n    left: -4rpx;\n    right: -4rpx;\n    bottom: -4rpx;\n    border-radius: 50%;\n    background: linear-gradient(45deg, rgba(255,255,255,0.6), rgba(255,255,255,0.2));\n    z-index: -1;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n// 分类导航栏\n.nav-wrap {\n  background: $bg-primary;\n  padding: 24rpx 0 16rpx;\n  box-shadow: $shadow-light;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.nav {\n  white-space: nowrap;\n  padding: 0 30rpx;\n\n  .nav-item {\n    display: inline-block;\n    padding: 16rpx 0;\n    margin-right: 48rpx;\n    position: relative;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    .nav-text {\n      font-size: 32rpx;\n      color: $text-secondary;\n      font-weight: 500;\n      transition: all 0.3s ease;\n    }\n\n    .nav-indicator {\n      position: absolute;\n      bottom: -8rpx;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40rpx;\n      height: 6rpx;\n      background: linear-gradient(90deg, $primary-color 0%, $accent-color 100%);\n      border-radius: 3rpx;\n      animation: slideIn 0.3s ease;\n    }\n\n    &.active .nav-text {\n      color: $primary-color;\n      font-weight: 600;\n      transform: scale(1.05);\n    }\n  }\n}\n\n@keyframes slideIn {\n  from {\n    width: 0;\n    opacity: 0;\n  }\n  to {\n    width: 40rpx;\n    opacity: 1;\n  }\n}\n\n// 轮播图区域\n.slides {\n  margin: 24rpx 30rpx 32rpx;\n  position: relative;\n\n  .main-swiper {\n    height: 360rpx;\n    border-radius: 24rpx;\n    overflow: hidden;\n    box-shadow: $shadow-medium;\n  }\n\n  .swiper-item-wrap {\n    position: relative;\n    height: 100%;\n    overflow: hidden;\n  }\n\n  .swiper-image {\n    width: 100%;\n    height: 100%;\n    transition: transform 0.3s ease;\n  }\n\n  .swiper-overlay {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\n    padding: 40rpx 32rpx 32rpx;\n  }\n\n  .swiper-content {\n    .swiper-title {\n      display: block;\n      font-size: 36rpx;\n      font-weight: 600;\n      color: white;\n      margin-bottom: 8rpx;\n      text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);\n    }\n\n    .swiper-desc {\n      display: block;\n      font-size: 28rpx;\n      color: rgba(255,255,255,0.8);\n      text-shadow: 0 1rpx 4rpx rgba(0,0,0,0.3);\n    }\n  }\n\n  // 自定义指示器\n  .custom-indicators {\n    position: absolute;\n    bottom: 20rpx;\n    right: 32rpx;\n    display: flex;\n    gap: 12rpx;\n\n    .indicator-dot {\n      width: 12rpx;\n      height: 12rpx;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.4);\n      transition: all 0.3s ease;\n\n      &.active {\n        background: white;\n        transform: scale(1.2);\n      }\n    }\n  }\n}\n\n// 加载占位符\n.loading-placeholder {\n  height: 360rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: $bg-secondary;\n  border-radius: 24rpx;\n\n  .loading-animation {\n    display: flex;\n    gap: 8rpx;\n    margin-bottom: 24rpx;\n\n    .loading-dot {\n      width: 12rpx;\n      height: 12rpx;\n      border-radius: 50%;\n      background: $primary-color;\n      animation: bounce 1.4s ease-in-out infinite both;\n\n      &:nth-child(1) { animation-delay: -0.32s; }\n      &:nth-child(2) { animation-delay: -0.16s; }\n      &:nth-child(3) { animation-delay: 0s; }\n    }\n  }\n\n  .loading-text {\n    font-size: 28rpx;\n    color: $text-secondary;\n  }\n}\n\n@keyframes bounce {\n  0%, 80%, 100% {\n    transform: scale(0);\n  }\n  40% {\n    transform: scale(1);\n  }\n}\n\n// 快捷功能区\n.quick-actions {\n  display: flex;\n  justify-content: space-around;\n  padding: 32rpx 30rpx;\n  background: $bg-primary;\n  margin: 0 30rpx 24rpx;\n  border-radius: 24rpx;\n  box-shadow: $shadow-light;\n\n  .quick-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 12rpx;\n    transition: transform 0.2s ease;\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    .quick-icon {\n      width: 88rpx;\n      height: 88rpx;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n\n      .iconfont {\n        font-size: 40rpx;\n        color: white;\n      }\n\n      &.trending {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);\n      }\n\n      &.live {\n        background: linear-gradient(135deg, #4ecdc4 0%, #6ee5dd 100%);\n      }\n\n      &.nearby {\n        background: linear-gradient(135deg, #45b7d1 0%, #6cc5e0 100%);\n      }\n\n      &.upload {\n        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      }\n    }\n\n    .quick-text {\n      font-size: 24rpx;\n      color: $text-secondary;\n      font-weight: 500;\n    }\n  }\n}\n\n// 视频区域\n.video-section {\n  padding: 0 30rpx;\n\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 24rpx;\n\n    .section-title {\n      font-size: 36rpx;\n      font-weight: 600;\n      color: $text-primary;\n    }\n\n    .view-mode-switch {\n      display: flex;\n      background: $bg-secondary;\n      border-radius: 12rpx;\n      padding: 4rpx;\n\n      .mode-btn {\n        width: 56rpx;\n        height: 56rpx;\n        border-radius: 8rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.2s ease;\n\n        .iconfont {\n          font-size: 28rpx;\n          color: $text-secondary;\n        }\n\n        &.active {\n          background: $bg-primary;\n          box-shadow: $shadow-light;\n\n          .iconfont {\n            color: $primary-color;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 视频列表\n.video-wrap {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24rpx;\n\n  &.grid {\n    .video-item {\n      width: calc(50% - 12rpx);\n    }\n  }\n\n  &.list {\n    flex-direction: column;\n\n    .video-item {\n      width: 100%;\n\n      &.list-mode {\n        flex-direction: row;\n\n        .video-img {\n          width: 240rpx;\n          height: 180rpx;\n          flex-shrink: 0;\n        }\n\n        .video-content {\n          flex: 1;\n          padding: 16rpx 0 16rpx 20rpx;\n        }\n      }\n    }\n  }\n}\n\n.video-item {\n  background: $bg-primary;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: $shadow-light;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  flex-direction: column;\n\n  &:active {\n    transform: translateY(-4rpx);\n    box-shadow: $shadow-medium;\n  }\n}\n\n// 视频图片区域\n.video-img {\n  position: relative;\n  height: 240rpx;\n  overflow: hidden;\n\n  .video-thumbnail {\n    width: 100%;\n    height: 100%;\n    transition: transform 0.3s ease;\n  }\n\n  // 播放按钮覆盖层\n  .play-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: rgba(0,0,0,0.1);\n    opacity: 0;\n    transition: all 0.3s ease;\n\n    .play-btn {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.9);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transform: scale(0.8);\n      transition: all 0.3s ease;\n\n      .iconfont {\n        font-size: 36rpx;\n        color: $primary-color;\n        margin-left: 4rpx;\n      }\n    }\n  }\n\n  // 视频时长\n  .video-duration {\n    position: absolute;\n    bottom: 12rpx;\n    right: 12rpx;\n    background: rgba(0,0,0,0.7);\n    color: white;\n    font-size: 22rpx;\n    padding: 4rpx 8rpx;\n    border-radius: 6rpx;\n    font-weight: 500;\n  }\n\n  // 播放统计\n  .video-stats {\n    position: absolute;\n    top: 12rpx;\n    right: 12rpx;\n\n    .stat-item {\n      display: flex;\n      align-items: center;\n      background: rgba(0,0,0,0.6);\n      color: white;\n      font-size: 22rpx;\n      padding: 6rpx 10rpx;\n      border-radius: 12rpx;\n      gap: 4rpx;\n\n      .iconfont {\n        font-size: 20rpx;\n      }\n\n      .stat-text {\n        font-weight: 500;\n      }\n    }\n  }\n}\n\n// 视频内容区域\n.video-content {\n  padding: 20rpx;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n\n  .video-title {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: $text-primary;\n    line-height: 1.4;\n    margin-bottom: 16rpx;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n\n  .video-meta {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-top: auto;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      gap: 12rpx;\n      flex: 1;\n      min-width: 0;\n\n      .author-avatar {\n        width: 48rpx;\n        height: 48rpx;\n        border-radius: 50%;\n        border: 2rpx solid $border-color;\n      }\n\n      .author-name {\n        font-size: 24rpx;\n        color: $text-secondary;\n        font-weight: 500;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .video-actions {\n      display: flex;\n      align-items: center;\n      gap: 20rpx;\n\n      .action-item {\n        display: flex;\n        align-items: center;\n        gap: 6rpx;\n        padding: 8rpx;\n        border-radius: 8rpx;\n        transition: all 0.2s ease;\n\n        .iconfont {\n          font-size: 28rpx;\n          color: $text-secondary;\n\n          &.icon-heart-fill {\n            color: $primary-color;\n          }\n        }\n\n        .action-count {\n          font-size: 22rpx;\n          color: $text-secondary;\n          font-weight: 500;\n        }\n\n        &:active {\n          background: $bg-secondary;\n          transform: scale(0.95);\n        }\n      }\n    }\n  }\n}\n\n// 悬停效果\n.video-item:hover {\n  .video-img .play-overlay {\n    opacity: 1;\n\n    .play-btn {\n      transform: scale(1);\n    }\n  }\n\n  .video-thumbnail {\n    transform: scale(1.05);\n  }\n}\n\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 0;\n\n  .empty-icon {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 50%;\n    background: $bg-secondary;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 32rpx;\n\n    .iconfont {\n      font-size: 60rpx;\n      color: $text-secondary;\n    }\n  }\n\n  .empty-text {\n    font-size: 32rpx;\n    color: $text-secondary;\n    margin-bottom: 32rpx;\n  }\n\n  .empty-btn {\n    padding: 16rpx 32rpx;\n    background: $primary-color;\n    color: white;\n    border-radius: 24rpx;\n    font-size: 28rpx;\n    font-weight: 500;\n    transition: all 0.2s ease;\n\n    &:active {\n      transform: scale(0.95);\n      background: darken($primary-color, 10%);\n    }\n  }\n}\n\n// 加载更多\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n  gap: 16rpx;\n\n  .loading-spinner {\n    width: 32rpx;\n    height: 32rpx;\n    border: 3rpx solid $bg-secondary;\n    border-top: 3rpx solid $primary-color;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n\n  .loading-text {\n    font-size: 28rpx;\n    color: $text-secondary;\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n// 回到顶部按钮\n.back-top-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 120rpx;\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 50%;\n  background: linear-gradient(135deg, $primary-color 0%, $accent-color 100%);\n  box-shadow: $shadow-medium;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n  .iconfont {\n    font-size: 32rpx;\n    color: white;\n  }\n\n  &:active {\n    transform: scale(0.9);\n  }\n\n  // 添加呼吸动画\n  animation: breathe 2s ease-in-out infinite;\n}\n\n@keyframes breathe {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n// 底部安全区域\n.safe-area-bottom {\n  height: constant(safe-area-inset-bottom);\n  height: env(safe-area-inset-bottom);\n  background: $bg-secondary;\n}\n\n// 响应式适配\n@media screen and (max-width: 750rpx) {\n  .video-wrap.grid .video-item {\n    width: 100%;\n  }\n\n  .quick-actions {\n    .quick-item .quick-icon {\n      width: 72rpx;\n      height: 72rpx;\n\n      .iconfont {\n        font-size: 32rpx;\n      }\n    }\n\n    .quick-text {\n      font-size: 22rpx;\n    }\n  }\n\n  .slides .main-swiper {\n    height: 280rpx;\n  }\n\n  .nav .nav-item {\n    margin-right: 32rpx;\n\n    .nav-text {\n      font-size: 28rpx;\n    }\n  }\n}\n\n// 深色模式适配\n@media (prefers-color-scheme: dark) {\n  .container {\n    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);\n  }\n\n  .video-item {\n    background: #2d2d2d;\n\n    .video-title {\n      color: #ffffff;\n    }\n\n    .author-name {\n      color: #cccccc;\n    }\n  }\n\n  .nav-wrap {\n    background: #2d2d2d;\n  }\n\n  .quick-actions {\n    background: #2d2d2d;\n  }\n}\n\n// 动画优化\n* {\n  -webkit-tap-highlight-color: transparent;\n}\n\n// 滚动优化\n.container {\n  scroll-behavior: smooth;\n}\n\n// 字体图标样式（如果使用iconfont）\n.iconfont {\n  font-family: \"iconfont\" !important;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753864315094\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}