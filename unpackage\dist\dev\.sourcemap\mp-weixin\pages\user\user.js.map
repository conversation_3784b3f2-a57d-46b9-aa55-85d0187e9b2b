{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?d99b", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?b09c", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?00b4", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?8058", "uni-app:///pages/user/user.vue", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?a6ab", "webpack:///C:/Users/<USER>/Desktop/短视频平台/pages/user/user.vue?7d5d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;kDAAA;;;AAGA;AACA,yF,8FAHA;AACAA,EAAE,CAACC,iCAAH,GAAuCC,mBAAvC,CAGAC,UAAU,CAACC,aAAD,CAAV,C;;;;;;;;;;;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACmL;AACnL,gBAAgB,uLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAirB,CAAgB,gqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyErsB;AACA,MADA,kBACA;AACA;AACA;AACA;AACA,kBADA;AAEA,gDAFA;AAGA,+BAHA;AAIA,sBAJA;AAKA;AACA,oBADA;AAEA,wBAFA;AAGA,oBAHA;AAIA,uBAJA,EALA,EAFA;;;;AAeA,GAjBA;AAkBA,QAlBA,oBAkBA;AACA;AACA;AACA,GArBA;AAsBA;AACA;AACA,UAFA,oBAEA;AACA;AACA,gBADA;;AAGA,KANA;AAOA;AACA,gBARA,0BAQA;AACA;AACA,yCADA;;AAGA,KAZA;AAaA;AACA,iBAdA,2BAcA;AACA;AACA,yCADA;;AAGA,KAlBA;AAmBA;AACA,YApBA,sBAoBA;AACA;AACA,+BADA;;AAGA,KAxBA;AAyBA;AACA,iBA1BA,2BA0BA;AACA;AACA,yCADA;;AAGA,KA9BA;AA+BA;AACA,eAhCA,yBAgCA;AACA;AACA,qCADA;;AAGA,KApCA;AAqCA;AACA,YAtCA,sBAsCA;AACA;AACA,+BADA;;AAGA,KA1CA;AA2CA;AACA,UA5CA,oBA4CA;AACA;AACA,mBADA;AAEA,4BAFA;AAGA;AACA;AACA;AACA;AACA,uCADA;;AAGA;AACA,SAVA;;AAYA,KAzDA;AA0DA;AACA,iBA3DA,2BA2DA;AACA;AACA,wCADA;AAEA;AACA;AACA,SAJA;;AAMA,KAlEA,EAtBA,E;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAA4xC,CAAgB,urCAAG,EAAC,C;;;;;;;;;;;ACAhzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/user.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/user.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user.vue?vue&type=template&id=80842834&\"\nvar renderjs\nimport script from \"./user.vue?vue&type=script&lang=js&\"\nexport * from \"./user.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/user.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--16-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=template&id=80842834&\"", "var components\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"user-detail-page\">\n\t\t<!-- 顶部背景区域 -->\n\t\t<view class=\"user-header-bg\">\n\t\t\t<!-- 返回按钮 -->\n\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t<text class=\"fa fa-arrow-left\"></text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 用户信息卡片 -->\n\t\t<view class=\"user-card\">\n\t\t\t<!-- 头像区域 -->\n\t\t\t<view class=\"avatar-wrap\">\n\t\t\t\t<image \n\t\t\t\t\tsrc=\"../../static/tx.jpeg\"\n\t\t\t\t\tmode=\"widthFix\" \n\t\t\t\t\tclass=\"avatar-img\"\n\t\t\t\t/>\n\t\t\t\t<!-- 编辑头像按钮 -->\n\t\t\t\t<view class=\"edit-avatar-btn\">\n\t\t\t\t\t<text class=\"fa fa-pencil\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 基本信息 -->\n\t\t\t<view class=\"user-basic\">\n\t\t\t\t<view class=\"user-name\">小杰</view>\n\t\t\t\t<view class=\"user-sign\">热爱生活的视频创作者 | 已加入2年</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 数据统计 -->\n\t\t\t<view class=\"user-stats\">\n\t\t\t\t<view class=\"stat-item\" @click=\"goToMyVideos\">\n\t\t\t\t\t<view class=\"stat-num\">24</view>\n\t\t\t\t\t<view class=\"stat-label\">我的视频</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\" @click=\"goToFavorites\">\n\t\t\t\t\t<view class=\"stat-num\">156</view>\n\t\t\t\t\t<view class=\"stat-label\">收藏</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\" @click=\"goToFans\">\n\t\t\t\t\t<view class=\"stat-num\">3.2k</view>\n\t\t\t\t\t<view class=\"stat-label\">粉丝</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\" @click=\"goToFollowing\">\n\t\t\t\t\t<view class=\"stat-num\">89</view>\n\t\t\t\t\t<view class=\"stat-label\">关注</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"user-menu\">\n\t\t\t<view class=\"menu-item\" @click=\"goToSetting\">\n\t\t\t\t<text class=\"menu-icon fa fa-cog\"></text>\n\t\t\t\t<text class=\"menu-text\">设置</text>\n\t\t\t\t<text class=\"menu-arrow fa fa-angle-right\"></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"goToHelp\">\n\t\t\t\t<text class=\"menu-icon fa fa-question-circle\"></text>\n\t\t\t\t<text class=\"menu-text\">帮助中心</text>\n\t\t\t\t<text class=\"menu-arrow fa fa-angle-right\"></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"logout\">\n\t\t\t\t<text class=\"menu-icon fa fa-sign-out\"></text>\n\t\t\t\t<text class=\"menu-text\" style=\"color: #ff4d4f;\">退出登录</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 可在此处存储用户数据，实际项目中可从接口获取\n\t\t\t\tuserInfo: {\n\t\t\t\t\tname: '小杰',\n\t\t\t\t\tavatar: '/static/images/user-avatar.png',\n\t\t\t\t\tsignature: '热爱生活的视频创作者',\n\t\t\t\t\tjoinTime: '2年',\n\t\t\t\t\tstats: {\n\t\t\t\t\t\tvideos: 24,\n\t\t\t\t\t\tfavorites: 156,\n\t\t\t\t\t\tfans: 3200,\n\t\t\t\t\t\tfollowing: 89\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\t// 实际项目中可在此处请求用户详情接口\n\t\t\t// this.getUserDetail()\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转我的视频\n\t\t\tgoToMyVideos() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/my-videos/my-videos'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转收藏\n\t\t\tgoToFavorites() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/favorites/favorites'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转粉丝列表\n\t\t\tgoToFans() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/fans/fans'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转关注列表\n\t\t\tgoToFollowing() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/following/following'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转设置页面\n\t\t\tgoToSetting() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/setting/setting'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 跳转帮助中心\n\t\t\tgoToHelp() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/help/help'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 退出登录\n\t\t\tlogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 执行退出登录逻辑\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取用户详情（示例接口方法）\n\t\t\tgetUserDetail() {\n\t\t\t\tuni.request({\n\t\t\t\t\turl: config.url + '/user/detail',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tthis.userInfo = res.data.data\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.user-detail-page {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tfont-family: -apple-system, BlinkMacSystemFont, sans-serif;\n\t}\n\t\n\t// 顶部背景\n\t.user-header-bg {\n\t\theight: 220rpx;\n\t\tbackground: linear-gradient(135deg, #4a90e2, #5c6bc0);\n\t\tposition: relative;\n\t\t\n\t\t.back-btn {\n\t\t\tposition: absolute;\n\t\t\tleft: 30rpx;\n\t\t\ttop: 60rpx;\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tborder-radius: 50%;\n\t\t\tbackground-color: rgba(0,0,0,0.2);\n\t\t\tcolor: #fff;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 30rpx;\n\t\t}\n\t}\n\t\n\t// 用户卡片\n\t.user-card {\n\t\twidth: 90%;\n\t\tmargin: -80rpx auto 0;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx;\n\t\tbox-shadow: 0 5rpx 20rpx rgba(0,0,0,0.1);\n\t\tpadding: 40rpx 0 30rpx;\n\t\ttext-align: center;\n\t\t\n\t\t// 头像区域\n\t\t.avatar-wrap {\n\t\t\tposition: relative;\n\t\t\twidth: 160rpx;\n\t\t\theight: 160rpx;\n\t\t\tmargin: 0 auto 30rpx;\n\t\t\t\n\t\t\t.avatar-img {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tborder: 5rpx solid #fff;\n\t\t\t}\n\t\t\t\n\t\t\t.edit-avatar-btn {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tbackground-color: #4a90e2;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tcolor: #fff;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tborder: 2rpx solid #fff;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 基本信息\n\t\t.user-basic {\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t\n\t\t\t.user-name {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.user-sign {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tline-height: 1.5;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 数据统计\n\t\t.user-stats {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-around;\n\t\t\tpadding: 0 20rpx;\n\t\t\t\n\t\t\t.stat-item {\n\t\t\t\tflex: 1;\n\t\t\t\tpadding: 10rpx 0;\n\t\t\t\t\n\t\t\t\t.stat-num {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tmargin-bottom: 5rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.stat-label {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t// 功能菜单\n\t.user-menu {\n\t\twidth: 90%;\n\t\tmargin: 30rpx auto;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx;\n\t\tbox-shadow: 0 5rpx 20rpx rgba(0,0,0,0.1);\n\t\t\n\t\t.menu-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 30rpx 30rpx;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-icon {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #4a90e2;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t\twidth: 32rpx; // 固定宽度避免文字偏移\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-text {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\t\n\t\t\t.menu-arrow {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #ccc;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753861137177\n      var cssReload = require(\"C:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}